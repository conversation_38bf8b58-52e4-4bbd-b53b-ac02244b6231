from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import jwt, JWTError

from app.core.config import settings
from app.models.user import User
from app.api.schemas.user import UserResponse
import json
from pydantic import BaseModel
from typing import Optional
from app.api.schemas.role import InsetRole
from app.api.repository.user_report_usage import check_user_usage_limit
from app.api.repository.user_report_usage import get_count_data

from app.core.redis_client import get_redis_client
from typing import cast
from fastapi import Request


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/login")

class UserInfo(BaseModel):
    username: str
    organization_code: Optional[str] = None
    class Config:
        from_attribute= True
# 这是为了有代码提示
def get_current_user_from_state(request: Request) -> UserResponse:
    return cast(UserResponse, request.state.current_user)

async def auth_token_data(
    token: str
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    if not token:
        raise credentials_exception
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_info = UserInfo.model_validate(json.loads(payload.get("sub")))
        if user_info.username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return token
async def get_current_user(
    request: Request,
    token: str = Depends(oauth2_scheme)
) -> UserResponse:
    """通过JWT令牌获取当前用户，增加redis校验实现单点登录"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    user_info: Optional[UserInfo] = None
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        user_info = UserInfo.model_validate(json.loads(payload.get("sub")))
        print(f"user_info:{user_info}")
        if user_info.username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    if settings.IS_OPEN_SSO == 'ON':
        # 单点登录校验：redis 中的 token 必须与当前 token 匹配
        redis = get_redis_client()
        redis_key = f"sso:{user_info.username}"
        redis_token = await redis.get(redis_key)
        if not redis_token or redis_token != token:
            raise credentials_exception
    user = await User.filter(username=user_info.username, is_deleted=False).prefetch_related('role', 'organization').first()
    if not user:
        raise credentials_exception
    print(f"user: {user}")
    user_result = UserResponse.model_validate(user)
    if user_result.role.identifier not in [InsetRole.SUPER_ADMIN]:
        try: 
            user_result.is_could_create_report = await check_user_usage_limit(
                user_id=user.id
            )
        except Exception as e:
            user_result.is_could_create_report = False
        user_result.is_trial = user_result.organization.is_trial
    use_data = await get_count_data(
        user_id=user_result.id,
        organization_id=user_result.organization.id if user_result.organization else None,
        is_admin=user_result.role.identifier == InsetRole.ADMIN
    )
    user_result.used_count = use_data.used_count if use_data else 0
    user_result.max_allowed_count = use_data.max_allowed_count if use_data else 0
    # 将用户信息挂载到state
    if request:
        request.state.current_user = user_result
    return user_result

async def auth_super_admin_depend(
    request: Request
):
    current_user = get_current_user_from_state(request)
    ###非超级管理员的拦截###
    if not current_user or current_user.role.identifier != InsetRole.SUPER_ADMIN.value:
        raise HTTPException(status_code=403, detail="权限不足")
    return current_user
async def auth_org_admin_depend(request: Request):
    current_user = get_current_user_from_state(request)
    ###机构管理员的拦截###
    if not current_user or current_user.role.identifier != InsetRole.ADMIN.value:
        raise HTTPException(status_code=403, detail="权限不足")
    return current_user
async def auth_admin_or_super_admin(request: Request):
    current_user = get_current_user_from_state(request)
    ###机构管理员的拦截###
    if not current_user or current_user.role.identifier not in [InsetRole.ADMIN.value, InsetRole.SUPER_ADMIN.value]:
        raise HTTPException(status_code=403, detail="权限不足")
    return current_user
async def auth_not_trial_depend(
    current_user: UserResponse = Depends(get_current_user)  
):
    ###如果是体验用户则拦截###
    print(current_user.is_trial)
    if current_user and current_user.is_trial:
        raise HTTPException(status_code=403, detail="体验用户权限不足")
    return current_user