from app.services.research_service import (
    generate_search_queries,
    process_search_query,
    get_new_search_queries
)
from typing import Optional, Callable
from app.models.research import Research, ResearchStatus
from app.api.schemas.project_configs import ProjectConfigResponse2
from app.core.logging import get_logger
from app.api.schemas.project_members import ProjectMemberBase
from app.services import prompts
from app.core.config import settings
from app.api.repository.literatures import (
  get_literature_by_id,
  get_literature_length
)
from app.models.organization_model_use import UseCase
from app.api.repository.user_default_model import get_user_model
from app.api.repository.research import get_context_length
from app.services.prompts import (
  generate_project_config_prompt
)
from app.utils.llm_service import stream_llm_and_save
from app.utils.enum import ProjectReportError
from app.api.repository.dictionary import get_one_dict_by_category_and_value
from app.api.repository.project_url_summary import get_urls_by_project_id
from app.services.research_service import process_url
import asyncio
from app.api.schemas.user import UserResponse
from app.utils.enum import CallLLMFlag


logger = get_logger(__name__)


async def wrapper_error_callback(
  error_msg: str,
  error_callback: Optional[Callable[[str], None]] = None
):
  logger.error(error_msg)
  if error_callback:
      await error_callback(error_msg)
async def search_to_carry_info(
    research: Research,
    config_response: ProjectConfigResponse2,
    current_user: UserResponse,
    # 搜索关键词的引擎的标识符
    search_method_flag: Optional[str] = None,
    open_literature_summary = False,
):
    model_config = await get_user_model(
        current_user=current_user,
        use_case=UseCase.JUDGE_WEB_USEFUL.value
    )
    logger.info(f"材料{config_response.id}: 开始生成初始网络搜索查询关联词列表")
    # 获得初始的google查询关键词列表
    initial_keywords_list = await generate_search_queries(
        title=config_response.name,
        api_key=model_config.api_key,
        api_url=model_config.api_url,
        model=model_config.model_name,
        search_engine="google-scholar"
    )
    if not initial_keywords_list:
        error_msg = f"{ProjectReportError.NOT_KEY_WORDS.value}"
        await wrapper_error_callback(error_msg)
        raise Exception(error_msg)
  
    # 打印关键词列表
    queries_list = '\n'.join([f"- {query}" for query in initial_keywords_list])
    progress_msg = f"{ProjectReportError.KEY_WORDS_LIST.value}：\n\n{queries_list}\n\n"
    logger.info(progress_msg)

    # 已经迭代的次数
    iteration = 0
    # 最大迭代次数
    max_iterations = settings.RESEARCH_ITERATION_LIMIT
    # 添加最大搜索查询次数限制
    max_search_queries = settings.MAX_SEARCH_QUERIES  # 使用配置的最大搜索查询数
    max_contexts = settings.MAX_LITERATURE_AND_CONTEXTS if open_literature_summary else settings.MAX_CONTEXTS  # 使用配置的最大上下文数量
    # 已经处理的关键词个数
    total_processed_queries = 0
    # 所有的关键词列表
    keywords_list = initial_keywords_list.copy()
    # 关键词的计位数字
    keyword_index = 0
    while iteration < max_iterations:
        # 检查是否已处理所有查询或达到查询上限
        remaining_queries = keywords_list[keyword_index:]
        if not remaining_queries or total_processed_queries >= max_search_queries:
            done_msg = "✅ 所有查询已处理完毕或达到查询上限\n\n"
            logger.info(done_msg)
            break
            
        # 处理每个查询，但确保不超过最大查询次数
        for query in remaining_queries:
            # 检查是否达到查询上限
            if total_processed_queries >= max_search_queries:
                limit_msg = f"⚠️ 已达到查询上限({max_search_queries}个)，停止搜索\n\n"
                logger.info(limit_msg)
                # yield f"data: {json.dumps({'content': limit_msg, 'type': 'warning'})}\n\n".encode('utf-8')
                break
                
            search_msg = f"正在搜索关键词: \"{query}\"\n\n"
            logger.info(search_msg)
            # yield f"data: {json.dumps({'content': search_msg, 'type': 'status'})}\n\n".encode('utf-8')
            
            # 处理搜索查询
            search_engine_site_url = ""
            if search_method_flag:
                result = await get_one_dict_by_category_and_value(
                    category="参考文献库",
                    value=search_method_flag
                )
                if result:
                    search_engine_site_url = result.remark
            contexts = await process_search_query(
                title=config_response.name,
                query=query,
                research_id=str(research.id),
                api_key=model_config.api_key,
                api_url=model_config.api_url,
                model=model_config.model_name,
                is_summary_literature=open_literature_summary,
                search_method_flag=search_method_flag,
                search_engine_site_url=search_engine_site_url
            )
            logger.info(f"关键词：【{query}】的上下文收集完毕")
            keyword_index += 1
            # 增加迭代计数和处理查询计数
            research.iterations += 1
            research.contexts += contexts
            total_processed_queries += 1
            await research.save()
            
            # 如果开启了有用的context总结文献的话，
            # 就context的长度为有用的context加上文献条数，
            # 否则仅是有用的context长度
            reference_length = await get_literature_length(research_id=research.id)
            other_length = await get_context_length(research_id=research.id)
            context_length = len(research.contexts) if not open_literature_summary else reference_length + other_length
            
            # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
            if context_length >= max_contexts:  # 使用配置的最大上下文数量
                enough_msg = f"✅ 已收集足够的信息（{len(research.contexts)}条上下文）\n\n（318）"
                logger.info(enough_msg)
                # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            # 报告进度
            if contexts:
                success_msg = f"✅ 已找到 {len(contexts)} 条相关信息\n\n"
                logger.info(success_msg)
                # yield f"data: {json.dumps({'content': success_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                warning_msg = "⚠️ 未找到相关信息\n\n"
                logger.info(warning_msg)
                # yield f"data: {json.dumps({'content': warning_msg, 'type': 'warning'})}\n\n".encode('utf-8')
        # 如果开启了有用的context总结文献的话，
        # 就context的长度为有用的context加上文献条数，
        # 否则仅是有用的context长度
        reference_length = await get_literature_length(research_id=research.id)
        other_length = await get_context_length(research_id=research.id)
        context_length = len(research.contexts) if not open_literature_summary else reference_length + other_length
        
        # 检查上下文数量，如果已经收集足够多的上下文，则提前结束
        if context_length >= max_contexts:  # 使用配置的最大上下文数量
            enough_msg = f"✅ 已收集足够的信息：（{context_length}条背景信息（文献加context））max_contexts：{max_contexts}\n\n（334）"
            logger.info(enough_msg)
            # yield f"data: {json.dumps({'content': enough_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            break
        
        # 分析已收集的信息并获取新的搜索查询
        analyzing_msg = "正在分析已收集的信息...\n\n"
        logger.info(analyzing_msg)
        # yield f"data: {json.dumps({'content': analyzing_msg, 'type': 'status'})}\n\n".encode('utf-8')
        
        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        try:
            new_queries = await get_new_search_queries(
                research=research,
                api_key=model_config.api_key,
                api_url=model_config.api_url,
                model=model_config.model_name,
                max_contexts=max_contexts
            )
            
            # 如果不需要更多查询，退出循环
            if new_queries is None:
                complete_msg = "✅ 已收集足够的信息\n\n（357）"
                logger.info(complete_msg)
                # yield f"data: {json.dumps({'content': complete_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
            
            # 更新搜索查询并继续
            if new_queries and len(new_queries) > 0:
                # 限制新增查询数量，避免无限增长
                new_queries = new_queries[:5]  # 每轮最多添加5个新查询
                research.search_queries = research.search_queries + new_queries
                research.status = ResearchStatus.SEARCHING
                await research.save()
                
                new_queries_list = '\n'.join([f"- {query}" for query in new_queries])
                new_queries_msg = f"已生成新的搜索查询：\n\n{new_queries_list}\n\n"
                logger.info(new_queries_msg)
                # yield f"data: {json.dumps({'content': new_queries_msg, 'type': 'progress'})}\n\n".encode('utf-8')
            else:
                # 无新查询，退出循环
                done_msg = "✅ 搜索完成，未生成新的查询\n\n"
                logger.info(done_msg)
                # yield f"data: {json.dumps({'content': done_msg, 'type': 'progress'})}\n\n".encode('utf-8')
                break
        except Exception as e:
            error_msg = f"⚠️ 生成新查询时出错: {str(e)}\n\n"
            # yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n".encode('utf-8')
            logger.error(f"研究 {research.id}: 生成新查询时出错: {str(e)}")
            # 出错时也要继续，避免整个过程中断
            break
        
        iteration += 1
    
# 实现从研究报告流式生成内容的异步生成器函数
async def stream_research_report_content(
    current_user: UserResponse,
    research: Research,
    outline: str,
    config_response: ProjectConfigResponse2,
    callback: Optional[Callable[[str], None]] = None,
    complete_callback: Optional[Callable[[str], None]] = None,
    error_callback: Optional[Callable[[str], None]] = None,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    open_literature_summary = False
):
    """
    流式生成报告内容
    
    执行真实的搜索-总结-迭代-搜索流程，并流式返回生成结果
    """
    # 生成报告提示词
    team_members = [] if not config_response.team_members else [
      ProjectMemberBase(
        name=join.member.name,
        title=join.member.title,
        representative_works=join.member.representative_works,
        organization=join.member.organization
      )
      for join in config_response.team_members
    ]
    model_config = await get_user_model(
        current_user=current_user,
        use_case=UseCase.JUDGE_WEB_USEFUL.value
    )
    prompt = None
    try:
        search_engine_list = config_response.literature_library.split(",")
        # 搜索参考文献库
        for search_method_flag in search_engine_list:
            await search_to_carry_info(
                research=research,
                current_user=current_user,
                config_response=config_response,
                # 搜索关键词的引擎的标识符
                search_method_flag=search_method_flag,
                open_literature_summary=open_literature_summary,
            )
        try:
            # 搜索用户给的指定url
            logger.info(f"开始进行用户上传的URL的查询")
            url_list = await get_urls_by_project_id(config_response.id)
            tasks = [process_url(
                title=config_response.name,
                url=item.url,
                research_id=str(research.id),
                search_query=config_response.name,
                api_key=model_config.api_key,
                api_url=model_config.api_url,
                model=model_config.model_name,
                is_summary_literature=open_literature_summary
            ) for item in url_list]
            resources = await asyncio.gather(*tasks)
            url_contexts = []
            # 收集成功提取的上下文
            for resource in resources:
                if resource:
                    url_contexts.append(resource)
            research.contexts += url_contexts
            await research.save()
            
        except Exception as e:
            error_msg = ProjectReportError.GET_USER_ADD_URL_FAIL.value
            logger.error(error_msg)
        logger.info("迭代结束！开始正常流式生成报告内容")
        # 步骤3: 生成最终报告
        final_section = "## 步骤3: 生成研究报告\n\n"
        # yield f"data: {json.dumps({'content': final_section, 'type': 'section'})}\n\n".encode('utf-8')
        logger.info(final_section)

        generating_msg = "正在生成最终报告，这可能需要一些时间...\n\n"
        # yield f"data: {json.dumps({'content': generating_msg, 'type': 'status'})}\n\n".encode('utf-8')
        logger.info(generating_msg)

        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        # 打印research.query和research.contexts用于调试
        debug_info = f"DEBUG - Query: {research.query}\n\n"
        logger.info(debug_info)
        # yield f"data: {json.dumps({'content': debug_info, 'type': 'debug'})}\n\n".encode('utf-8')

        # 显示收集的上下文数量
        contexts_count = f"DEBUG - 收集到 {len(research.contexts)} 条上下文\n\n"
        logger.info(contexts_count)
        # yield f"data: {json.dumps({'content': contexts_count, 'type': 'debug'})}\n\n".encode('utf-8')

        # 如果需要显示详细内容，可以添加以下代码（谨慎使用，可能输出很多内容）
        for i, context in enumerate(research.contexts[:3]):  # 只显示前3条，避免输出过多
            context_preview = context[:200] + "..." if len(context) > 200 else context
            context_info = f"上下文 #{i+1}:\n{context_preview}\n\n"
            logger.info(context_info)
            # yield f"data: {json.dumps({'content': context_info, 'type': 'debug'})}\n\n".encode('utf-8')

        # 还可以添加日志记录
        logger.info(f"研究id {research.id}: 查询: {research.query}")
        logger.info(f"研究id {research.id}: 收集到 {len(research.contexts)} 条上下文")
        messages = None
        try:
            config = generate_project_config_prompt(
                name=config_response.name,
                # application_category=config_response.application_category or "",
                leader=config_response.leader,
                team_members=team_members,
                word_count_requirement=config_response.word_count_requirement or 0,
                team_introduction=config_response.team_introduction or "",
                flag='REPORT',
                leader_introduction=config_response.ai_leader_introduction
                # language_style=config_response.language_style or ""
            )
            participants = "、".join([
                f"{item.name}{item.title or ''}" + 
                (f"，就职于{item.organization}" if item.organization else "") + 
                (f"，{item.education + '学历'}" if item.education else "") + 
                (f"，代表性成就有{item.representative_works}" if item.representative_works else "")
                for item in team_members
            ])
            main = "" if not config_response.leader else f"{config_response.leader.name}, institution established date {config_response.leader.founded_date}, related projects: {config_response.leader.related_projects}"
            literatures = await get_literature_by_id(research.id)

            # 获取参考资料 - 使用新的中间表
            from app.models.file_biz_relations import FileBizRelation
            from app.utils.constants import ProductType
            
            # 获取该项目的所有参考资料关联（只查询未删除的文件）
            reference_relations = await FileBizRelation.filter(
                product_type=ProductType.DOCGEN,
                biz_id=str(config_response.id),
                file__is_deleted=False  # 通过关联查询过滤已删除的文件
            ).prefetch_related("file").all()
            
            # 汇总所有AI分析结果
            all_analysis_results = []
            for relation in reference_relations:
                file = relation.file
                # 🔧 关键修复：添加文件存在性和软删除检查
                if not file:
                    logger.warning(f"关联记录 {relation.id} 的文件已被删除，跳过处理")
                    continue
                if getattr(file, 'is_deleted', False):
                    logger.warning(f"关联记录 {relation.id} 的文件已被软删除，跳过处理，文件名: {file.file_name}")
                    continue
                file_name = file.file_name           # 文件名称
                word_count = file.word_count         # 字数（不是file_size）
                analysis_result = relation.ai_analysis_summary  # 从关联表获取AI分析结果
                
                logger.info(f"处理参考资料文件: {file_name}, 字数: {word_count}, AI分析结果长度: {len(analysis_result) if analysis_result else 0}")
                
                if analysis_result:
                    all_analysis_results.append(f"\n{analysis_result}")
            
            # 合并所有分析结果
            final_analysis_result = "\n\n".join(all_analysis_results) if all_analysis_results else ""
            logger.info(f"项目 {config_response.id} 的参考资料总结完成，共 {len(reference_relations)} 个文件，合并后分析结果长度: {len(final_analysis_result)}")
           
            # 准备生成报告的提示词

            prompt = await prompts.final_report_prompt(
                outline=outline,
                language_style=config_response.language_style or "",
                name=config_response.name,
                application_category=config_response.application_category or "",
                config=config,
                word_count=config_response.word_count_requirement or 0,
                team_introduction=config_response.team_introduction or "",
                participants=participants,
                main=main,
                literatures=literatures,
                contexts=research.contexts,
                analysis_result=final_analysis_result,  # 使用合并后的分析结果
                is_summary_literature=open_literature_summary,
                user_prompt=config_response.user_add_prompt
            )

            if open_literature_summary:
                reference_prompt = await prompts.final_report_reference_prompt(
                    literatures=literatures,
                    is_summary_literature=open_literature_summary
                )
                logger.info(f"参考文献的提示词：\n{reference_prompt}")
                prompt = reference_prompt + prompt

            system_prompt = await prompts.final_report_prompt_system(
                word_count=config_response.word_count_requirement or 0,
                is_summary_literature=open_literature_summary
            )
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
            logger.info(f"最终报告的系统提示词：\n{system_prompt}")
            logger.info(f"最终报告的用户提示词：\n{prompt}")
        except Exception as e:
            msg = f"生成报告提示词失败: {str(e)}"
            logger.error(msg)
            raise Exception(msg)
        
        await stream_llm_and_save(
            messages=messages,
            user=current_user, 
            callback=callback,
            complete_callback=complete_callback,
            error_callback=error_callback,
            flag=CallLLMFlag.GENERATE_REPORT.value,
            model=model,
            apiKey=api_key,
            apiUrl=api_url,
            related_id=config_response.id
        )
    except Exception as e:
        error_msg = f"报告流式生成错误: {str(e)}"
        logger.error(error_msg)
        await wrapper_error_callback(error_msg)

