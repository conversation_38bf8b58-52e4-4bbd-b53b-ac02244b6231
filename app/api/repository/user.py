from app.models.user import User
from pydantic import UUID4
from app.api.schemas.role import InsetRole
from app.api.repository.model_configs import get_super_admin_model
from app.api.repository.organization_model import get_organization_model
from app.api.schemas.user import UserResponse
from typing import List
from app.api.schemas.model_config import ModelConfigBase


# 判断一个资源是不是某个用户可以操作
async def is_user_authed(
  resource_belong_user: UUID4,
  operator: UUID4
):
  operator_user = await User.filter(
    id=operator
  ).prefetch_related("role", "organization").first()
  resource_user = await User.filter(
    id=resource_belong_user
  ).prefetch_related("role", "organization").first()
  # 如果是i超级管理员就什么权限都有
  if operator_user.role.identifier == InsetRole.SUPER_ADMIN.value:
    return True
  # 如果是机构管理员就对本机构的所有资源都有操作权限
  elif operator_user.role.identifier == InsetRole.ADMIN.value:
    return (
      operator_user.organization and
      resource_user.organization and 
      resource_user.organization.id == operator_user.organization.id
    )
  # 如果是其他用户则要求资源的所属用户ID和操作者的ID一致
  else:
    return operator_user.id == resource_user.id

# 判断一个资源是不是某个用户可以操作，超级管理员可以操作所有的资源，其他用户只能操作自己创建的资源
def is_user_authed_self_only(
  resource_belong_user: UUID4,
  operator: UserResponse
):
  # 如果是超级管理员就什么权限都有
  if operator.role.identifier == InsetRole.SUPER_ADMIN.value:
    return True
  else:
    # 如果是其他用户则要求资源的所属用户ID和操作者的ID一致
    return resource_belong_user == operator.id

# 获取用户的所有可用模型列表
async def get_user_model_list(
  current_user: UserResponse
) -> List[ModelConfigBase] :
  result = []
  if current_user.role.identifier == InsetRole.SUPER_ADMIN.value:
    result = await get_super_admin_model()
  elif current_user.organization:
    list_data = await get_organization_model(current_user.organization.id)
    result = [item.model for item in list_data]
  return [ModelConfigBase.model_validate(item, from_attributes=True) for item in result]
  
