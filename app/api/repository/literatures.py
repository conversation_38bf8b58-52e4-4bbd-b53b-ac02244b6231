from app.models.literatures import Literature
from app.api.schemas.literatures import LiteratureCreate, LiteratureResponse
from datetime import datetime
from typing import List
from app.models.project_configs import ProjectConfig
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)

async def create_literature(literature: LiteratureCreate) -> LiteratureResponse:
    """创建文献"""
    try:  
      data = literature.model_dump()
      data["updated_at"] = datetime.now()
      literature = Literature(**data)
      await literature.save()
      return LiteratureResponse.model_validate(literature)
    except Exception as e:
      logger.error(f"创建文献失败:{e}")


async def get_literature_by_id(research_id: str) -> List[LiteratureResponse]:
    """根据研究ID获取文献"""
    literature = await Literature.filter(research_id=research_id).all()
    return [LiteratureResponse.model_validate(item) for item in literature]


async def get_literature_by_project(config_id: str) -> List[LiteratureResponse]:
    """根据研究ID获取项目配置"""
    project_config = await ProjectConfig.filter(id=config_id, is_deleted=False).first()
    if not project_config:
      raise ValueError("项目配置不存在")
    literatures = await Literature.filter(research_id=project_config.research_id).all()
    literature_list = [LiteratureResponse.model_validate(item) for item in literatures]
    return literature_list
async def get_literature_length(research_id: str) -> int:
   literature_count = await Literature.filter(research_id=research_id).count()
   return literature_count