from pydantic import UUID4
from app.core.logging import get_logger
from app.models.voice_text import VoiceText
from app.api.schemas.user import UserResponse
from app.api.schemas.voice_text import VoiceTextCreate, VoiceTextUpdate, VoiceTextResponse

logger = get_logger(__name__)

async def create_voice_text(
  data: VoiceTextCreate
) -> VoiceText:
  """
  创建一条录音转文字记录。
  参数说明：
  - user_id: 当前请求的用户，用于审计日志记录
  - text_content: 文字内容，可为空（异步转写场景）
  返回：
  - 创建成功的 VoiceTextResponse 实体
  """
  try:
    result = await VoiceText.create(
      user_id=data.user_id,
      text_content=data.text_content
    )
    logger.info(f"用户{data.user_id}创建VoiceText记录: {result.id}")
    return VoiceTextResponse.model_validate(result, from_attributes=True)
  except Exception as e:
    logger.error(f"创建VoiceText失败: {str(e)}")
    raise e

async def update_voice_text(
    voice_text_id: UUID4,
    data: VoiceTextUpdate
) -> bool:
  """
  更新录音转文字记录。
  参数说明：
  - voice_text_id: 需要更新的记录ID
  - text_content: 新的文字内容
  - upload_file_id: 新的关联上传文件ID，可为空
  返回：
  - True 表示更新成功，False 表示无记录被更新
  """
  try:
    update_fields = {}
    if data.text_content is not None:
      update_fields["text_content"] = data.text_content
    if data.upload_file_id is not None:
      update_fields["upload_files_id"] = data.upload_file_id
    if not update_fields:
      logger.info("没有可以更新的数据")
      return True
    affected_rows = await VoiceText.filter(id=voice_text_id, is_deleted=False).update(**update_fields)
    is_updated = affected_rows > 0
    if is_updated:
      logger.info(f"更新VoiceText记录: {voice_text_id}")
    else:
      logger.info(f"尝试更新VoiceText记录但未找到: {voice_text_id}")
    return is_updated
  except Exception as e:
    logger.error(f"更新VoiceText失败: {str(e)}")
    raise e 