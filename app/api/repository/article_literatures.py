from app.models.article_literatures import ArticleLiterature
from app.api.schemas.article_literatures import ArticleLiteratureCreate, ArticleLiteratureResponse
from datetime import datetime
from typing import List
from app.models.article_generation_config import ArticleGenerationConfig
from app.core.logging import get_logger

# 获取logger实例
logger = get_logger(__name__)

async def create_article_literature(literature: ArticleLiteratureCreate) -> ArticleLiteratureResponse:
    """创建文章文献"""
    try:  
        data = literature.model_dump()
        data["updated_at"] = datetime.now()
        literature = ArticleLiterature(**data)
        await literature.save()
        return ArticleLiteratureResponse.model_validate(literature)
    except Exception as e:
        logger.error(f"创建文章文献失败:{e}")
        raise e

async def get_article_literature_by_id(research_id: str) -> List[ArticleLiteratureResponse]:
    """根据研究ID获取文章文献"""
    literature = await ArticleLiterature.filter(research_id=research_id).all()
    return [ArticleLiteratureResponse.model_validate(item) for item in literature]

async def get_article_literature_by_config_id(config_id: str) -> List[ArticleLiteratureResponse]:
    """根据文章配置ID获取文章文献"""
    literatures = await ArticleLiterature.filter(article_config_id=config_id).all()
    return [ArticleLiteratureResponse.model_validate(item) for item in literatures]

async def get_article_literature_length(research_id: str) -> int:
    """获取文章文献数量"""
    literature_count = await ArticleLiterature.filter(research_id=research_id).count()
    return literature_count

async def get_article_literature_count_by_config(config_id: str) -> int:
    """根据文章配置ID获取文献数量"""
    literature_count = await ArticleLiterature.filter(article_config_id=config_id).count()
    return literature_count