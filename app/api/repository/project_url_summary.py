from app.models.project_url_summary import ProjectUrlSummary
from app.api.schemas.project_url_summary import (
  ProjectUrlSummaryCreate,
  ProjectUrlSummaryResponse
)
from datetime import datetime
from typing import List
from pydantic import UUID4
from app.api.schemas.user import UserResponse
import asyncio
from app.core.logging import get_logger
from app.services.search_service import (
  fetch_webpage_text_async
)
from app.services.prompts import (
  evaluate_page_usefulness_prompt,
  PAGE_USEFULNESS_PROMPT_SYSTEM
)
from app.services.llm_service import call_llm
# from app.api.repository.model_configs import get_default_model_config
from app.utils.content_manager import ContentManager
from app.utils.enum import (
  ProjectUrlSummaryError,
  CallLLMFlag
)
from app.models.organization_model_use import UseCase
from app.api.repository.user_default_model import get_user_model

url_content_manager = ContentManager()
logger = get_logger(__name__)

async def is_page_useful(
  # 网页内容
  page_content: str,
  # 研究主题
  name: str,
  api_key: str,
  api_url: str,
  model: str
):
  """
    评估页面是否对研究有用
    
    Args:
        user_query: 用户查询
        page_content: 页面内容
    
    Returns:
        页面是否有用
    """
    # 截断过长的内容
  max_content_length = 20000  # 限制内容长度
  if len(page_content) > max_content_length:
    page_content = page_content[:max_content_length] + "..."
  
  # 准备提示词
  prompt = await evaluate_page_usefulness_prompt(name, page_content)
  messages = [
    {"role": "system", "content": PAGE_USEFULNESS_PROMPT_SYSTEM},
    {"role": "user", "content": prompt}
  ]
  
  # 调用LLM
  response = await call_llm(
    messages=messages,
    flag=CallLLMFlag.URL_IS_VALID.value,
    apiKey=api_key,
    apiUrl=api_url,
    model=model
  )
  if not response:
    return False
  
  # 清理响应
  response = response.strip().lower()
  
  # 检查响应是否为Yes
  if response == "yes":
    return True
  else:
    return False

async def process_url(
  url_data: ProjectUrlSummary,
  # 研究主题
  name: str,
  api_key: str = "",
  api_url: str = "",
  model: str = "",
) -> bool:
    """
    处理单个URL：判断是否有效
    
    Returns:
      
    """
    page_content = await fetch_webpage_text_async(url_data.url)
    is_useful = await is_page_useful(
      page_content=page_content,
      api_key=api_key,
      api_url=api_url,
      model=model,
      name=name
    )
    url_data.is_valid = is_useful
    await url_data.save()
    await url_data.refresh_from_db()
    return is_useful

async def get_urls_by_project_id(project_id: UUID4) -> List[ProjectUrlSummary]:
  """
  根据项目ID返回所有链接信息
  """
  result = await ProjectUrlSummary.filter(
    project_id=project_id,
    is_deleted=False
  ).all()
  return result
async def get_urls_by_ids(url_ids: List[UUID4]) -> List[ProjectUrlSummary]:
  """
  根据项目ID返回所有链接信息
  """
  result: List[ProjectUrlSummary] = []
  for item in url_ids:
    temp = await ProjectUrlSummary.filter(
      id=item,
      is_deleted=False
    ).first()
    result.append(temp)
  return result

async def batch_create_urls(
  url_list: List[str],
  name: str,
  current_user: UserResponse
) -> List[ProjectUrlSummary]:
  """
  批量插入url到指定项目
  Args:
      project_id: 项目ID
      url_list: url字符串列表
  Returns:
      int: 成功插入的数量
  """
  try:
    if not url_list:
      raise Exception(ProjectUrlSummaryError.URL_LIST_IS_EMPTY)
    model = await get_user_model(
      current_user=current_user,
      use_case=UseCase.PROJECT_CONFIG_NEED.value
    )
    list_result: List[ProjectUrlSummary] = []
    for url in url_list:
      item = await ProjectUrlSummary.create(
        url=url,
        user_id=current_user.id
      )
      list_result.append(item)
    async def handle(list_result):
      tasks = [process_url(
        url_data=item,
        name=name,
        api_key=model.api_key,
        api_url=model.api_url,
        model=model.model_name
      ) for item in list_result]
      await asyncio.gather(*tasks)
      for item in list_result:
        try:
          instance = url_content_manager.get_asyncio(item.id)
          url_content_manager.clear_project(item.id)
          instance and instance.cancel()
        except Exception as e:
          logger.error(f"{ProjectUrlSummaryError.STOP_ASYNC_TASK_ERROR}: {str(e)}")
    task = asyncio.create_task(handle(list_result))
      # 将任务实例保存到内容管理器
    for item in list_result:
      url_content_manager.add_asyncio(item.id, task)
    return list_result
  except Exception as e:
    logger.error(e)
    raise e

async def stop_async_handle(
  url_list: List[UUID4]
):
  """
  根据url列表的id暂停掉异步任务
  Args:
      url_list: url id列表
  Returns:
      True
  """
  for id in url_list:
    try:
      instance = url_content_manager.get_asyncio(id)
      url_content_manager.clear_project(id)
      instance and instance.cancel()
    except Exception as e:
      logger.error(f"{ProjectUrlSummaryError.STOP_ASYNC_TASK_ERROR.value}: {str(e)}")
  return True

async def detect_async_handle(
  url_list: List[UUID4]
):
  """
  根据url列表的id查询是否有还在运行中的异步任务
  Args:
      url_list: url id列表
  Returns:
      bool: 是否有还在运行中的异步任务
  """
  result = False
  for id in url_list:
    try:
      instance = url_content_manager.get_asyncio(id)
      if instance:
        result = True
        break
    except Exception as e:
      logger.error(f"{ProjectUrlSummaryError.GET_ASYNC_TASK_ERROR.value}: {str(e)}")
  return result 

async def bind_project(
  url_list: List[UUID4],
  project_id: UUID4
) -> int:
  """
  根据网站链接绑定到某个项目
  Args:
      url_list: url id列表
      project_id: 项目的ID
  Returns:
      int: 更新成功的数量
  """
  preview_data = await ProjectUrlSummary.filter(
    project_id=project_id,
    is_deleted=False
  ).all()
  for item in preview_data:
    if item.id not in url_list:
      item.is_deleted = True
      item.deleted_at = datetime.now()
      await item.save()
  result: int = 0
  for item in url_list:
    data = await get_one_url(item)
    if data:
      data.project_id = project_id
      await data.save()
      result += 1
  return result

# 获取某个网站记录的详细信息
async def get_one_url(id: UUID4):
  return await ProjectUrlSummary.filter(
    id=id,
    is_deleted=False
  ).first()
