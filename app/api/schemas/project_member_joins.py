from pydantic import BaseModel
from typing import Optional, List, Any
from datetime import datetime, timezone
from app.api.schemas.project_members import ProjectMemberResponse


class ProjectMemberJoinBase(BaseModel):
    join_id: str
    member: str


class ProjectMemberJoinCreate(BaseModel):
    join_id: Optional[str] = None
    member: List[str]


class ProjectMemberJoinResponse(BaseModel):
    join_id: str
    member: ProjectMemberResponse
    created_at: datetime
    is_deleted: int = 0
    deleted_at: Optional[datetime] = None
    
    model_config = {
      "from_attributes": True
    }


class ProjectMemberJoinUpdate(BaseModel):
    member: Optional[str] = None
    is_deleted: Optional[int] = None
    deleted_at: Optional[datetime] = None 