from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field
from datetime import datetime
from app.api.schemas.user import UserResponse
from app.models.workflow import WorkflowCategory


class DelWorkflow(BaseModel):
    name: str
class WorkflowBase(BaseModel):
    """工作流程基础模型"""
    name: str = Field(..., description="流程名称")
    project_config_id: UUID = Field(..., description="关联项目配置ID")
    operator: UUID = Field(..., description="操作人")
    content: Optional[str] = Field(None, description="流程内容")
    content_remark: Optional[str] = Field(None, description="流程内容的补充")

class WorkflowCreate(BaseModel):
    """创建工作流程请求模型"""
    name: str = Field(..., description="流程名称")
    project_config_id: UUID = Field(..., description="关联项目配置ID")
    content: Optional[str] = Field(None, description="流程内容")
    content_remark: Optional[str] = Field(None, description="流程内容的补充")

class WorkflowResponse(BaseModel):
    """工作流程响应模型"""
    id: UUID = Field(..., description="工作流程ID")
    name: str = Field(..., description="流程名称")
    project_config_id: UUID = Field(..., description="关联项目配置ID")
    content: Optional[str] = Field(None, description="流程内容")
    content_remark: Optional[str] = Field(None, description="流程内容的补充")
    operator: Optional[UserResponse] = None
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_deleted: int = Field(..., description="是否删除 (0: 正常, 1: 删除)")
    order: int = Field(..., description="流程顺序")
    category: WorkflowCategory = Field(..., description="流程分类：OUTLINE, REPORT, OTHER")
    class Config:
        from_attributes = True 