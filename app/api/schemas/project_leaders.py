from pydantic import BaseModel, UUID4, Field
from typing import Optional, List
from datetime import datetime, timezone, date
from pydantic import ConfigDict
from uuid import UUID
from app.api.schemas.area import AreaResponse


class ProjectLeaderBase(BaseModel):
    name: str = Field(..., description="主体名称")
    credit_code: str = Field(..., max_length=25, description="统一社会信用代码")
    institution_type: str = Field(..., max_length=20, description="机构性质")
    # ai_introduction: Optional[str] = Field(None, max_length=500, description="公司的AI详细介绍")
    founded_date: date = Field(..., description="成立时间")
    # patent_count: Optional[int] = Field(None, description="专利数量")
    related_projects: str = Field(..., description="主要业务及业绩")


class ProjectLeaderCreate(ProjectLeaderBase):
    province_id: Optional[int] = Field(None, description="省ID")
    city_id: Optional[int] = Field(None, description="市ID")
    district_id: Optional[int] = Field(None, description="区ID")
    address: Optional[str] = Field(None, max_length=200, description="详细地址")
    website: Optional[str] = Field(None, description="机构网站")


class ProjectLeaderResponse(ProjectLeaderBase):
    """项目主体响应模型"""
    id: UUID
    ai_introduction: Optional[str] = Field(None, max_length=500, description="公司的AI详细介绍")
    is_deleted: int
    deleted_at: Optional[datetime]
    updated_at: Optional[datetime]
    created_at: Optional[datetime]
    province: Optional[AreaResponse] = None
    city: Optional[AreaResponse] = None
    district: Optional[AreaResponse] = None
    address: Optional[str] = None
    website: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class ProjectLeaderUpdate(BaseModel):
    name: Optional[str] = Field(None, description="主体名称")
    credit_code: Optional[str] = Field(None, max_length=25, description="统一社会信用代码")
    institution_type: Optional[str] = Field(None, max_length=20, description="机构性质")
    province_id: Optional[int] = Field(None, description="省ID")
    city_id: Optional[int] = Field(None, description="市ID")
    district_id: Optional[int] = Field(None, description="区ID")
    address: Optional[str] = Field(None, max_length=200, description="详细地址")
    website: Optional[str] = Field(None, description="机构网站")
    ai_introduction: Optional[str] = Field(None, max_length=500, description="公司的AI详细介绍")
    founded_date: Optional[date] = Field(None, description="成立时间")
    # patent_count: Optional[int] = Field(None, description="专利数量")
    related_projects: Optional[str] = Field(None, description="主要业务及业绩")

