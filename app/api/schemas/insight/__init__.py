# 使insight文件夹成为Python包
from app.api.schemas.insight.insight_report import (
    GenerateReportRequest,
    GenerateReportResponse,
    ReportGenerationInfo,
    ReportListItem,
    ReportDetailResponse,
    ReportQueryParams,
    BatchGenerateReportRequest,
    BatchGenerateReportResponse
)
from app.models.insight.inspirations_canvas_report_relation import InspirationSourceType

__all__ = [
    "InspirationSourceType", 
    "GenerateReportRequest",
    "GenerateReportResponse",
    "ReportGenerationInfo",
    "ReportListItem",
    "ReportDetailResponse",
    "ReportQueryParams",
    "BatchGenerateReportRequest",
    "BatchGenerateReportResponse"
] 