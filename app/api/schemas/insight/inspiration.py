from pydantic import BaseModel, Field, UUID4
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.utils.utils import PageQuery


class InspirationQueryParams(PageQuery):
    """灵感查询参数"""
    tag: Optional[str] = Field(None, description="标签筛选")
    keyword: Optional[str] = Field(None, description="关键词搜索")


class InspirationRequest(BaseModel):
    """灵感生成请求模型"""
    canvas_ids: List[str] = Field(..., description="知识卡片ID列表，最多10个")


class InspirationResponse(BaseModel):
    """灵感生成响应模型"""
    content: str = Field(..., description="生成的灵感内容")
    summary: Optional[str] = Field(None, description="灵感综述")


class InspirationSource(BaseModel):
    """灵感来源模型"""
    source_id: str = Field(..., description="来源ID")
    source_type: str = Field(..., description="来源类型")
    source_name: str = Field(..., description="来源名称")


class InspirationTag(BaseModel):
    """灵感标签模型"""
    id: UUID4 = Field(..., description="标签ID")
    name: str = Field(..., description="标签名称")


class SaveInspirationRequest(BaseModel):
    """保存灵感请求模型"""
    name: str = Field(..., description="灵感名称")
    inspiration_source: List[InspirationSource] = Field(..., description="灵感来源")
    tags: List[str] = Field(..., description="灵感标签ID或名称列表")
    original_article_truncated: Optional[str] = Field(None, description="灵感库综述")


class InspirationDetail(BaseModel):
    """灵感详情响应模型"""
    id: UUID4 = Field(..., description="灵感ID")
    name: str = Field(..., description="灵感名称")
    source: List[InspirationSource] = Field(..., description="灵感来源")
    tags: List[InspirationTag] = Field(..., description="灵感标签")
    content: str = Field(..., description="灵感内容")
    summary: Optional[str] = Field(None, description="灵感库简介")
    original_article_truncated: Optional[str] = Field(None, description="灵感库综述")
    ai_expanded: Optional[str] = Field(None, description="AI扩写")
    ai_analysis: Optional[str] = Field(None, description="AI分析")
    ai_keynotes: Optional[str] = Field(None, description="AI重点")
    ai_outline: Optional[str] = Field(None, description="AI结构大纲")
    ai_probe: Optional[str] = Field(None, description="AI追问")
    ai_thought_process: Optional[str] = Field(None, description="AI思考过程")
    is_deleted: bool = Field(False, description="是否删除")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")
    user_id: Optional[UUID4] = Field(None, description="创建用户ID")
    insight_report_id: Optional[UUID4] = Field(None, description="报告ID")

class InspirationListItem(BaseModel):
    """灵感列表项响应模型"""
    id: UUID4 = Field(..., description="灵感ID")
    name: str = Field(..., description="灵感名称")
    summary: Optional[str] = Field(None, description="灵感综述")
    tags: List[InspirationTag] = Field(..., description="灵感标签")
    source: List[InspirationSource] = Field(..., description="灵感来源")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class InspirationList(BaseModel):
    """灵感列表响应模型"""
    items: List[InspirationListItem] = Field(..., description="灵感列表")
    total: int = Field(..., description="总数")


class CreateCanvasFromInspirationRequest(BaseModel):
    """根据灵感库ID创建灵感卡片请求模型"""
    inspiration_ids: List[UUID4] = Field(..., description="灵感库ID列表", min_items=1, max_items=10)


class CreatedCanvasItem(BaseModel):
    """创建的灵感卡片项"""
    id: UUID4 = Field(..., description="灵感卡片ID")
    name: str = Field(..., description="灵感卡片名称")
    source_type: str = Field(..., description="来源类型")
    type: str = Field(..., description="卡片类型")
    summary: Optional[str] = Field(None, description="灵感卡片概要")
    original_article_truncated: Optional[str] = Field(None, description="原始文章截取")
    inspiration_source: List[dict] = Field(default=[], description="灵感来源")
    tags: List[str] = Field(default=[], description="标签列表")
    ai_expanded: Optional[str] = Field(None, description="AI扩写")
    ai_analysis: Optional[str] = Field(None, description="AI分析")
    ai_keynotes: Optional[str] = Field(None, description="AI重点注释")
    ai_outline: Optional[str] = Field(None, description="AI大纲")
    ai_probe: Optional[str] = Field(None, description="AI追问")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    user_id: UUID4 = Field(..., description="创建用户ID")


class CreateCanvasFromInspirationResponse(BaseModel):
    """根据灵感库创建灵感卡片响应模型"""
    created_canvases: List[CreatedCanvasItem] = Field(..., description="成功创建的灵感卡片列表")
    success_count: int = Field(..., description="成功创建的数量")
    total_count: int = Field(..., description="总请求数量") 