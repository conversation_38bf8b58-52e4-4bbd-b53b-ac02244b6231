"""
insight报告相关的Pydantic Schema定义
用于定义生成报告的请求和响应数据结构
"""
from app.models.insight.hi_insight_report import InsightReportType
from app.models.insight.inspirations_canvas_report_relation import InspirationSourceType
from pydantic import BaseModel, UUID4, Field
from typing import Optional, List
from datetime import datetime


class GenerateReportRequest(BaseModel):
    """生成报告请求模型"""
    content_id: UUID4 = Field(..., description="需要生成报告的内容ID（知识卡片ID或灵感库ID）")
    content_type: InspirationSourceType = Field(..., description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库")
    report_type: InsightReportType = Field(..., description="报告类型")


class ReportGenerationInfo(BaseModel):
    """报告生成信息"""
    generation_id: UUID4 = Field(..., description="生成记录ID")
    model_name: str = Field(..., description="使用的模型名称")
    generation_time_ms: Optional[int] = Field(None, description="生成耗时(毫秒)")
    input_tokens: Optional[int] = Field(None, description="输入token数量")
    output_tokens: Optional[int] = Field(None, description="输出token数量")
    total_tokens: Optional[int] = Field(None, description="总token数量")


class GenerateReportResponse(BaseModel):
    """生成报告响应模型"""
    report_id: UUID4 = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    content: str = Field(..., description="报告内容")
    content_name: str = Field(..., description="内容名称")
    content_id: UUID4 = Field(..., description="内容ID")
    content_type: InspirationSourceType = Field(..., description="内容类型")
    report_type: InsightReportType = Field(..., description="报告类型")
    word_count: Optional[int] = Field(None, description="实际字数统计")
    generation_info: ReportGenerationInfo = Field(..., description="生成统计信息")
    created_at: datetime = Field(..., description="创建时间")


class ReportListItem(BaseModel):
    """报告列表项"""
    id: UUID4 = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    report_type: InsightReportType = Field(..., description="报告类型")
    status: str = Field(..., description="报告状态：processing/completed/failed")
    word_count: Optional[int] = Field(None, description="字数统计")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ReportDetailResponse(BaseModel):
    """报告详情响应模型"""
    id: UUID4 = Field(..., description="报告ID")
    title: str = Field(..., description="报告标题")
    report_type: InsightReportType = Field(..., description="报告类型")
    status: str = Field(..., description="报告状态")
    content: str = Field(..., description="报告内容")
    word_count: Optional[int] = Field(None, description="字数统计")
    generations: List[ReportGenerationInfo] = Field(default=[], description="所有生成记录")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ReportQueryParams(BaseModel):
    """报告查询参数"""
    keyword: Optional[str] = Field(None, description="关键词搜索")
    report_type: Optional[InsightReportType] = Field(None, description="报告类型筛选")
    status: Optional[str] = Field(None, description="状态筛选：processing/completed/failed")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")


class BatchGenerateReportRequest(BaseModel):
    """批量生成报告请求模型"""
    content_items: List[dict] = Field(
        ..., 
        description="内容项列表，每项包含content_id和content_type",
        min_items=1,
        max_items=10
    )
    report_type: InsightReportType = Field(..., description="报告类型")
    word_count_requirement: Optional[int] = Field(default=3000, ge=500, le=50000, description="报告字数要求")
    include_references: Optional[bool] = Field(default=True, description="是否包含参考文献")
    custom_requirements: Optional[str] = Field(default=None, max_length=1000, description="自定义要求")


class BatchGenerateReportResponse(BaseModel):
    """批量生成报告响应模型"""
    successful_reports: List[GenerateReportResponse] = Field(default=[], description="成功生成的报告列表")
    failed_items: List[dict] = Field(default=[], description="失败的项目列表，包含content_id和失败原因")
    success_count: int = Field(..., description="成功数量")
    total_count: int = Field(..., description="总请求数量") 