from pydantic import BaseModel, UUID4
from typing import Optional, List
from datetime import datetime
from app.utils.constants import ProductType
from app.api.schemas.requirements_attachment_files import RequirementsAttachmentResponse


class FileBizRelationBase(BaseModel):
    """文件和业务关联基础Schema"""
    ai_analysis_summary: Optional[str] = None
    product_type: ProductType
    biz_id: Optional[str] = None


class FileBizRelationCreate(FileBizRelationBase):
    """创建文件和业务关联Schema"""
    file_id: UUID4


class FileBizRelationUpdate(BaseModel):
    """更新文件和业务关联Schema"""
    ai_analysis_summary: Optional[str] = None
    biz_id: Optional[str] = None


class FileBizRelationResponse(FileBizRelationBase):
    """文件和业务关联响应Schema"""
    id: UUID4
    file: RequirementsAttachmentResponse
    created_at: datetime
    updated_at: datetime
    
    model_config = {
        "from_attributes": True
    }


class FileBizRelationBindRequest(BaseModel):
    """文件和业务关联绑定请求Schema"""
    file_ids: List[UUID4]
    product_type: ProductType
    biz_id: str
