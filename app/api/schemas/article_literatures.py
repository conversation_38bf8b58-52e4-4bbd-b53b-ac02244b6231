from pydantic import BaseModel, Field
from typing import Optional
from uuid import UUID


class ArticleLiteratureBase(BaseModel):
    """文章文献基础模型"""
    title: str = Field(..., description="文献标题")
    authors: str = Field(..., description="作者，多个作者用逗号分隔")
    journal: str = Field(..., description="期刊名称")
    year: int = Field(..., description="发表年份")
    issue: Optional[str] = Field(None, description="期号")
    volume: Optional[str] = Field(None, description="卷号")
    pages: Optional[str] = Field(None, description="页码范围，如：123-145")
    doi: Optional[str] = Field(None, description="DOI索引")
    summary: str = Field(..., description="文献总结")
    url: str = Field(..., description="文献网页链接")


class ArticleLiteratureCreate(ArticleLiteratureBase):
    """创建文章文献模型"""
    article_config_id: UUID = Field(..., description="关联的文章生成配置ID")


class ArticleLiteratureResponse(BaseModel):
    """文章文献响应模型"""
    # id: UUID
    # article_config_id: UUID
    doi: Optional[str] = Field(None, description="DOI索引")
    url: Optional[str] = Field(None, description="文献网页链接")
    title: str = Field(..., description="文献标题")
    citation_format: Optional[str] = Field(None, description="标准引用格式")
    summary: str = Field(..., description="文献总结")

    model_config = {"from_attributes": True} 