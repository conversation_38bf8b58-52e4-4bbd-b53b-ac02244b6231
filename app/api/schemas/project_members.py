from typing import List, Optional
from pydantic import BaseModel, UUID4, ConfigDict
from datetime import datetime


class ProjectMemberBase(BaseModel):
    name: str
    title: str
    organization: Optional[str] = None
    introduction: Optional[str] = None
    education: Optional[str] = None
    representative_works: Optional[str] = None


class ProjectMemberCreate(ProjectMemberBase):
    pass


class ProjectMemberResponse(ProjectMemberBase):
    id: UUID4
    created_at: datetime
    updated_at: datetime
    is_deleted: int = 0
    deleted_at: Optional[datetime] = None
    
    class Config:
        # orm_mode = True
        from_attributes = True


class ProjectMemberUpdate(BaseModel):
    name: Optional[str] = None
    title: Optional[str] = None
    organization: Optional[str] = None
    introduction: Optional[str] = None
    education: Optional[str] = None
    representative_works: Optional[str] = None
    is_deleted: Optional[int] = None
    deleted_at: Optional[datetime] = None 


class TeamIntroductionRequest(BaseModel):
    """团队介绍请求模型"""
    member_ids: List[str]
    project_configs_name: str
    model_config_id: Optional[UUID4] = None

    
class TeamMemberInfo(BaseModel):
    """团队成员信息模型，用于团队介绍生成"""
    name: str
    title: str
    organization: Optional[str] = None
    introduction: Optional[str] = None
    education: Optional[str] = None
    representative_works: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)


class TeamIntroductionResponse(BaseModel):
    """团队介绍响应模型"""
    team_introduction: str
    model_config = ConfigDict(from_attributes=True) 