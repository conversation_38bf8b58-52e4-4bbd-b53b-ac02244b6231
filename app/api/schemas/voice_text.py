from pydantic import BaseModel, UUID4, Field
from typing import Optional
from datetime import datetime
from app.api.schemas.upload_file import UploadFileResponse

class VoiceTextCreate(BaseModel):
  """
  录音转文字创建请求模型
  字段说明：
  - text_content: 转写出的文本内容，可为空（异步转写场景下可后续更新）
  - upload_file_id: 关联的上传语音文件ID，可为空
  """
  text_content: Optional[str] = Field(None, description="录音文件的文字内容")
  user_id: UUID4 = Field(None, description="记录所属的用户")

class VoiceTextUpdate(BaseModel):
  """
  录音转文字更新请求模型
  字段说明：
  - text_content: 新的文本内容，可为空（仅更新文件时可不传）
  - upload_file_id: 新的上传语音文件ID，可为空
  """
  text_content: Optional[str] = Field(None, description="录音文件的文字内容")
  upload_file_id: Optional[UUID4] = Field(None, description="上传的语音文件ID")

class VoiceTextResponse(BaseModel):
  """
  录音转文字响应模型
  字段说明：
  - id: 记录ID
  - text_content: 文本内容
  - upload_file: 关联的上传文件信息（若存在）
  - created_at/updated_at: 创建与更新时间
  """
  id: UUID4
  text_content: Optional[str] = Field(None, description="录音文件的文字内容")
  upload_file: Optional[UploadFileResponse] = Field(None, description="关联的上传文件信息")
  created_at: Optional[datetime] = Field(None, description="创建时间")
  updated_at: Optional[datetime] = Field(None, description="更新时间") 