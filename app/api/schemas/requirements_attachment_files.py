from pydantic import BaseModel, UUID4
from typing import Optional, List
from datetime import datetime, timezone


class RequirementsAttachmentFilesBase(BaseModel):
    file_path: str
    file_name: str


class RequirementsAttachmentFilesCreate(RequirementsAttachmentFilesBase):
    requirements_attachment_id: UUID4


class RequirementsAttachmentFilesResponse(RequirementsAttachmentFilesBase):
    id: UUID4
    requirements_attachment_id: UUID4
    created_at: datetime
    
    model_config = {
        "from_attributes": True
    }


class RequirementsAttachmentBase(BaseModel):
    project_configs_id: Optional[UUID4] = None
    requirements_type: str


class RequirementsAttachmentCreate(RequirementsAttachmentBase):
    pass


class RequirementsAttachmentUpdate(BaseModel):
    requirements_type: Optional[str] = None


class RequirementsAttachmentResponse(BaseModel):
    id: UUID4
    file_path: str
    file_name: str
    file_content: Optional[str] = None
    word_count: int = 0
    analysis_result: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    project_configs_id: Optional[UUID4] = None
    
    # 软删除字段
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None
    
    model_config = {
        "from_attributes": True
    }


class RequirementsTypeContent(BaseModel):
    """处理附件内容的简化模型，只包含文件信息"""
    id: UUID4
    file_path: str
    file_name: str
    file_content: Optional[str] = None
    word_count: int = 0
    analysis_result: Optional[str] = None
    project_configs_id: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime

    # 软删除字段
    is_deleted: bool = False
    deleted_at: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }


class BatchResummarizeRequest(BaseModel):
    """批量重新总结请求模型"""
    attachment_ids: List[UUID4]
    project_configs_name: Optional[str] = None


class BatchResummarizeResponse(BaseModel):
    """批量重新总结响应模型"""
    total_count: int
    success_count: int
    failed_count: int
    results: List[RequirementsAttachmentResponse]
    failed_items: List[dict] = []