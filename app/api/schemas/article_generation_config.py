from pydantic import BaseModel, Field
from typing import Optional, List, Union
from uuid import UUID
from enum import Enum
from datetime import datetime


class ArticleType(str, Enum):
    """文章类型枚举"""
    MEETING_MINUTES = "meeting_minutes"  # 会议纪要
    THINK_TANK_REPORT = "think_tank_report"  # 智库报告
    MARKET_RESEARCH = "market_research"  # 市场行业研习报告
    FEASIBILITY_STUDY = "feasibility_study"  # 可研报告
    LITERATURE_REVIEW = "literature_review"  # 文献综述
    CUSTOM = "custom"  # 自定义


class ArticleGenerationConfigCreateRequest(BaseModel):
    """创建文章生成配置请求模型"""
    # 基本信息
    name: str = Field(..., description="文章标题/名称")
    article_type: Optional[ArticleType] = Field(ArticleType.CUSTOM, description="文章类型")
    
    # 团队成员信息（JSON字符串格式）
    team_members: Optional[str] = Field(None, description="团队成员信息")
    
    # 团队整体介绍
    team_introduction: Optional[str] = Field(None, description="团队整体介绍")
    
    # 参考文献库（多个用逗号分隔）
    reference_library: Optional[str] = Field(None, description="参考文献库")
    
    # 申报主题
    leader: Optional[str] = Field(None, description="申报主体ID")
    
    ai_leader_introduction: Optional[str] = Field(None, description="申报主体的AI介绍")
    # 参考材料描述（数组格式）
    reference_library_urls: Optional[List[str]] = Field(None, description="参考文献库URL列表")
    
    # 自定义模板名称
    user_add_demo_id: Optional[str] = Field(None, description="用户添加的示例文件ID")
    
    # 用户自定义提示词
    user_add_prompt: Optional[str] = Field(None, description="用户自定义提示词")
    
    # 字数要求
    word_count_requirement: Optional[int] = Field(None, description="字数要求")
    
    # 语言风格：INFORM(正式), CASUAL(随意), ACADEMIC(学术)等
    language_style: Optional[str] = Field(None, description="语言风格")
    
    # 应用类别：NSFC(国家自然科学基金), 863(863计划)等
    application_category: Optional[str] = Field(None, description="应用类别")
    
    # 需求附件内容（JSON数组格式）
    requirements_attachments: Optional[List[str]] = Field(None, description="需求附件内容")
    
    # 搜索引擎：google, bing, baidu等
    search_engine: str = Field(..., description="搜索引擎选择")
    
    # 搜索列表数量
    search_list_count: int = Field(..., description="搜索列表数量")
    
    # 搜索迭代次数
    search_iterations: int = Field(..., description="搜索迭代次数")
    
    # 检索内容数量
    retrieval_content_count: int = Field(..., description="检索内容数量")
    
    # 是否启用文献摘要
    literature_summary_enabled: bool = Field(..., description="是否启用文献摘要")
    
    # 最大内容收集数量
    max_content_collection_count: int = Field(..., description="最大内容收集数量")
    
    # 最大参考文献数量
    max_reference_count: int = Field(..., description="最大参考文献数量")
    
    # 使用的AI模型ID
    model_id: UUID = Field(..., description="使用的AI模型ID")
    
    # 温度参数（控制创造性，0-1）
    temperature: float = Field(..., description="温度参数")
    
    # Top-p参数（控制多样性，0-1）
    top_p: float = Field(..., description="Top-p参数")
    
    # Top-k参数（控制候选词数量）
    top_k: int = Field(..., description="Top-k参数")
    
    # 大纲生成的系统提示词
    outline_system_prompt: str = Field(..., description="大纲生成的系统提示词")
    
    # 内容生成的系统提示词
    content_system_prompt: str = Field(..., description="内容生成的系统提示词")
    
    # 大纲生成的用户提示词
    outline_user_prompt: Optional[str] = Field(None, description="大纲生成的用户提示词")
    
    # 内容生成的用户提示词
    content_user_prompt: Optional[str] = Field(None, description="内容生成的用户提示词")
    
    # 关联的研究项目ID（如果有的话）
    research_id: Optional[str] = Field(None, description="关联的研究项目ID")

    class Config:
        # 支持额外的字段
        extra = "allow"
        # 允许字段别名
        allow_population_by_field_name = True


# 保留原有的 ArticleGenerationConfigBase 用于其他用途
class ArticleGenerationConfigBase(BaseModel):
    """文章生成配置基础模型"""
    # 基本信息
    name: str = Field(..., description="文章名称")
    article_type: Optional[ArticleType] = Field(ArticleType.CUSTOM, description="文章类型")
    
    # 会议纪要相关
    meeting_minutes_audio: Optional[str] = Field(None, description="会议纪要-录音文件路径")
    
    # 智库报告相关
    target_audience: Optional[str] = Field(None, description="智库报告-目标受众")
    analysis_method: Optional[str] = Field(None, description="智库报告-分析方法")
    
    # 市场行业研习报告相关
    industry_type: Optional[str] = Field(None, description="市场行业研习报告-行业类型")
    
    # 可研报告相关
    total_investment_amount: Optional[str] = Field(None, description="可研报告-总投资额")
    financial_analysis: Optional[str] = Field(None, description="可研报告-财务分析")
    risk_assessment: Optional[str] = Field(None, description="可研报告-风险评估")
    
    # 文献综述相关
    research_field: Optional[str] = Field(None, description="文献综述-研究领域")
    
    # 通用信息
    material_subject: Optional[str] = Field(None, description="材料主体")
    team_members: Optional[str] = Field(None, description="团队成员")
    team_introduction: Optional[str] = Field(None, description="团队介绍")
    reference_library: Optional[str] = Field(None, description="参考文献库")
    user_add_prompt: Optional[str] = Field(None, description="用户添加的提示词")
    word_count_requirement: Optional[int] = Field(None, description="字数要求")
    language_style: Optional[str] = Field(None, description="语言风格")
    application_category: Optional[str] = Field(None, description="申报口径")
    requirements_attachments: Optional[Union[str, List[str]]] = Field(None, description="申报要求附件")
    
    # 新增字段
    leader: Optional[str] = Field(None, description="申报主体ID")
    leader_id: Optional[str] = Field(None, description="申报主体ID")
    leader_text: Optional[str] = Field(None, description="申报主体")
    ai_leader_introduction: Optional[str] = Field(None, description="项目主体的AI介绍")
    requirements_attachments_id: Optional[str] = Field(None, description="申报要求附件ID（字符串类型）")
    team_members_id: Optional[str] = Field(None, description="团队成员ID（字符串类型）")
    user_add_demo_id: Optional[str] = Field(None, description="用户添加的示例文件ID")
    reference_library_urls: Optional[Union[str, List[str]]] = Field(None, description="参考文献库URL列表")
    
    # 搜索引擎配置
    search_engine: str = Field(..., description="搜索引擎选择")
    search_list_count: int = Field(..., description="搜索列表数")
    search_iterations: int = Field(..., description="搜索迭代次数")
    retrieval_content_count: int = Field(..., description="检索内容数")
    
    # 文献总结配置
    literature_summary_enabled: bool = Field(..., description="文献总结开关")
    max_content_collection_count: int = Field(..., description="最大内容收集数量")
    max_reference_count: int = Field(..., description="最大参考文献数")
    
    # 模型配置
    model_id: UUID = Field(..., description="模型选择")
    temperature: float = Field(..., description="温度参数")
    top_p: float = Field(..., description="Top-p参数")
    top_k: int = Field(..., description="Top-k参数")
    
    # 提示词配置
    outline_system_prompt: str = Field(..., description="大纲的系统提示词")
    content_system_prompt: str = Field(..., description="正文的系统提示词")
    outline_user_prompt: Optional[str] = Field(None, description="大纲的用户提示词")
    content_user_prompt: Optional[str] = Field(None, description="正文的用户提示词")


class TeamMemberResponse(BaseModel):
    """团队成员响应模型"""
    name: str
    title: Optional[str] = None
    representative_works: Optional[str] = None
    organization: Optional[str] = None


class ArticleGenerationConfigResponse(BaseModel):
    """文章生成配置响应模型"""
    id: str
    name: str
    industry_type: Optional[str] = None
    team_members: List[TeamMemberResponse] = []
    word_count_requirement: Optional[int] = None
    language_style: Optional[str] = None
    additional_info: Optional[str] = None
    search_engine: Optional[str] = None
    team_introduction: Optional[str] = None
    user_add_prompt: Optional[str] = None
    application_category: Optional[str] = None
    requirements_attachments: Optional[str] = None
    
    # 新增字段
    leader_id: Optional[str] = None
    leader_text: Optional[str] = None
    ai_leader_introduction: Optional[str] = None
    requirements_attachments_id: Optional[str] = None
    team_members_id: Optional[str] = None
    
    # 文件相关
    meeting_minutes_audio: Optional[str] = None
    target_audience: Optional[str] = None
    analysis_method: Optional[str] = None
    total_investment_amount: Optional[str] = None
    financial_analysis: Optional[str] = None
    risk_assessment: Optional[str] = None
    research_field: Optional[str] = None
    material_subject: Optional[str] = None
    reference_library: Optional[str] = None
    reference_library_urls: Optional[Union[str, List[str]]] = Field(None, description="参考文献库URL列表")
    user_add_prompt: Optional[str] = None
    
    # 搜索引擎配置
    search_list_count: Optional[int] = None
    search_iterations: Optional[int] = None
    retrieval_content_count: Optional[int] = None
    
    # 文献总结配置
    literature_summary_enabled: Optional[bool] = False
    max_content_collection_count: Optional[int] = None
    max_reference_count: Optional[int] = None
    
    # 模型参数配置
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    top_k: Optional[int] = None
    
    # 提示词管理
    outline_system_prompt: Optional[str] = None
    content_system_prompt: Optional[str] = None
    outline_user_prompt: Optional[str] = None
    content_user_prompt: Optional[str] = None
    
    # 生成状态
    status: Optional[str] = None
    outline_generation_status: Optional[str] = None
    content_generation_status: Optional[str] = None
    
    # 生成时间
    outline_generation_time: Optional[datetime] = None
    content_generation_time: Optional[datetime] = None
    
    # 文件路径
    generated_outline_file_path: Optional[str] = None
    generated_content_file_path: Optional[str] = None
    
    # Token消耗
    outline_tokens_consumed: Optional[int] = None
    content_tokens_consumed: Optional[int] = None
    
    # 时间戳
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # 关联研究表
    research_id: Optional[str] = None
    
    class Config:
        from_attributes = True


class ArticleGenerationConfigUpdateRequest(BaseModel):
    """更新文章生成配置请求模型"""
    name: Optional[str] = Field(None, description="文章名称")
    article_type: Optional[ArticleType] = Field(None, description="文章类型")
    
    # 会议纪要相关
    meeting_minutes_audio: Optional[str] = Field(None, description="会议纪要-录音文件路径")
    
    # 智库报告相关
    target_audience: Optional[str] = Field(None, description="智库报告-目标受众")
    analysis_method: Optional[str] = Field(None, description="智库报告-分析方法")
    
    # 市场行业研习报告相关
    industry_type: Optional[str] = Field(None, description="市场行业研习报告-行业类型")
    
    # 可研报告相关
    total_investment_amount: Optional[str] = Field(None, description="可研报告-总投资额")
    financial_analysis: Optional[str] = Field(None, description="可研报告-财务分析")
    risk_assessment: Optional[str] = Field(None, description="可研报告-风险评估")
    
    # 文献综述相关
    research_field: Optional[str] = Field(None, description="文献综述-研究领域")
    
    # 通用信息
    material_subject: Optional[str] = Field(None, description="材料主体")
    team_members: Optional[str] = Field(None, description="团队成员")
    team_introduction: Optional[str] = Field(None, description="团队介绍")
    reference_library: Optional[str] = Field(None, description="参考文献库")
    user_add_prompt: Optional[str] = Field(None, description="用户添加的提示词")
    word_count_requirement: Optional[int] = Field(None, description="字数要求")
    language_style: Optional[str] = Field(None, description="语言风格")
    application_category: Optional[str] = Field(None, description="申报口径")
    requirements_attachments: Optional[str] = Field(None, description="申报要求附件")
    
    # 新增字段
    leader_id: Optional[str] = Field(None, description="申报主体ID")
    leader_text: Optional[str] = Field(None, description="申报主体")
    ai_leader_introduction: Optional[str] = Field(None, description="项目主体的AI介绍")
    requirements_attachments_id: Optional[str] = Field(None, description="申报要求附件ID（字符串类型）")
    team_members_id: Optional[str] = Field(None, description="团队成员ID（字符串类型）")
    
    # 搜索引擎配置
    search_engine: Optional[str] = Field(None, description="搜索引擎选择")
    search_list_count: Optional[int] = Field(None, description="搜索列表数")
    search_iterations: Optional[int] = Field(None, description="搜索迭代次数")
    retrieval_content_count: Optional[int] = Field(None, description="检索内容数")
    
    # 文献总结配置
    literature_summary_enabled: Optional[bool] = Field(None, description="文献总结开关")
    max_content_collection_count: Optional[int] = Field(None, description="最大内容收集数量")
    max_reference_count: Optional[int] = Field(None, description="最大参考文献数")
    
    # 模型配置
    model_id: Optional[UUID] = Field(None, description="模型选择")
    temperature: Optional[float] = Field(None, description="温度参数")
    top_p: Optional[float] = Field(None, description="Top-p参数")
    top_k: Optional[int] = Field(None, description="Top-k参数")
    
    # 提示词配置
    outline_system_prompt: Optional[str] = Field(None, description="大纲的系统提示词")
    content_system_prompt: Optional[str] = Field(None, description="正文的系统提示词")
    outline_user_prompt: Optional[str] = Field(None, description="大纲的用户提示词")
    content_user_prompt: Optional[str] = Field(None, description="正文的用户提示词")


class ArticleGenerationConfigListResponse(BaseModel):
    """文章生成配置列表响应模型"""
    id: str = Field(..., description="配置ID")
    name: str = Field(..., description="文章名称")
    article_type: Optional[str] = Field(None, description="文章类型")
    status: Optional[str] = Field(None, description="生成状态")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    
    # 关联信息
    user_id: Optional[str] = Field(None, description="创建用户ID")
    organization_id: Optional[str] = Field(None, description="所属机构ID")
    model_id: Optional[str] = Field(None, description="使用的模型ID")

    model_config = {"from_attributes": True} 
