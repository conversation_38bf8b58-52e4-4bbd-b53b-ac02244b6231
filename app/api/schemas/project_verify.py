from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
from typing import List, Optional

class Status(str, Enum):
  COMPLETED = "completed"
  PENDING = "pending"
class HallucinationResponse(BaseModel):
  status: Status = Field(..., description="状态：completed和pending")
  data: Optional[str] = Field(None, description="幻觉审查报告")
  estimated_time: Optional[datetime] = Field(None, description="预估时间")
  model_config = {
    "from_attributes": True
  }
class ProjectConfigAITraces(BaseModel):
  original: str = Field(..., description="原始内容")
  modified: str = Field(..., description="修改后的内容")
  seriesNum: int = Field(..., description="文本原始的位置")
class AITraceResponse(BaseModel):
  status: Status = Field(..., description="状态：completed和pending")
  data: Optional[List[ProjectConfigAITraces]] = Field(..., description="去AI痕迹的内容")
  estimated_time: Optional[datetime] = Field(None, description="预估时间")
  model_config = {
    "from_attributes": True
  }