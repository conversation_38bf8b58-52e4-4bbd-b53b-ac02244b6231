from typing import List
from uuid import UUID
from fastapi import APIRouter

from app.api.schemas.workflow import (
    WorkflowResponse
)
from fastapi.responses import StreamingResponse
import json
from app.models.workflow import Workflow
from app.models.project_configs import ProjectConfig
# from app.models.user import User
from app.api.schemas.user import UserResponse
from app.utils.utils import send_data, ResponseModel, stream_file_content_sse, ContentStatus
from app.models.workflow import Order, Name
from app.models.workflow import WorkflowCategory


router = APIRouter()

def get_result_type(val: str) -> str:
    outline_vals = {
        "GENERATED_OUTLINE_FIRST_TIME",
        "REVISED_OUTLINE",
        "OUTLINE_POLISHING"
    }
    content_vals = {
        "GENERATED_CONTENT_FIRST_TIME",
        "REVISED_CONTENT",
        "CONTENT_POLISHING"
    }

    if val in outline_vals:
        return WorkflowCategory.OUTLINE.value
    elif val in content_vals:
        return WorkflowCategory.REPORT.value
    else:
        return WorkflowCategory.OTHER.value

async def create_workflow(
    project_id: UUID,
    name: str,
    content: str,
    current_user: UserResponse
):
    """
    创建工作流程
    """
    # 验证项目配置存在
    project_config = await ProjectConfig.filter(id=project_id, is_deleted=0).first()
    if not project_config:
        return send_data(
            False,
            None,
            "项目配置不存在"
        )
    await delete_workflow(project_id=project_id, name=name)
    # 判断是否已经存在首次生成大纲和首次生成正文,如果存在则修改为再次生成大纲或者再次生成正文
    if name == 'GENERATED_OUTLINE_FIRST_TIME':
        # 查询工作流程
        workflow = await Workflow.filter(
            project_config_id=project_id,
            name='GENERATED_OUTLINE_FIRST_TIME'
        ).first()
        if workflow:
            name = 'REVISED_OUTLINE'
    # 判断是否已经存在首次生成大纲和首次生成正文
    if name == 'GENERATED_CONTENT_FIRST_TIME':
        # 查询工作流程
        workflow = await Workflow.filter(
            project_config_id=project_id,
            name='GENERATED_CONTENT_FIRST_TIME'
        ).first()
        if workflow:
            name = 'REVISED_CONTENT'
    # 创建新工作流程
    await Workflow.create(
        operator_id=current_user.id,
        content=content,
        order=Order[name].value,
        name=name,
        project_config_id=project_id,
        category=get_result_type(Order[name].value),
    )
    
    return True


@router.get("/project/{project_config_id}", response_model=ResponseModel[List[WorkflowResponse]])
async def get_workflows_by_project(
    project_config_id: UUID
):
    """
    获取项目下所有工作流程
    """
    # 验证项目配置存在
    project_config = await ProjectConfig.filter(id=project_config_id).first()
    if not project_config:
        return send_data(
            False,
            None,
            "项目配置不存在"
        )
    
    # 查询工作流程列表
    workflows = await Workflow.filter(project_config_id=project_config_id).prefetch_related("operator", "operator__role", "operator__organization").all()
    # 转换为响应数据
    result = []
    for workflow in workflows:
        temp = WorkflowResponse.model_validate(workflow).model_dump()
        try:
            temp["name"] = Name[workflow.name].value
        except:
            temp["name"] = workflow.name
        result.append(WorkflowResponse.model_validate(temp))
    
    return send_data(
        True,
        result,
        ""
    )

async def delete_workflow(
    project_id: UUID,
    name: str,
):
    """
    删除工作流程（软删除）
    """
    order = Order[name].value
    # 查询工作流程
    workflow = await Workflow.filter(project_config_id=project_id, is_deleted=0).all()
    for w in workflow:
        # 如果order大于等于传入的order，则软删除
        if w.order >= order: 
            # 软删除
            w.is_deleted = 1
            await w.save()
    
    return True
# 流式返回节点内容的接口（SSE）
@router.get("/{workflow_id}/stream")
async def stream_node_content(workflow_id: str):
    """
    流式返回节点内容的接口（SSE格式）
    """
    # 检查项目是否存在并验证权限
    workflow_db = await Workflow.filter(id=workflow_id).first()
    if not workflow_db:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '节点信息不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    if not workflow_db.content:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '节点内容为空', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    # 如果不是正在生成状态，则回退到从文件读取
    content_path = workflow_db.content
    
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(content_path, current_user), 
        media_type="text/event-stream"
    )