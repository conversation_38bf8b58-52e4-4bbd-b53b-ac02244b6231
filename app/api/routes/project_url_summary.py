from app.api.repository.project_url_summary import (
  get_urls_by_project_id,
  batch_create_urls,
  bind_project,
  get_one_url,
  get_urls_by_ids,
  detect_async_handle,
  stop_async_handle
)
from fastapi import APIRouter, Query, Body, Request, Depends
from typing import List
from pydantic import UUID4
from app.api.schemas.user import UserResponse
from app.api.schemas.project_url_summary import (
  ProjectUrlSummaryResponse,
  ProjectUrlSummaryCreate,
  ProjectUrlSummaryBind,
  UrlByIds
)
from app.utils.utils import send_data, ResponseModel
from app.api.deps import get_current_user_from_state
from app.utils.enum import ProjectUrlSummaryError
from app.api.repository.user import is_user_authed

router = APIRouter()

@router.post("/by-id", response_model=ResponseModel[List[ProjectUrlSummaryResponse]], summary="根据ID获取所有的链接信息")
async def api_get_urls_by_project_id(
  data: UrlByIds
):
    """
    根据ID获取所有链接信息
    """
    try:
        result = await get_urls_by_ids(
          url_ids=data.ids
        )
        return send_data(True, [ProjectUrlSummaryResponse.model_validate(item, from_attributes=True) for item in result])
    except Exception as e:
        return send_data(False, None, f"{ProjectUrlSummaryError.GET_LIST_FAIL.value}: {str(e)}")

@router.get("/by-project", response_model=ResponseModel[List[ProjectUrlSummaryResponse]], summary="获取项目所有链接信息")
async def api_get_urls_by_project_id(
    project_id: UUID4 = Query(..., description="项目ID")
):
    """
    根据项目ID获取所有链接信息
    """
    try:
        result = await get_urls_by_project_id(project_id)
        return send_data(True, [ProjectUrlSummaryResponse.model_validate(item, from_attributes=True) for item in result])
    except Exception as e:
        return send_data(False, None, f"{ProjectUrlSummaryError.GET_LIST_FAIL.value}: {str(e)}")

@router.post("/batch", response_model=ResponseModel[List[ProjectUrlSummaryResponse]], summary="批量插入项目URL并异步处理")
async def api_batch_create_urls(
  request: Request,
  data: ProjectUrlSummaryCreate
):
    """
    批量插入url到指定项目，并异步处理有效性
    """
    try:
        current_user = get_current_user_from_state(request)
        result = await batch_create_urls(
          url_list=data.url,
          name=data.name,
          current_user=current_user
        )
        return send_data(True, [ProjectUrlSummaryResponse.model_validate(item, from_attributes=True) for item in result])
    except Exception as e:
        return send_data(False, None, f"{ProjectUrlSummaryError.INSERT_BATCH_FAIL.value}: {str(e)}")

@router.post("/bind", response_model=ResponseModel[int], summary="批量绑定URL到项目")
async def api_bind_project(
  data: ProjectUrlSummaryBind,
  request: Request
):
    """
    批量绑定URL到指定项目
    """
    try:
        current_user = get_current_user_from_state(request)
        # 有权限进行操作的URL
        url_list: List[UUID4] = []
        for item in data.url_ids:
          url_item = await get_one_url(id=item)
          if is_user_authed(
            operator=current_user.id,
            resource_belong_user=url_item.user_id
          ):
            url_list.append(item)
        result = await bind_project(
          url_list=url_list,
          project_id=data.project_id
        )
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"{ProjectUrlSummaryError.URL_BIND_FAIL.value}: {str(e)}")

@router.post("/detect-async", response_model=ResponseModel[bool], summary="检测URL异步任务是否在运行")
async def api_detect_async_handle(
    data: UrlByIds
):
    """
    检测URL异步任务是否在运行
    """
    try:
        result = await detect_async_handle(data.ids)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"检测异步任务失败: {str(e)}")

@router.post("/stop-async", response_model=ResponseModel[bool], summary="停止URL异步任务")
async def api_stop_async_handle(
    data: UrlByIds
):
    """
    停止URL异步任务
    """
    try:
        result = await stop_async_handle(data.ids)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"停止异步任务失败: {str(e)}")

