from typing import List
from fastapi import APIRouter, Depends, HTTPException, status

from app.api.deps import get_current_user
from app.core.security import generate_api_key
from app.models.user import User
from app.models.api_key import APIKey
from app.api.schemas.api_key import APIKeyCreate, APIKeyResponse, APIKeyUpdate

router = APIRouter()


@router.post("/", response_model=APIKeyResponse, status_code=status.HTTP_201_CREATED, deprecated=True)
async def create_api_key(
    api_key_in: APIKeyCreate, current_user: User = Depends(get_current_user)
):
    """创建新的API密钥"""
    # 生成唯一的API Key
    key = generate_api_key()
    
    api_key = APIKey(
        key=key,
        name=api_key_in.name,
        quota=api_key_in.quota,
    )
    await api_key.save()
    return api_key


@router.get("/", response_model=List[APIKeyResponse], deprecated=True, summary="废弃接口")
async def read_api_keys(
    skip: int = 0, limit: int = 100, current_user: User = Depends(get_current_user)
):
    """获取所有API密钥"""
    api_keys = await APIKey.all().offset(skip).limit(limit)
    return api_keys


@router.get("/{api_key_id}", response_model=APIKeyResponse, deprecated=True, summary="废弃接口")
async def read_api_key(
    api_key_id: str, current_user: User = Depends(get_current_user)
):
    """获取特定API密钥"""
    api_key = await APIKey.filter(id=api_key_id).first()
    if api_key is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在",
        )
    return api_key


@router.patch("/{api_key_id}", response_model=APIKeyResponse, deprecated=True, summary="废弃接口")
async def update_api_key(
    api_key_in: APIKeyUpdate,
):
    """更新API密钥"""
    api_key = await APIKey.filter().first()
    if api_key is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在",
        )
    
    # 更新字段
    update_data = api_key_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(api_key, field, value)
    
    await api_key.save()
    return api_key


@router.delete("/{api_key_id}", status_code=status.HTTP_204_NO_CONTENT, deprecated=True, summary="废弃接口")
async def delete_api_key(
    api_key_id: str, current_user: User = Depends(get_current_user)
):
    """删除API密钥"""
    api_key = await APIKey.filter(id=api_key_id).first()
    if api_key is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在",
        )
    
    await api_key.delete()
    return None 