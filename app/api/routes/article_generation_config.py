from app.api.repository.article_generation_config import stream_article_generation_config_content
from app.api.schemas.project_members import ProjectMemberBase
from app.models.requirements_attachments_files import RequirementsAttachmentFiles
from app.models.research import Research, ResearchStatus
from fastapi import APIRouter, Depends, HTTPException, status, Request
from app.models.project_member_joins import ProjectMemberJoin

from fastapi.responses import StreamingResponse, FileResponse
from starlette.background import BackgroundTask
from tortoise.transactions import atomic
from datetime import datetime
import asyncio
import json
import os
from app.core.logging import get_logger
from enum import Enum
from app.models import User, ArticleGenerationConfig, ModelConfig
from app.api.schemas.article_generation_config import (
    ArticleGenerationConfigCreateRequest,
    ArticleGenerationConfigResponse
)
logger = get_logger(__name__)
from app.utils.llm_service import stream_llm_and_save
from app.utils.utils import (
    send_data,
    ResponseModel,
    stream_file_content_sse,
    handle_before_save,
    save_text_to_file,
    sanitize_filename,
    ContentStatus,
    stream_handle_before_send,
    remove_markdown_h1_and_text,
)
from app.utils.content_manager import ContentManager, Data
from app.services.llm_token_service import get_generation_result
from app.utils.enum import CallLLMFlag
from app.api.schemas.user import UserResponse
from app.api.deps import get_current_user_from_state
from app.api.schemas.role import InsetRole
from typing import Union, Optional, List
from app.models.user_report_usage import UserReportUsage
from app.core.config import settings
from docx import Document
from docx.shared import Inches
import tempfile

# 导入project_downloads中的convert_markdown_to_docx函数
from app.api.routes.project_downloads import convert_markdown_to_docx

# 第37行：移除 prefix，因为现在在 __init__.py 中设置
router = APIRouter(tags=["文章生成配置"])

# 内容管理器实例
outline_content_manager = ContentManager()

# 文章生成配置状态枚举
class ArticleGenerationStatus(str, Enum):
    OUTLINE_GENERATING = "outline_generating"
    OUTLINE_GENERATED = "outline_generated"
    OUTLINE_FAILED = "outline_failed"
    OUTLINE_CANCELED = "outline_canceled"
    CONTENT_GENERATING = "content_generating"
    CONTENT_GENERATED = "content_generated"
    CONTENT_FAILED = "content_failed"
    CONTENT_CANCELED = "content_canceled"

# 删除未使用的常量和枚举
# 删除第62-77行的 ARTICLE_OUTLINE_SYSTEM_PROMPT
# 删除第53-60行的 ArticleType 枚举

def replace_template_variables(template: str, config_db) -> str:
    """
    替换模板中的变量占位符
    支持 {{#field_name#}} 格式的变量替换
    """
    if not template:
        return template
    
    import re
    
    # 查找所有 {{#field_name#}} 格式的变量
    pattern = r'\{\{#(\w+)#\}\}'
    matches = re.findall(pattern, template)
    
    logger.info(f"找到的变量: {matches}")
    logger.info(f"原始模板: {template}")
    
    result = template
    for field_name in matches:
        # 获取配置对象中对应字段的值
        field_value = getattr(config_db, field_name, None)
        logger.info(f"变量 {field_name} 的值: {field_value}, 类型: {type(field_value)}")
        
        # 安全处理 None 值
        if field_value is not None and str(field_value).strip():
            # 将变量替换为实际值（非空值），不带花括号
            replacement = str(field_value)
            logger.info(f"替换 {field_name}: {{#{field_name}#}} -> {replacement}")
            # 使用正则表达式替换，确保完全匹配
            result = re.sub(rf'\{{{{#{re.escape(field_name)}#\}}}}', replacement, result)
            logger.info(f"替换后: {result}")
        else:
            # 如果字段不存在或为空，替换为空字符串
            logger.info(f"替换 {field_name}: {{#{field_name}#}} -> ''")
            result = re.sub(rf'\{{{{#{re.escape(field_name)}#\}}}}', '', result)
    
    logger.info(f"最终结果: {result}")
    return result

# 清理存储的辅助函数
async def remove_article_storage(config_id: str, flag='outline'):
    """清理文章生成配置的存储内容"""
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user", "model").first()
    
    if flag == 'outline':
        # 获取该配置的内容对象
        project_content = outline_content_manager.get_project_content(config_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                logger.info(f"已取消文章配置 {config_id} 的大纲生成任务")
            except Exception as e:
                error = f"取消文章大纲生成任务时出错: {str(e)}"
                logger.error(error)
            
            # 更新配置状态为已取消
            config_db.status = ArticleGenerationStatus.OUTLINE_CANCELED.value
            config_db.ai_generated_outline = None
            await config_db.save()
    
    elif flag == 'content':
        # 获取该配置的内容对象
        project_content = outline_content_manager.get_project_content(config_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                logger.info(f"已取消文章配置 {config_id} 的内容生成任务")
            except Exception as e:
                error = f"取消文章内容生成任务时出错: {str(e)}"
                logger.error(error)
            
            # 更新配置状态为已取消
            config_db.content_generation_status = ArticleGenerationStatus.CONTENT_CANCELED.value
            config_db.generated_content_file_path = None
            await config_db.save()

@router.post(
    "/save",
    summary="保存文章生成配置",
    response_model=ArticleGenerationConfigResponse,
    description="保存文章生成配置"
)
async def save_article_generation_config(
    article_generation_config: ArticleGenerationConfigCreateRequest,
    request: Request
):
    """
    保存文章生成配置
    """
    try:
        logger.info(f"保存文章生成配置:开始了")
        
        # 获取当前用户
        current_user_response = get_current_user_from_state(request)
        logger.info(f"current_user: {current_user_response}")
        
        # 从数据库获取用户对象（包含关联的机构信息）
        current_user = await User.filter(id=current_user_response.id).prefetch_related('organization').first()
        if not current_user:
            return send_data(False, None, "用户不存在")
        
        # 检查模型是否存在
        model = await ModelConfig.get_or_none(id=article_generation_config.model_id)
        if not model:
            logger.warning(f"用户 {current_user.username} 尝试使用不存在的模型 {article_generation_config.model_id}")
            return send_data(False, None, "选择的模型不存在")
        
        # 验证并获取 research 对象（如果提供 research_id）
        research = None
        if article_generation_config.research_id:
            from app.models.research import Research
            research = await Research.filter(id=article_generation_config.research_id).first()
            if not research:
                return send_data(False, None, "指定的研究不存在")
        
        # 处理枚举值转换
        application_category_value = await convert_enum_to_value('材料口径', article_generation_config.application_category)
        language_style_value = await convert_enum_to_value('语言风格', article_generation_config.language_style)
        
        # 处理用户添加的示例文件
        user_add_demo = None
        user_add_demo_context = None
        if article_generation_config.user_add_demo_id:
            try:
                # 导入 UploadFile 模型 - 使用正确的导入路径
                from app.models.upload_file import UploadFile
                
                # 查询上传文件信息
                upload_file = await UploadFile.filter(id=article_generation_config.user_add_demo_id).first()
                if upload_file:
                    user_add_demo = upload_file.id
                    logger.info(f"找到用户添加的示例文件: {upload_file.filename}")
                    
                    # 读取文件内容
                    try:
                        # 根据实际的文件路径字段名调整
                        file_path = getattr(upload_file, 'file_path', None) or getattr(upload_file, 'path', None)
                        if file_path and os.path.exists(file_path):
                            with open(file_path, 'r', encoding='utf-8') as f:
                                user_add_demo_context = f.read()
                            logger.info(f"成功读取示例文件内容，长度: {len(user_add_demo_context)}")
                        else:
                            logger.warning(f"示例文件路径不存在: {file_path}")
                            user_add_demo_context = ""
                    except Exception as e:
                        logger.error(f"读取示例文件内容失败: {str(e)}")
                        user_add_demo_context = ""
                else:
                    logger.warning(f"未找到ID为 {article_generation_config.user_add_demo} 的上传文件")
            except Exception as e:
                logger.error(f"处理用户添加的示例文件时出错: {str(e)}")
                user_add_demo = None
                user_add_demo_context = None
        
        try:
            logger.info(f"[generate_project_outline] 开始获取项目配置详情")
            logger.info(f"[generate_project_outline] 项目配置详情获取成功")
            logger.info(f"[generate_project_outline] 团队成员数量: {len(article_generation_config.team_members)}")
            article_generation_config.team_members_id = article_generation_config.team_members
            # 查询团队成员信息
            team_member_joins = []
            if article_generation_config.team_members:
                try:
                    logger.info(f'[save_article_config] 开始查询团队成员，IDs: {article_generation_config.team_members}')
                    team_member_joins = await ProjectMemberJoin.filter(
                        join_id=article_generation_config.team_members,
                        is_deleted=0
                    ).prefetch_related("member").all()
                    logger.info(f'[save_article_config] 团队成员查询成功，数量: {len(team_member_joins)}')
                except Exception as e:
                    logger.error(f'[save_article_config] 查询团队成员时发生异常: {str(e)}')
                    team_member_joins = []
            # 转换为 ProjectMemberBase 对象
            team_members = [
                ProjectMemberBase(
                    name=join.member.name,
                    title=join.member.title,
                    representative_works=join.member.representative_works,
                    organization=join.member.organization
                )
                for join in team_member_joins
            ]
            logger.info(f"[team_member_joins] 团队成员ID:逻辑结束{team_member_joins}")
            # 格式化团队成员信息为字符串
            team_members_text = "、".join([
                f"{item.name}{item.title or ''}" + 
                (f"，就职于{item.organization}" if item.organization else "") + 
                (f"，代表性成就有{item.representative_works}" if item.representative_works else "")
                for item in team_members
            ])
            
            logger.info(f"[team_members_text] 团队成员ID:逻辑结束----{team_members_text}")
            # 生成申报主体信息字符串（参考 prompts.py 的逻辑）
            logger.info(f"[article_generation_config.leader] 开始查询申报主体，ID:逻辑开始----{article_generation_config.leader}")
            leader_text = ""
            if article_generation_config.leader:
                # 这里需要根据 leader_id 获取申报主体信息
                # 假设有一个 Leader 模型，需要根据实际情况调整
                from app.models.project_leaders import ProjectLeader

                leader = await ProjectLeader.filter(id=article_generation_config.leader).first()
                if leader:
                    leader_text = f"{leader.name}, institution established date {leader.founded_date}, related projects: {leader.related_projects}"
            
           
            logger.info(f"[generate_project_outline] 申报主体数据处理完成----{leader_text}")
            logger.info(f"[generate_project_outline] 团队成员数据处理完成")
        except Exception as e:
            logger.error(f"[generate_project_outline] 获取项目配置详情时发生异常: {str(e)}")
            logger.error(f"[generate_project_outline] 异常详情: {type(e).__name__}")
            return send_data(False, None, f"获取项目配置失败: {str(e)}")

        #reference_materials
        requirements_attachments_text = None;
        
        # 处理 requirements_attachments_id - 使用新的绑定机制
        if article_generation_config.requirements_attachments:
            logger.info(f"[article_generation_config.requirements_attachments] 开始查询申报要求附件，ID:逻辑开始----{article_generation_config.requirements_attachments}")
            # 验证文件是否存在且未被删除
            requirements_attachments_files = await RequirementsAttachmentFiles.filter(
                id__in=article_generation_config.requirements_attachments, 
                is_deleted=False
            ).all()
            logger.info(f"开始查询申报要求附件----{requirements_attachments_files}")
            if requirements_attachments_files:
                logger.info(f"开始为文章配置绑定 {len(requirements_attachments_files)} 个文件")
                
                analysis_results = []
                for file in requirements_attachments_files:
                    # 收集分析结果
                    if file.analysis_result:
                        analysis_results.append(f"\n{file.analysis_result}")
                # 将分析结果组装成指定格式并存储
                requirements_attachments_text = "".join(analysis_results) if analysis_results else None
                logger.info(f"文章配置成功绑定参考资料，分析结果数量: {len(analysis_results)}")
        
        # 创建新的文章生成配置
        config = await ArticleGenerationConfig.create(
            user=current_user,  # 使用数据库用户对象
            organization=current_user.organization,  # 现在可以安全访问，因为已经 prefetch_related
            model=model,  # 使用数据库模型对象
            research=research,  # 关联的研究对象
            created_by=current_user,  # 使用数据库用户对象
            updated_by=current_user,  # 使用数据库用户对象
            
            # 信息配置
            name=article_generation_config.name,  # 主题（必填）
            team_members=team_members_text,  # 团队成员（格式化后的字符串）
            team_members_id=article_generation_config.team_members_id,  # 团队成员ID
            team_introduction=article_generation_config.team_introduction,  # 团队介绍
            reference_library=article_generation_config.reference_library,  # 参考文献库
            reference_library_urls=article_generation_config.reference_library_urls,  # 参考文献库URL列表
           
            leader_id=article_generation_config.leader,  # 申报主体ID
            leader_text=leader_text,  # 申报主体（格式化后的字符串）
            ai_leader_introduction=article_generation_config.ai_leader_introduction,  # 项目主体的AI介绍
            user_add_demo=user_add_demo,  # 用户添加的示例文件ID
            user_add_demo_context=user_add_demo_context,  # 用户添加的示例文件内容
            user_add_prompt=article_generation_config.user_add_prompt,  # 用户添加的提示词
            word_count_requirement=article_generation_config.word_count_requirement,  # 字数要求
            language_style=article_generation_config.language_style,  # 语言风格（转换为value值）
            application_category=article_generation_config.application_category,  # 申报口径（转换为value值）
            requirements_attachments_id=article_generation_config.requirements_attachments,  # 申报要求附件ID（JSON格式存储）
            requirements_attachments=requirements_attachments_text,  # 参考资料（从requirements_attachments分析结果汇总）
            
            # 搜索引擎配置
            search_engine=article_generation_config.search_engine,  # 搜索引擎选择
            search_list_count=article_generation_config.search_list_count,  # 搜索列表数
            search_iterations=article_generation_config.search_iterations,  # 搜索迭代次数
            retrieval_content_count=article_generation_config.retrieval_content_count,  # 检索内容数
            
            # 文献总结配置
            literature_summary_enabled=article_generation_config.literature_summary_enabled,  # 文献总结开关
            max_content_collection_count=article_generation_config.max_content_collection_count,  # 最大内容收集数量
            max_reference_count=article_generation_config.max_reference_count,  # 最大参考文献数
            
            # 模型参数配置
            temperature=article_generation_config.temperature,  # 温度参数
            top_p=article_generation_config.top_p,  # Top-p参数
            top_k=article_generation_config.top_k,  # Top-k参数
            
            # 提示词管理
            outline_system_prompt=article_generation_config.outline_system_prompt,  # 大纲的系统提示词
            content_system_prompt=article_generation_config.content_system_prompt,  # 正文的系统提示词
            outline_user_prompt=article_generation_config.outline_user_prompt,  # 大纲的用户提示词
            content_user_prompt=article_generation_config.content_user_prompt  # 正文的用户提示词
        )
        
        logger.info(f"用户 {current_user.username} 成功创建文章生成配置 {config.id}")
        
        # 返回创建结果
        return ArticleGenerationConfigResponse(
            id=str(config.id),
            name=config.name,
            research_id=str(config.research.id) if config.research else None,
            created_at=config.created_at.isoformat() if config.created_at else None,
            status="保存成功"
        )
        
    except Exception as e:
        logger.error(f"保存文章生成配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存失败: {str(e)}"
        )

async def convert_enum_to_value(category: str, label: str) -> str:
    """
    根据category和label查询字典表，返回对应的value值
    """
    try:
        # 导入字典表模型
        from app.models.dictionary import Dictionary
        
        if not label:
            return ""
        
        # 查询字典表
        dictionary = await Dictionary.filter(
            label=label,
            category=category,
            is_deleted=False
        ).first()
        
        if dictionary and dictionary.value:
            logger.info(f"找到字典项: {label} -> {dictionary.value}")
            return dictionary.value
        else:
            logger.warning(f"未找到字典项: {label} (category: {category})")
            return label  # 如果没找到，返回原值
            
    except Exception as e:
        logger.error(f"查询字典表失败: {e}")
        return label  # 出错时返回原值

# 生成文章大纲接口
@router.post("/{config_id}/generate-outline", response_model=ResponseModel[ArticleGenerationConfigResponse])
async def generate_article_outline(
    config_id: str,
    current_user: User = Depends(get_current_user_from_state)  # 使用依赖注入
):
    """
    生成文章大纲
    """
    logger.info(f"生成文章大纲:开始了")
    # 检查用户报告使用次数限制
    user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
    if not user_obj:
        return send_data(False, None, "用户不存在")
    
   
    # 获取文章生成配置
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user", "model").first()
    if not config_db:
        return send_data(False, None, "文章生成配置不存在")
    
    # 权限检查
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, None, "无权访问此配置")
    
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
    
    # 获取模型信息
    model = config_db.model
    api_key = model.api_key
    api_url = model.api_url
    model_name = model.model_name
    
    # 取消之前的任务
    await remove_article_storage(config_id=config_id)
    
    # 重新计算预估时间
    try:
        logger.info(f"文章配置 {config_db.id} 预估时间重新计算开始")
        config_db.updated_at = datetime.now()
        await config_db.save()
        logger.info(f"文章配置 {config_db.id} 预估时间重新计算完成")
    except Exception as e:
        logger.warning(f"文章配置 {config_db.id} 预估时间重新计算失败: {e}")
      # 获取config_response用于生成提示词
 
    # 准备文件路径
    project_folder = f"llm_file/article/{config_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"outline_{timestamp}.txt"
    if config_db.name:
        file_name = sanitize_filename(f"outline_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    
    # 创建空文件
    save_text_to_file(content="", file_path=relative_path)
    
    # 调用LLM生成大纲
    # 处理系统提示词中的变量替换
    system_prompt = replace_template_variables(config_db.outline_system_prompt, config_db)
    user_prompt = replace_template_variables(config_db.outline_user_prompt, config_db) if config_db.outline_user_prompt else ""
    
    logger.info(f"文章大纲系统提示词：{system_prompt}")
    logger.info(f"文章大纲用户提示词：{user_prompt}")
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}  # 如果用户提示词为空，则使用系统提示词
    ]
    
    logger.info(f"文章大纲系统提示词：{messages}")

    # 完成回调
    async def complete_callback(open_router_id: str, all_content: str):
        # 重新从数据库获取配置对象，确保状态是最新的
        fresh_config = await ArticleGenerationConfig.filter(id=config_id).first()
        if not fresh_config:
            logger.error(f"配置 {config_id} 不存在，无法完成回调")
            return
        
        fresh_config.outline_generation_status = ArticleGenerationStatus.OUTLINE_GENERATED.value
        fresh_config.outline_generation_time = datetime.now()
        # 清空之前的内容 - 这些字段在模型中不存在，需要删除或添加字段
  
        
        logger.info(f"生成文章大纲:完成了{all_content}")
        # 保存大纲内容到文件
        save_text_to_file(
            handle_before_save(remove_markdown_h1_and_text(all_content, fresh_config.name)), 
            relative_path
        )
        
        # 保存生成的大纲文件路径
        fresh_config.generated_outline_file_path = relative_path
        logger.info(f"生成文章大纲地址:完成了{fresh_config.generated_outline_file_path}")
      
        # 获取并保存Token消耗 - 这个字段在模型中不存在
        try:
            temp = await get_generation_result(
                generation_id=open_router_id,
                api_key=api_key
            )
            fresh_config.outline_tokens_consumed = temp.data.tokens_completion + temp.data.tokens_prompt
        except Exception as e:
            logger.error(f"获取Token消耗失败: {e}")
            fresh_config.outline_tokens_consumed = 0
        
        await fresh_config.save()
    
    # 错误回调
    async def error_callback(error: str):
        logger.error(f"生成文章大纲时发生错误: {error}")
        outline_content_manager.add_content(config_id, error, ContentStatus.ERROR)
        config_db.status = ArticleGenerationStatus.OUTLINE_FAILED.value
        await config_db.save()
    
    # 流式回调
    async def callback(content: str):
        outline_content_manager.add_content(config_id, content, ContentStatus.NORMAL)
    
    # 添加空字符串触发流式处理
    outline_content_manager.add_content(config_id, "", ContentStatus.NORMAL)
    
    # 创建异步任务
    task = asyncio.create_task(stream_llm_and_save(
        messages=messages,
        model=model_name,
        apiKey=api_key,
        apiUrl=api_url,
        user=current_user,
        flag=CallLLMFlag.GENERATE_OUTLINE.value,
        callback=callback,
        complete_callback=complete_callback,
        error_callback=error_callback,
        related_id=config_db.id
    ))
    
    # 将任务实例保存到内容管理器
    outline_content_manager.add_asyncio(config_id, task)
    
    # 更新配置状态
    config_db.ai_generated_outline = relative_path
    config_db.status = ArticleGenerationStatus.OUTLINE_GENERATING.value
    await config_db.save()
    
    return send_data(True, {
        "id": str(config_db.id),
        "name": config_db.name,
        "status": config_db.status,
        "outline_path": relative_path
    }, "文章大纲生成已开始")

# 流式返回文章大纲内容的接口（SSE）
@router.get("/{config_id}/stream-outline")
async def stream_article_outline(
    config_id: str,
    request: Request
):
    """
    流式返回文章大纲内容（SSE格式）
    """
    current_user = get_current_user_from_state(request)
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user").first()
    
    if not config_db:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '文章生成配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 权限检查
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '无权访问此配置', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 检查正在生成的内容
    if config_db.status == ArticleGenerationStatus.OUTLINE_GENERATING.value:
        async def stream_realtime_content():
            read_count = 0
            project_content = outline_content_manager.get_project_content(config_id)
            
            if not project_content:
                config_db.status = ArticleGenerationStatus.OUTLINE_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                return
            
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
            
            # 然后持续检查是否有新内容
            while config_db.status == ArticleGenerationStatus.OUTLINE_GENERATING.value:
                chunk = outline_content_manager.read_next_chunk(config_id)
                if chunk:
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                    read_count += 1
                else:
                    await asyncio.sleep(0.5)
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
            
            # 获取剩余未读取的内容
            project_content = outline_content_manager.get_project_content(config_id)
            if project_content and project_content.unread_chunks:
                for item in project_content.unread_chunks:
                    yield f"data: {json.dumps({'content': item.content, 'status': item.status})}\n\n"
                read_count += len(project_content.unread_chunks)
            
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
        
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    
    # 如果不是正在生成状态，则从文件读取
    outline_path = getattr(config_db, 'manual_modified_outline', None) or getattr(config_db, 'ai_generated_outline', None)
    if not outline_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '文章尚未生成大纲', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 清空内存中的内容
    outline_content_manager.clear_project(config_id)
    
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(outline_path, current_user), 
        media_type="text/event-stream"
    )

# 终止文章大纲生成接口
@router.post("/{config_id}/stop-outline", response_model=ResponseModel[bool])
async def stop_article_outline_generation(
    config_id: str, 
    request: Request
):
    """终止文章大纲生成"""
    current_user = get_current_user_from_state(request)
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user").first()
    
    if not config_db:
        return send_data(False, False, "文章生成配置不存在")
    
    # 权限检查
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, False, "无权访问此配置")
    
    # 检查当前状态
    if config_db.status != ArticleGenerationStatus.OUTLINE_GENERATING.value:
        return send_data(False, False, "当前没有正在生成大纲")
    
    try:
        # 获取该项目的内容对象
        project_content = outline_content_manager.get_project_content(config_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消文章大纲生成任务"
                logger.info(f"已取消文章配置 {config_id} 的大纲生成任务")
                outline_content_manager.add_content(config_id, error, ContentStatus.ERROR)
            except Exception as e:
                error = f"取消文章大纲生成任务时出错: {str(e)}"
                outline_content_manager.add_content(config_id, error, ContentStatus.ERROR)
                logger.error(error)
        
        # 更新配置状态为已取消
        config_db.status = ArticleGenerationStatus.OUTLINE_CANCELED.value
        config_db.ai_generated_outline = None
        await config_db.save()
        
        return send_data(True, True, "已终止文章大纲生成")
    except Exception as e:
        return send_data(False, False, f"终止文章大纲生成失败: {str(e)}") 

# 生成文章内容接口
@router.post("/{config_id}/generate-content", response_model=ResponseModel[ArticleGenerationConfigResponse])
async def generate_article_content(
    config_id: str,
    request: Request,
    data: Optional[dict] = None
):
    """
    生成文章内容
    """
    summary_literature = data.get('summary_literature', False) if data else False
    current_user = get_current_user_from_state(request)
    starttime = datetime.now()
    logger.info(f"文章内容开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    is_diff_count = False
    
    # 检查用户报告使用次数限制
    user_obj = await User.filter(id=current_user.id, is_deleted=False).prefetch_related('role', 'organization').first()
    if not user_obj:
        return send_data(False, None, "用户不存在")
    

    # 获取文章生成配置
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user", "model").first()
    if not config_db:
        return send_data(False, None, "文章生成配置不存在")
    
    # 权限检查
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, None, "无权访问此配置")
    
    if config_db.model is None:
        return send_data(False, None, "请先配置模型")
    
    # 获取模型信息
    model = config_db.model
    api_key = model.api_key
    api_url = model.api_url
    model_name = model.model_name
    
    # 取消之前的任务
    await remove_article_storage(config_id=config_id, flag='content')
    
    # 检查是否已有大纲
    outline_path = getattr(config_db, 'manual_modified_outline', None) or getattr(config_db, 'ai_generated_outline', None) or config_db.generated_outline_file_path
    if not outline_path:
        return send_data(False, None, "请先生成文章大纲")
    
    outline: str = ""
    # 从文件中读取大纲内容
    try:
        full_path = os.path.join(os.getcwd(), outline_path)
        if not os.path.exists(full_path):
            return send_data(False, None, f"大纲文件不存在: {outline_path}")
            
        with open(full_path, "r", encoding="utf-8") as f:
            outline = f.read()
            
        if not outline:
            return send_data(False, None, "大纲文件内容为空")
    except Exception as e:
        return send_data(False, None, f"读取大纲文件失败: {str(e)}")
    
    # 准备文件路径
    project_folder = f"llm_file/article/{config_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"content_{timestamp}.txt"
    if config_db.name:
        file_name = sanitize_filename(f"content_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    
    # 创建空文件
    save_text_to_file(content="", file_path=relative_path)
    
    # 重新计算预估时间
    try:
        logger.info(f"文章配置 {config_db.id} 预估时间重新计算开始")
        config_db.updated_at = datetime.now()
        await config_db.save()
        logger.info(f"文章配置 {config_db.id} 预估时间重新计算完成")
    except Exception as e:
        logger.warning(f"文章配置 {config_db.id} 预估时间重新计算失败: {e}")
    
    # 完成回调
    async def complete_callback(open_router_id: str, all_content: str):
        # 重新从数据库获取配置对象，确保状态是最新的
        fresh_config = await ArticleGenerationConfig.filter(id=config_id).first()
        if not fresh_config:
            logger.error(f"配置 {config_id} 不存在，无法完成回调")
            return
        
        logger.info("准备把文章内容写到文件里面了。")
        outline_content_manager.clear_project(config_id)
        logger.info(all_content)
        save_text_to_file(handle_before_save(remove_markdown_h1_and_text(all_content, fresh_config.name)), relative_path)
        logger.info("文章内容已经写到文件里面了。")
        
        # 获取并保存Token消耗
        try:
            temp = await get_generation_result(
                generation_id=open_router_id,
                api_key=api_key
            )
            fresh_config.content_tokens_consumed = temp.data.tokens_completion + temp.data.tokens_prompt
        except Exception as e:
            logger.error(f"获取Token消耗失败: {e}")
            fresh_config.content_tokens_consumed = 0
        
        # 更新配置状态
        fresh_config.content_generation_status = ArticleGenerationStatus.CONTENT_GENERATED.value
        fresh_config.content_generation_time = datetime.now()
        fresh_config.generated_content_file_path = relative_path
        await fresh_config.save()
        
        endtime = datetime.now()
        logger.info(f"文章内容：{fresh_config.id}生成结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
    
    # 错误回调
    async def error_callback(error: str):
        logger.error(f"生成文章内容时发生错误: {error}")
        outline_content_manager.add_content(config_id, error, ContentStatus.ERROR)
        config_db.content_generation_status = ArticleGenerationStatus.CONTENT_FAILED.value
        await config_db.save()
    
    # 流式回调
    async def callback(content: str):
        nonlocal is_diff_count
       
        outline_content_manager.add_content(config_id, content, ContentStatus.NORMAL)
    
    # 添加空字符串触发流式处理
    outline_content_manager.add_content(config_id, "", ContentStatus.NORMAL)
    
    # 处理系统提示词中的变量替换
    system_prompt = config_db.content_system_prompt or ""
    user_prompt = config_db.content_user_prompt or ""
    logger.info(f"文章内容系统提示词：{system_prompt}")
    logger.info(f"文章内容用户提示词：{user_prompt}")
    # 确保不为 None 后再进行变量替换
    if system_prompt:
        system_prompt = replace_template_variables(system_prompt, config_db)
    if user_prompt:
        user_prompt = replace_template_variables(user_prompt, config_db)

    logger.info(f"文章内容系统提示词：{system_prompt}")
    logger.info(f"文章内容用户提示词：{user_prompt}")
    
    research = await Research.create(
            query=config_db.name,
            search_queries=[],
            contexts=[],
            status=ResearchStatus.PENDING
        )
    config_db.research = research
    
    # 创建异步任务
    task = asyncio.create_task(stream_article_generation_config_content(
        current_user=current_user,
        research=research,
        config_response=config_db,
        complete_callback=complete_callback,
        callback=callback,
        error_callback=error_callback,
        api_key=api_key,
        api_url=api_url,
        model=model,
        open_literature_summary=config_db.literature_summary_enabled
    ))
    
    # 将任务实例保存到内容管理器
    outline_content_manager.add_asyncio(config_id, task)
    
    # 更新配置状态
    config_db.generated_content_file_path = relative_path
    config_db.content_generation_status = ArticleGenerationStatus.CONTENT_GENERATING.value
    await config_db.save()
    
    return send_data(True, {
        "id": str(config_db.id),
        "name": config_db.name,
        "status": config_db.content_generation_status,
        "content_path": relative_path
    }, "文章内容生成已开始")

# 流式返回文章内容的接口（SSE）
@router.get("/{config_id}/stream-content")
async def stream_article_content(
    config_id: str,
    request: Request
):
    """
    流式返回文章内容（SSE格式）
    """
    current_user = get_current_user_from_state(request)
    logger.info(f"流式返回文章内容（SSE格式），配置ID: {config_id}")
    
    # 检查配置是否存在并验证权限
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user").first()
    if not config_db:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '文章生成配置不存在', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 权限检查
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '无权访问此配置', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    # 检查正在生成的内容
    if config_db.content_generation_status == ArticleGenerationStatus.CONTENT_GENERATING.value:
        # 如果是体验用户
        if current_user.is_trial:
            already_generated_text = send_trail_text(config_id, 'content')
            if already_generated_text:
                logger.info("体验用户的文章内容截断啦！")
                return StreamingResponse(
                    content=stream_fn(already_generated_text),
                    media_type="text/event-stream"
                )
        
        async def stream_realtime_content():
            send_length = 0
            read_count = 0
            project_content = outline_content_manager.get_project_content(config_id)
            
            if not project_content:
                config_db.content_generation_status = ArticleGenerationStatus.CONTENT_FAILED.value
                await config_db.save()
                yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                return
            
            # 先发送已有的内容
            for chunk in project_content.read_chunks:
                send_length += calculate_length(chunk)
                yield yield_self(chunk, send_length, current_user)
            
            # 然后持续检查是否有新内容
            while config_db.content_generation_status == ArticleGenerationStatus.CONTENT_GENERATING.value:
                chunk = outline_content_manager.read_next_chunk(config_id)
                if chunk:
                    send_length += calculate_length(chunk)
                    yield yield_self(chunk, send_length, current_user)
                    read_count += 1
                else:
                    await asyncio.sleep(5)
                    await config_db.refresh_from_db()
                    yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT.value})}\n\n"
            
            # 获取剩余未读取的内容
            project_content = outline_content_manager.get_project_content(config_id)
            if project_content and project_content.unread_chunks:
                for chunk in project_content.unread_chunks:
                    yield yield_self(chunk=chunk, send_length=send_length, current_user=current_user)
                read_count += len(project_content.unread_chunks)
            
            logger.info("\n\n\n我要清空本地数据啦！\n\n\n")
            # 生成已完成，发送完成状态
            yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
            outline_content_manager.clear_project(config_id)
        
        return StreamingResponse(
            content=stream_realtime_content(),
            media_type="text/event-stream"
        )
    
    # 如果不是正在生成状态，则从文件读取
    content_path = getattr(config_db, 'manual_modified_content', None) or getattr(config_db, 'ai_generated_content', None) or config_db.generated_content_file_path
    if not content_path:
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': '文章内容生成失败', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )
    
    logger.info("\n\n\n我要清空本地数据啦2！\n\n\n")
    # 返回SSE流式响应（从文件读取）
    return StreamingResponse(
        content=stream_file_content_sse(content_path, current_user, True), 
        media_type="text/event-stream",
        background=BackgroundTask(lambda: outline_content_manager.clear_project(config_id))
    )

# 终止文章内容生成接口
@router.post("/{config_id}/stop-content", response_model=ResponseModel[bool])
async def stop_article_content_generation(
    config_id: str,
    request: Request
):
    """终止文章内容生成"""
    current_user = get_current_user_from_state(request)
    config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user").first()
    
    if not config_db:
        return send_data(False, False, "文章生成配置不存在")
    
    # 权限检查
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, False, "无权访问此配置")
    
    # 检查当前状态
    if config_db.content_generation_status != ArticleGenerationStatus.CONTENT_GENERATING.value:
        return send_data(False, False, "当前没有正在生成内容")
    
    try:
        # 获取该配置的内容对象
        project_content = outline_content_manager.get_project_content(config_id)
        
        # 如果存在异步任务，尝试取消
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error = f"已取消文章内容生成任务"
                outline_content_manager.add_content(config_id, error, ContentStatus.ERROR)
                logger.info(f"已取消文章配置 {config_id} 的内容生成任务")
            except Exception as e:
                error = f"取消文章内容生成任务时出错: {str(e)}"
                outline_content_manager.add_content(config_id, error, ContentStatus.ERROR)
                logger.error(error)
        
        # 更新配置状态为已取消
        config_db.content_generation_status = ArticleGenerationStatus.CONTENT_CANCELED.value
        config_db.content_generation_time = None
        config_db.generated_content_file_path = None
        await config_db.save()
        
        return send_data(True, True, "已终止文章内容生成")
    except Exception as e:
        return send_data(False, False, f"终止文章内容生成失败: {str(e)}")

# 需要添加的辅助函数
def send_trail_text(config_id: str, flag: str):
    """返回体验用户看到的文本"""
    data = outline_content_manager.get_project_content(config_id)
    if data:
        list_data = data.read_chunks + data.unread_chunks
        content = ""
        for item in list_data:
            if item.status == ContentStatus.NORMAL and len(content) <= settings.TRIAL_USER_MAX_TEXT:
                content += item.content
        if len(content) >= settings.TRIAL_USER_MAX_TEXT:
            return stream_handle_before_send(content)
        else:
            return ""
    else:
        return ""

def calculate_length(chunk: Data):
    """计算内容长度"""
    if chunk.status == ContentStatus.NORMAL:
        return len(chunk.content) if chunk.content else 0
    return 0

def yield_self(chunk: Data, send_length: int, current_user: UserResponse):
    """根据用户类型返回内容"""
    if send_length >= settings.TRIAL_USER_MAX_TEXT and current_user.is_trial:
        return f"data: {json.dumps({'status': 'completed'})}\n\n"
    else:
        return f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"

def stream_fn(text):
    """流式返回函数"""
    yield f"data: {json.dumps({'content': text, 'status': ContentStatus.NORMAL})}\n\n"
    yield f"data: {json.dumps({'status': 'completed'})}\n\n" 

@router.get("/{config_id}/export-config")
async def export_article_generation_config(
    config_id: str,
    request: Request
):
    """
    导出文章生成配置参数为Word文档
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求导出配置 {config_id}")
        
        # 获取文章生成配置
        config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related(
            "user", "model", "research", "organization"
        ).first()
        
        if not config_db:
            return send_data(False, None, "文章生成配置不存在")
        
        # 权限检查
        if (config_db.user.id != current_user.id
            and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
        ):
            return send_data(False, None, "无权访问此配置")
        
        # 创建Word文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading(f'文章生成配置参数导出', 0)
        title.alignment = 1  # 居中对齐
        
        # 添加导出信息
        doc.add_paragraph(f'导出时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        doc.add_paragraph(f'配置名称: {config_db.name or "未设置"}')
        doc.add_paragraph(f'创建用户: {config_db.user.username}')
        doc.add_paragraph(f'所属机构: {config_db.organization.name if config_db.organization else "未设置"}')
        doc.add_paragraph('')  # 空行
        
        # 基本信息配置
        doc.add_heading('基本信息配置', level=1)
        basic_info = [
            ('配置名称', config_db.name),
            ('目标受众', config_db.target_audience),
            ('分析方法', config_db.analysis_method),
            ('行业类型', config_db.industry_type),
            ('总投资额', config_db.total_investment_amount),
            ('财务分析', config_db.financial_analysis),
            ('风险评估', config_db.risk_assessment),
            ('研究领域', config_db.research_field),
            ('材料主体', config_db.material_subject),
            ('团队成员', config_db.team_members),
            ('团队介绍', config_db.team_introduction),
            ('参考文献库', config_db.reference_library),
            ('参考资料', config_db.requirements_attachments),
            ('自定义模版', config_db.user_add_demo),
            ('额外信息补充', config_db.user_add_prompt),
            ('用户添加的提示词', config_db.user_add_prompt),
            ('字数要求', config_db.word_count_requirement),
            ('语言风格', config_db.language_style),
            ('申报口径', config_db.application_category),
            ('申报要求附件', config_db.requirements_attachments),
        ]
        
        for field_name, field_value in basic_info:
            if field_value:
                doc.add_paragraph(f'{field_name}: {field_value}')
        
        # 搜索引擎配置
        doc.add_heading('搜索引擎配置', level=1)
        search_info = [
            ('搜索引擎选择', config_db.search_engine),
            ('搜索列表数', config_db.search_list_count),
            ('搜索迭代次数', config_db.search_iterations),
            ('检索内容数', config_db.retrieval_content_count),
        ]
        
        for field_name, field_value in search_info:
            if field_value is not None:
                doc.add_paragraph(f'{field_name}: {field_value}')
        
        # 文献总结配置
        doc.add_heading('文献总结配置', level=1)
        literature_info = [
            ('文献总结开关', '开启' if config_db.literature_summary_enabled else '关闭'),
            ('最大内容收集数量', config_db.max_content_collection_count),
            ('最大参考文献数', config_db.max_reference_count),
        ]
        
        for field_name, field_value in literature_info:
            if field_value is not None:
                doc.add_paragraph(f'{field_name}: {field_value}')
        
        # 模型参数配置
        doc.add_heading('模型参数配置', level=1)
        model_info = [
            ('模型名称', config_db.model.model_name if config_db.model else '未设置'),
            ('API地址', config_db.model.api_url if config_db.model else '未设置'),
            ('温度参数', config_db.temperature),
            ('Top-p参数', config_db.top_p),
            ('Top-k参数', config_db.top_k),
        ]
        
        for field_name, field_value in model_info:
            if field_value is not None:
                doc.add_paragraph(f'{field_name}: {field_value}')
        
        # 提示词管理
        doc.add_heading('提示词管理', level=1)
        prompt_info = [
            ('大纲系统提示词', config_db.outline_system_prompt),
            ('正文系统提示词', config_db.content_system_prompt),
            ('大纲用户提示词', config_db.outline_user_prompt),
            ('正文用户提示词', config_db.content_user_prompt),
        ]
        
        for field_name, field_value in prompt_info:
            if field_value:
                doc.add_paragraph(f'{field_name}:')
                doc.add_paragraph(f'{field_value}')
                doc.add_paragraph('')  # 空行
        
        # 生成状态信息
        doc.add_heading('生成状态信息', level=1)
        status_info = [
            ('大纲生成状态', config_db.outline_generation_status),
            ('大纲生成时间', config_db.outline_generation_time.strftime("%Y-%m-%d %H:%M:%S") if config_db.outline_generation_time else '未生成'),
            ('大纲Token消耗', config_db.outline_tokens_consumed),
            ('内容生成状态', config_db.content_generation_status),
            ('内容生成时间', config_db.content_generation_time.strftime("%Y-%m-%d %H:%M:%S") if config_db.content_generation_time else '未生成'),
            ('内容Token消耗', config_db.content_tokens_consumed),
            ('生成的大纲文件路径', config_db.generated_outline_file_path),
            ('生成的内容文件路径', config_db.generated_content_file_path),
        ]
        
        for field_name, field_value in status_info:
            if field_value:
                doc.add_paragraph(f'{field_name}: {field_value}')
        
        # 时间信息
        doc.add_heading('时间信息', level=1)
        time_info = [
            ('创建时间', config_db.created_at.strftime("%Y-%m-%d %H:%M:%S") if config_db.created_at else '未设置'),
            ('更新时间', config_db.updated_at.strftime("%Y-%m-%d %H:%M:%S") if config_db.updated_at else '未设置'),
            ('创建者', config_db.user.username if config_db.user else '未设置'),
            ('更新者', config_db.user.username if config_db.user else '未设置'),
        ]
        
        for field_name, field_value in time_info:
            if field_value:
                doc.add_paragraph(f'{field_name}: {field_value}')
        
        # 保存到临时文件
        temp_dir = tempfile.gettempdir()
        filename = f"article_config_{config_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(temp_dir, filename)
        
        doc.save(filepath)
        
        logger.info(f"用户 {current_user.username} 成功导出配置 {config_id} 到文件 {filepath}")
        
        # 返回文件
        return FileResponse(
            path=filepath,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            background=BackgroundTask(lambda: os.remove(filepath) if os.path.exists(filepath) else None)
        )
        
    except Exception as e:
        logger.error(f"导出文章生成配置失败: {str(e)}")
        return send_data(False, None, f"导出失败: {str(e)}") 


# 下载文章大纲的Word文档格式的接口（基于Pandoc实现）
@router.get("/{config_id}/download-outline-docx")
async def download_article_outline_as_docx(
    config_id: str,
    request: Request
):
    """
    使用Pandoc下载文章大纲文件为Word文档格式
    
    Args:
        config_id: 文章生成配置ID
        request: 请求对象
        
    Returns:
        FileResponse: 可下载的Word格式大纲文件
    """
    try:
        import pypandoc
        
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求下载文章大纲Word文档 {config_id}")
        
        # 获取文章生成配置
        config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user").first()
        if not config_db:
            return send_data(False, None, "文章生成配置不存在")
        
        # 权限检查
        if (config_db.user.id != current_user.id
            and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
        ):
            return send_data(False, None, "无权访问此配置")
        
        # 检查大纲状态和文件是否存在
        if config_db.outline_generation_status not in [
            ArticleGenerationStatus.OUTLINE_GENERATED.value,
            ArticleGenerationStatus.CONTENT_GENERATING.value,
            ArticleGenerationStatus.CONTENT_GENERATED.value
        ]:
            return send_data(False, None, "大纲尚未生成完成或生成失败")
        
        # 获取大纲文件路径
        outline_path = config_db.generated_outline_file_path
        if not outline_path:
            return send_data(False, None, "大纲文件路径不存在")
        
        # 处理文件路径
        if os.path.isabs(outline_path):
            abs_file_path = outline_path
        else:
            abs_file_path = os.path.join(os.getcwd(), outline_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_file_path):
            return send_data(False, None, f"大纲文件不存在: {outline_path}")
        
        # 生成下载文件名
        if config_db.name:
            date_str = datetime.now().strftime("%Y-%m-%d")
            download_filename = f"outline_{config_db.name.replace(' ', '_')}_{date_str}.docx"
        else:
            download_filename = f"outline_{datetime.now().strftime('%Y-%m-%d')}.docx"
        
        # 使用通用函数处理转换
        try:
            temp_docx_file, _ = convert_markdown_to_docx(abs_file_path, download_filename)
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            return send_data(False, None, f"文档处理失败: {str(e)}")
        
        logger.info(f"用户 {current_user.username} 成功下载文章大纲Word文档 {config_id}")
        
        # 返回文件下载响应
        return FileResponse(
            path=temp_docx_file,
            filename=download_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
        
    except ImportError as e:
        logger.error(f"导入pypandoc失败: {str(e)}")
        return send_data(False, None, "需要安装pypandoc库才能使用此功能。请运行 'pip install pypandoc'")
    except Exception as e:
        logger.error(f"下载文章大纲Word文档失败: {str(e)}")
        return send_data(False, None, f"下载失败: {str(e)}")

# 下载文章内容的Word文档格式的接口
@router.get("/{config_id}/download-content-docx")
async def download_article_content_as_docx(
    config_id: str,
    request: Request
):
    """
    下载文章内容文件为Word文档格式
    
    Args:
        config_id: 文章生成配置ID
        request: 请求对象
        
    Returns:
        FileResponse: 可下载的Word格式内容文件
    """
    try:
        import pypandoc
        
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求下载文章内容Word文档 {config_id}")
        
        # 获取文章生成配置
        config_db = await ArticleGenerationConfig.filter(id=config_id).prefetch_related("user").first()
        if not config_db:
            return send_data(False, None, "文章生成配置不存在")
       
        # 权限检查
        if (config_db.user.id != current_user.id
            and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
        ):
            return send_data(False, None, "无权访问此配置")
        
        # 检查内容状态和文件是否存在
        if config_db.content_generation_status not in [
            ArticleGenerationStatus.CONTENT_GENERATED.value
        ]:
            return send_data(False, None, "文章内容尚未生成完成或生成失败")
        
        # 获取内容文件路径
        content_path = config_db.generated_content_file_path
        if not content_path:
            return send_data(False, None, "内容文件路径不存在")
        
        # 处理文件路径
        if os.path.isabs(content_path):
            abs_file_path = content_path
        else:
            abs_file_path = os.path.join(os.getcwd(), content_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_file_path):
            return send_data(False, None, f"内容文件不存在: {content_path}")
        
        # 生成下载文件名
        if config_db.name:
            date_str = datetime.now().strftime("%Y-%m-%d")
            download_filename = f"content_{config_db.name.replace(' ', '_')}_{date_str}.docx"
        else:
            download_filename = f"content_{datetime.now().strftime('%Y-%m-%d')}.docx"
        
        # 使用通用函数处理转换
        try:
            temp_docx_file, _ = convert_markdown_to_docx(abs_file_path, download_filename)
        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            return send_data(False, None, f"文档处理失败: {str(e)}")
        
        logger.info(f"用户 {current_user.username} 成功下载文章内容Word文档 {config_id}")
        
        # 返回文件下载响应
        return FileResponse(
            path=temp_docx_file,
            filename=download_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
        )
        
    except ImportError as e:
        logger.error(f"导入pypandoc失败: {str(e)}")
        return send_data(False, None, "需要安装pypandoc库才能使用此功能。请运行 'pip install pypandoc'")
    except Exception as e:
        logger.error(f"下载文章内容Word文档失败: {str(e)}")
        return send_data(False, None, f"下载失败: {str(e)}") 

