from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, UUID4
from app.models.research import Research



# 路由定义
router = APIRouter()



# 响应模型
class ResearchResponse(BaseModel):
    id: UUID4
    literatures: Any
    contexts: Any
    

# 研究列表响应模型
class ResearchListResponse(BaseModel):
    researches: List[ResearchResponse]

@router.get("/{research_id}")
async def get_research(
    research_id: str,
):
    """
    获取研究详情
    
    Args:
        research_id: 研究ID
        api_key: API密钥对象
    
    Returns:
        研究详情信息
    """
    # 获取研究
    research = await Research.filter(id=research_id).first()
    if not research:
        raise HTTPException(status_code=404, detail="研究不存在")
    
    return ResearchResponse.model_validate(research, from_attributes=True)