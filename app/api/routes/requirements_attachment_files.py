from typing import List, Optional
# from app.models.model_config import ModelConfig
from app.api.schemas.model_config import ModelConfigBase
from app.services import prompts
from app.services.attachments_service import extract_text_from_file
from app.services.llm_service import call_llm
from app.services.prompts import generate_requirements_analysis_prompt
from fastapi import APIRouter, Depends, status, HTTPException, UploadFile, File,Request
from uuid import UUID
import os
import shutil
import asyncio
from datetime import datetime
from app.api.repository.user_default_model import get_user_model
from app.models.organization_model_use import UseCase
from app.api.deps import get_current_user
# from app.models.user import User
from app.api.schemas.user import UserResponse
from app.models.requirements_attachments_files import RequirementsAttachmentFiles
from app.models.project_configs import ProjectConfig
from app.api.schemas.requirements_attachment_files import (
    RequirementsAttachmentResponse,
    RequirementsTypeContent,
    BatchResummarizeRequest,
    BatchResummarizeResponse
)
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)
router = APIRouter()


@router.get("", response_model=ResponseModel[List[RequirementsAttachmentResponse]])
async def list_requirements_attachments_files(
    project_id: Optional[UUID] = None,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取申报要求附件列表，可按项目ID筛选"""
    try:
        # 构建查询（只查询未删除的记录）
        query = RequirementsAttachmentFiles.filter(is_deleted=False)
        
        # 如果指定了项目ID，只查询该项目的附件
        if project_id:
            # 验证当前用户是否有权限查看此项目
            project_config = await ProjectConfig.filter(id=project_id).first()
            if not project_config:
                return send_data(False, None, "项目配置不存在")
            # if project_config.user_id != current_user.id:
            #     return send_data(False, None, "无权查看此项目")
            
            query = query.filter(project_configs_id=project_id)
        else:
            # 如果未指定项目ID，查询当前用户所有可访问的附件
            # 获取用户所有的项目ID
            project_ids = await ProjectConfig.filter(user_id=current_user.id).values_list('id', flat=True)
            if not project_ids:
                return send_data(True, [])
            query = query.filter(project_configs_id__in=project_ids)
        
        # 执行查询
        attachments = await query.all()
        logger.info(f"用户 {current_user.id} 查询到 {len(attachments)} 个未删除的参考资料附件")
        return send_data(True, attachments)
    except Exception as e:
        return send_data(False, None, f"获取申报要求附件列表失败: {str(e)}")



@router.delete("/{attachment_id}", response_model=ResponseModel)
async def delete_requirements_attachment_files(
    attachment_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """删除申报要求附件（软删除）"""
    try:
        # 查找附件（只查询未删除的）
        attachment_file = await RequirementsAttachmentFiles.filter(
            id=attachment_id, 
            is_deleted=False
        ).first()
        if not attachment_file:
            return send_data(False, None, "申报要求附件不存在或已被删除")
        
        # 验证当前用户是否有权限操作此附件
        project_config = await ProjectConfig.filter(id=attachment_file.project_configs_id).first()
        if project_config and project_config.user_id != current_user.id:
            return send_data(False, None, "无权操作此附件")
         
        # 🔧 主动删除关联记录（避免引用已删除文件）
        from app.models.file_biz_relations import FileBizRelation
        relation_count = await FileBizRelation.filter(file_id=attachment_id).count()
        if relation_count > 0:
            await FileBizRelation.filter(file_id=attachment_id).delete()
            logger.info(f"主动删除了 {relation_count} 个文件业务关联记录，文件ID: {attachment_id}")
        
        # 软删除文件记录
        from datetime import datetime
        attachment_file.is_deleted = True
        attachment_file.deleted_at = datetime.now()
        await attachment_file.save()
        
        logger.info(f"成功软删除参考资料文件: {attachment_file.file_name}, ID: {attachment_id}")
        return send_data(True, None, "删除成功")
    except Exception as e:
        logger.error(f"删除申报要求附件失败: {str(e)}")
        return send_data(False, None, f"删除申报要求附件失败: {str(e)}") 
    

@router.post("/process", response_model=ResponseModel[RequirementsTypeContent])
async def process_requirements_attachment_files(
    file: UploadFile = File(...),
    project_configs_name: Optional[str] = None,
    current_user: UserResponse = Depends(get_current_user)
):
    """直接上传文件进行解析 - 上传时不需要project_id，后续通过绑定接口关联"""
    try:
        logger.info(f"用户 {current_user.id} 开始处理上传的参考资料文档: {file.filename}")
        
        # 验证文件
        if not file.filename:
            return send_data(False, None, "文件名不能为空")
            
        # 获取文件扩展名
        file_extension = os.path.splitext(file.filename)[1].lower()
        if not file_extension:
            return send_data(False, None, "文件必须包含扩展名")
            
        # 验证文件类型
        allowed_extensions = ['.pdf', '.doc', '.docx','.md', '.txt']
        if file_extension not in allowed_extensions:
            return send_data(False, None, f"不支持的文件类型: {file_extension}")
            
        # 重置文件指针
        await file.seek(0)
        
        # 准备保存文件的目录
        date_str = datetime.now().strftime("%Y%m%d")
        user_dir = os.path.join("attachments", date_str)
        if not os.path.exists(user_dir):
            os.makedirs(user_dir, exist_ok=True)
        
        # 生成文件名和保存路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        original_filename = file.filename
        safe_filename = f"{timestamp}_{original_filename}"
        
        # 用于保存的实际文件路径
        file_save_path = os.path.join(user_dir, safe_filename)
        
        # 用于数据库的标准化路径，使用正斜杠
        db_file_path = f"attachments/{date_str}/{safe_filename}"
        
        # 保存文件
        try:
            with open(file_save_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            logger.info(f"文件保存成功: {file_save_path}")
        except Exception as save_error:
            logger.error(f"保存文件失败: {str(save_error)}")
            return send_data(False, None, f"保存文件失败: {str(save_error)}")
            
        # 读取文件内容并计算字数
        try:
            # 使用extract_text_from_file函数处理文件
            file_content = await extract_text_from_file(file_save_path)
            if not file_content:
                raise Exception("无法提取文件内容")
            
            word_count = len(file_content)
            
            if word_count > settings.MAX_FILE_WORD_COUNT:
                # 删除已保存的文件
                if os.path.exists(file_save_path):
                    os.remove(file_save_path)
                return send_data(False, None, f"文档字数超过限制（{settings.MAX_FILE_WORD_COUNT}字）")
                
        except Exception as read_error:
            logger.error(f"读取文件内容失败: {str(read_error)}")
            # 删除已保存的文件
            if os.path.exists(file_save_path):
                os.remove(file_save_path)
            return send_data(False, None, f"读取文件内容失败: {str(read_error)}")
            
        # 创建文件记录（不设置project_configs_id，等待后续绑定）
        try:
            file_record = await RequirementsAttachmentFiles.create(
                file_path=db_file_path,
                file_name=original_filename,
                file_content=file_content,  # 保存文件内容
                word_count=word_count  # 上传时不绑定项目，等待后续绑定
            )
            logger.info(f"参考资料文件数据记录创建成功，文件ID: {file_record.id}，等待后续绑定业务")
            
            # 调用LLM服务处理文档
            # model_config = await ModelConfig.filter(user_id=current_user.id, is_deleted=False, is_active=True).first()
            # if not model_config:
            #     logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            #     return send_data(False, None, "用户没有配置LLM模型")
            model_config = await get_user_model(
                current_user=current_user,
                use_case=UseCase.PROJECT_CONFIG_NEED.value
            )
            # 获取模型配置
            api_key = model_config.api_key
            api_url = model_config.api_url
            model = model_config.model_name
            
            logger.info(f"使用模型配置: model={model}, api_url={api_url} ,api_key={api_key[:10]}...{api_key[-5:]}")
            
            # 使用prompts.py中的提示词生成函数
            prompt = generate_requirements_analysis_prompt(content=file_content, project_configs_name=project_configs_name)
            
            # 准备消息
            messages = [
                {"role": "system", "content": prompts.REQUIREMENTS_ATTACHMENT_ANALYSIS_SYSTEM},
                {"role": "user", "content": prompt}
            ]
            start_time = datetime.now()
            logger.info(f"开始调用LLM进行参考资料内容提炼，文件ID: {file_record.id}, 记录时间：{start_time}")
            # 调用LLM
            llm_response = await call_llm(
                messages, 
                flag="参考资料内容提炼", 
                model=model,
                apiKey=api_key, 
                apiUrl=api_url
            )
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"LLM调用完成，文件ID: {file_record.id}, 完成时间：{end_time}，耗时：{duration.total_seconds()}秒({duration.total_seconds() / 60:.2f}分钟)，返回结果字数：{len(llm_response) if llm_response else 0}")
            
            if llm_response:
                logger.info(f"参考资料文件数据提取结果成功，文件ID: {file_record.id}，AI分析结果长度: {len(llm_response)}字符")
                # 更新文件记录的分析结果
                file_record.analysis_result = llm_response
                await file_record.save()
            else:
                logger.warning(f"LLM分析未返回结果，文件ID: {file_record.id}")
                return send_data(False, None, "LLM分析未返回结果")
            
            logger.info(f"成功处理文档，文件ID: {file_record.id}，字数: {word_count}，等待业务绑定")
            return send_data(True, file_record, "文件上传并分析成功，请在创建项目时绑定此文件")
            
        except Exception as db_error:
            logger.error(f"处理文件失败: {str(db_error)}")
            # 删除已保存的文件
            if os.path.exists(file_save_path):
                os.remove(file_save_path)
            return send_data(False, None, f"处理文件失败: {str(db_error)}")
            
    except Exception as e:
        logger.error(f"处理文档失败: {str(e)}")
        return send_data(False, None, f"处理文件失败: {str(e)}")


async def process_single_attachment(
    attachment_id: UUID,
    project_configs_name: Optional[str],
    model_config: ModelConfigBase
) -> dict:
    """处理单个附件的重新总结"""
    try:
        # 查询附件记录
        attachment = await RequirementsAttachmentFiles.filter(
            id=attachment_id,
            is_deleted=False
        ).first()

        if not attachment:
            return {
                "success": False,
                "attachment_id": str(attachment_id),
                "error": "附件不存在或已删除"
            }

        # 检查文件是否存在
        if not attachment.file_path:
            return {
                "success": False,
                "attachment_id": str(attachment_id),
                "error": "文件路径为空"
            }

        # 构建完整文件路径
        file_path = attachment.file_path
        if not os.path.isabs(file_path):
            # 如果是相对路径，构建完整路径
            file_path = os.path.join(os.getcwd(), file_path)

        if not os.path.exists(file_path):
            return {
                "success": False,
                "attachment_id": str(attachment_id),
                "error": f"文件不存在: {file_path}"
            }

        # 重新提取文件内容（如果需要）
        file_content = attachment.file_content
        if not file_content:
            file_content = await extract_text_from_file(file_path)
            if not file_content:
                return {
                    "success": False,
                    "attachment_id": str(attachment_id),
                    "error": "无法提取文件内容"
                }

            # 更新文件内容和字数
            attachment.file_content = file_content
            attachment.word_count = len(file_content)

        # 获取模型配置
        api_key = model_config.api_key
        api_url = model_config.api_url
        model = model_config.model_name

        # 生成提示词
        prompt = generate_requirements_analysis_prompt(
            content=file_content,
            project_configs_name=project_configs_name
        )

        # 准备消息
        messages = [
            {"role": "system", "content": prompts.REQUIREMENTS_ATTACHMENT_ANALYSIS_SYSTEM},
            {"role": "user", "content": prompt}
        ]

        start_time = datetime.now()
        logger.info(f"开始重新总结附件，文件ID: {attachment_id}, 开始时间：{start_time}")

        # 调用LLM
        llm_response = await call_llm(
            messages,
            flag="批量重新总结附件",
            model=model,
            apiKey=api_key,
            apiUrl=api_url
        )

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"附件重新总结完成，文件ID: {attachment_id}, 完成时间：{end_time}，耗时：{duration.total_seconds()}秒")

        if llm_response:
            # 更新分析结果
            attachment.analysis_result = llm_response
            attachment.updated_at = datetime.now()
            await attachment.save()

            logger.info(f"附件重新总结成功，文件ID: {attachment_id}，AI分析结果长度: {len(llm_response)}字符")
            return {
                "success": True,
                "attachment_id": str(attachment_id),
                "attachment": attachment
            }
        else:
            return {
                "success": False,
                "attachment_id": str(attachment_id),
                "error": "LLM分析未返回结果"
            }

    except Exception as e:
        logger.error(f"处理附件 {attachment_id} 时出错: {str(e)}")
        return {
            "success": False,
            "attachment_id": str(attachment_id),
            "error": str(e)
        }


@router.post("/batch-resummarize", response_model=ResponseModel[BatchResummarizeResponse])
async def batch_resummarize_attachments(
    request: BatchResummarizeRequest,
    current_user: UserResponse = Depends(get_current_user)
):
    """批量重新总结附件"""
    try:
        logger.info(f"用户 {current_user.id} 开始批量重新总结附件，数量: {len(request.attachment_ids)}")

        if not request.attachment_ids:
            return send_data(False, None, "附件ID列表不能为空")

        if len(request.attachment_ids) > settings.MAX_BATCH_ATTACHMENT_COUNT:
            return send_data(False, None, f"单次批量处理数量不能超过{settings.MAX_BATCH_ATTACHMENT_COUNT}个")

        # 获取用户的模型配置
        # model_config = await ModelConfig.filter(
        #     user_id=current_user.id,
        #     is_deleted=False,
        #     is_active=True
        # ).first()

        # if not model_config:
        #     return send_data(False, None, "用户没有配置LLM模型")
        model_config = await get_user_model(
            current_user=current_user,
            use_case=UseCase.PROJECT_CONFIG_NEED.value
        )

        # 使用asyncio.gather进行并发处理，但限制并发数量
        semaphore = asyncio.Semaphore(5)  # 限制同时处理5个

        async def process_with_semaphore(attachment_id):
            async with semaphore:
                return await process_single_attachment(
                    attachment_id,
                    request.project_configs_name,
                    model_config
                )

        # 并发处理所有附件
        start_time = datetime.now()
        logger.info(f"开始并发处理 {len(request.attachment_ids)} 个附件，开始时间：{start_time}")

        results = await asyncio.gather(
            *[process_with_semaphore(attachment_id) for attachment_id in request.attachment_ids],
            return_exceptions=True
        )

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"批量处理完成，总耗时：{duration.total_seconds()}秒({duration.total_seconds() / 60:.2f}分钟)")

        # 统计结果
        success_results = []
        failed_results = []

        for result in results:
            if isinstance(result, Exception):
                failed_results.append({
                    "attachment_id": "unknown",
                    "error": str(result)
                })
            elif result["success"]:
                success_results.append(result["attachment"])
            else:
                failed_results.append({
                    "attachment_id": result["attachment_id"],
                    "error": result["error"]
                })

        response_data = BatchResummarizeResponse(
            total_count=len(request.attachment_ids),
            success_count=len(success_results),
            failed_count=len(failed_results),
            results=success_results,
            failed_items=failed_results
        )

        logger.info(f"批量重新总结完成，总数: {response_data.total_count}, 成功: {response_data.success_count}, 失败: {response_data.failed_count}")

        return send_data(True, response_data, f"批量重新总结完成，成功: {response_data.success_count}, 失败: {response_data.failed_count}")

    except Exception as e:
        logger.error(f"批量重新总结附件失败: {str(e)}")
        return send_data(False, None, f"批量重新总结失败: {str(e)}")


