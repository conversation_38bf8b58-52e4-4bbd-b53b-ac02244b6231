from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from uuid import UUID

from app.api.deps import get_current_user
from app.api.schemas.user import UserResponse
from app.models.requirements_attachments_files import RequirementsAttachmentFiles
from app.models.file_biz_relations import FileBizRelation
from app.api.schemas.file_biz_relations import (
    FileBizRelationBindRequest,
    FileBizRelationResponse
)
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.utils.constants import ProductType
from app.utils.constants import ProductTypeText
from app.api.schemas.requirements_attachment_files import RequirementsAttachmentResponse

logger = get_logger(__name__)
router = APIRouter()


@router.post("/bind", response_model=ResponseModel[List[FileBizRelationResponse]])
async def bind_files_to_business(
    request: FileBizRelationBindRequest,
    current_user: UserResponse = Depends(get_current_user)
):
    """绑定文件到具体业务（如项目创建时调用）"""
    try:
        logger.info(f"用户 {current_user.id} 开始绑定文件，业务类型: {request.product_type}, 业务ID: {request.biz_id}, 文件数量: {len(request.file_ids)}")
        
        # 验证文件是否存在且未被删除
        files = await RequirementsAttachmentFiles.filter(
            id__in=request.file_ids, 
            is_deleted=False
        ).all()
        if len(files) != len(request.file_ids):
            missing_ids = set(request.file_ids) - {f.id for f in files}
            logger.warning(f"部分文件不存在或已被删除: {missing_ids}")
            return send_data(False, None, f"部分文件不存在或已被删除: {missing_ids}")
        
        # 检查是否有文件已经被绑定到同一业务
        existing_relations = await FileBizRelation.filter(
            file_id__in=request.file_ids,
            product_type=request.product_type,
            biz_id=request.biz_id
        ).all()
        
        if existing_relations:
            bound_file_ids = [str(r.file_id) for r in existing_relations]
            logger.warning(f"部分文件已绑定到该业务: {bound_file_ids}")
            return send_data(False, None, f"部分文件已绑定到该业务: {bound_file_ids}")
        
        # 创建关联记录
        created_relations = []
        for file in files:
            relation = await FileBizRelation.create(
                file_id=file.id,
                ai_analysis_summary=file.analysis_result,  # 将原有的analysis_result复制到中间表
                product_type=request.product_type,
                biz_id=request.biz_id
            )
            
            created_relations.append(relation)
            logger.info(f"成功创建参考资料和业务关联: 文件ID {file.id} -> 类型：{request.product_type}，业务是：{ProductTypeText[request.product_type].value}，业务ID：{request.biz_id}")
        
        # 预加载关联数据并返回（确保文件未被删除）
        relations_with_files = await FileBizRelation.filter(
            id__in=[r.id for r in created_relations],
            file__is_deleted=False
        ).prefetch_related("file").all()
        
        logger.info(f"成功绑定 {len(created_relations)} 个文件到业务 {request.product_type}:{request.biz_id}")
        return send_data(True, relations_with_files, f"成功绑定 {len(created_relations)} 个文件")
        
    except Exception as e:
        logger.error(f"绑定参考资料失败: {str(e)}")
        return send_data(False, None, f"绑定文件失败: {str(e)}")


@router.get("/business/{product_type}/{biz_id}", response_model=ResponseModel[List[RequirementsAttachmentResponse]])
async def get_files_for_business(
    product_type: ProductType,
    biz_id: str,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取特定业务的参考文件"""
    try:
        logger.info(f"用户 {current_user.id} 查询业务参考资料，业务类型: {product_type}, 业务ID: {biz_id}")
        
        # 查询该业务下的所有参考资料关联（只查询未删除的文件）
        relations = await FileBizRelation.filter(
            product_type=product_type,
            biz_id=biz_id,
            file__is_deleted=False  # 通过关联查询过滤已删除的文件
        ).prefetch_related("file").all()
        
        if not relations:
            logger.info(f"业务 {product_type}:{biz_id} 没有关联的参考资料")
            return send_data(True, [], "该业务没有关联的参考资料")
        # 获取所有的文件对象
        files = [relation.file for relation in relations]
        return send_data(True, files, f"获取到 {len(files)} 个关联的文件")
        
    except Exception as e:
        logger.error(f"获取业务文件失败: {str(e)}")
        return send_data(False, None, f"获取业务文件失败: {str(e)}")