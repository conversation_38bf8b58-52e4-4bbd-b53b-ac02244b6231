from typing import Optional, List
from app.api.repository.user_default_model import get_user_model
from app.api.schemas.user import UserResponse
from app.models.organization_model_use import UseCase
from fastapi import APIRouter, Depends, status, Body, Request, HTTPException    
from datetime import datetime
from tortoise.expressions import Q
import json
from uuid import UUID

from app.api.deps import get_current_user, get_current_user_from_state
from app.models.user import User
from app.utils.utils import send_data, ResponseModel, send_page_data, PageInfo, ResponsePageModel
from app.core.logging import get_logger
from app.services.insight.inspiration_service import generate_inspiration, save_inspiration_to_library
from app.api.schemas.insight.inspiration import InspirationRequest, InspirationResponse, SaveInspirationRequest, InspirationListItem, InspirationQueryParams, InspirationDetail, CreateCanvasFromInspirationRequest, CreateCanvasFromInspirationResponse, CreatedCanvasItem
from app.models.model_config import ModelConfig
from app.models.insight.inspirations import Inspiration
from app.models.insight.inspiration_tag import InspirationTag
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.models.insight.knowledge_canvas_tag import KnowledgeCanvasTag
from app.api.schemas.insight.knowledge_canvas import CanvasSourceType, CanvasType

logger = get_logger(__name__)
router = APIRouter()

# 生成灵感接口
@router.post("", response_model=ResponseModel[list],summary="生成灵感接口")
async def create_inspiration(
    request: Request
):
    """
    生成灵感接口
    
    出参:
    - content: 生成的灵感内容（JSON数组格式，每个元素包含 inspiration、name、source 和 tags 字段）
    """
    try:
        current_user = get_current_user_from_state(request)
        # 获取用户的模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_GENERATE.value)
        
        if not model_config:
            raise HTTPException(status_code=400, detail="未找到有效的模型配置，请先配置模型")
            
        # 调用服务层函数生成灵感
        result = await generate_inspiration(model_config,current_user.id)
        
        # 解析JSON字符串为列表
        inspiration_content = result.get("inspiration", "")
        logger.info(f"原始灵感内容: {inspiration_content}")
        
        # 处理前置文本问题，查找第一个'['作为JSON开始
        json_start = inspiration_content.find("[")
        if json_start != -1:
            inspiration_content = inspiration_content[json_start:]
            logger.info(f"提取JSON部分: {inspiration_content}")
        
        try:
            inspiration_json = json.loads(inspiration_content)
            if not isinstance(inspiration_json, list):
                return send_data(False, None, "生成的灵感格式无效：应为数组格式")
            
            # 处理每个灵感项
            processed_inspirations = []
            for item in inspiration_json:
                if not all(key in item for key in ['inspiration', 'name', 'source', 'tags']):
                    return send_data(False, None, "生成的灵感格式无效：缺少必要字段")
                
                # 处理source字段 - 如果是字符串格式则尝试解析为JSON
                if isinstance(item['source'], str):
                    try:
                        logger.info(f"解析灵感source字段: {item['source']}")
                        item['source'] = json.loads(item['source'])
                    except json.JSONDecodeError:
                        # 如果解析失败，保留原始字符串
                        logger.warning(f"解析灵感source字段失败: {item['source']}")
                
                processed_inspirations.append(item)
            
            return send_data(True, processed_inspirations)
        except json.JSONDecodeError as e:
            logger.error(f"解析灵感JSON失败: {str(e)}")
            return send_data(False, None, f"生成的灵感格式无效")
    except HTTPException as e:
        return send_data(False, None, str(e.detail))
    except Exception as e:
        logger.error(f"创建灵感失败: {str(e)}")
        return send_data(False, None, f"创建灵感失败: {str(e)}")


# 保存灵感到灵感库接口
@router.post("/save", response_model=ResponseModel[bool])
async def save_inspiration(
    req: List[SaveInspirationRequest],
    request: Request
):
    """
    批量保存灵感到灵感库接口
    
    入参:
    - req: List[SaveInspirationRequest]，包含多个灵感信息
    
    出参:
    - success: 是否保存成功
    """
    try:
        current_user = get_current_user_from_state(request)
        logger.info(f"批量保存灵感请求: {req}")
        
        for item in req:
            # 处理inspiration_source参数
            source_data = [source.dict() for source in item.inspiration_source] if item.inspiration_source else None
            
            # 创建灵感
            inspiration = await Inspiration.create(
                user_id=current_user.id,
                name=item.name,
                source=source_data,
                original_article_truncated=item.original_article_truncated,
                source_type=CanvasSourceType.INSPIRATION.value,
                type=CanvasType.INSPIRATION.value
            )
            
            # 处理标签
            if item.tags:
                tag_objs = []
                for tag_name in item.tags:
                    tag, created = await InspirationTag.get_or_create(
                        name=tag_name,
                        user_id=current_user.id,
                        defaults={"user_id": current_user.id}
                    )
                    tag_objs.append(tag)
                await inspiration.tags.add(*tag_objs)
            
        return send_data(True, True)
    except HTTPException as e:
        return send_data(False, False, str(e.detail))
    except Exception as e:
        logger.error(f"保存灵感失败: {str(e)}")
        return send_data(False, False, f"保存灵感失败: {str(e)}")


# 查询灵感库列表接口
@router.get("/list", response_model=ResponsePageModel[InspirationListItem])
async def get_inspiration_list(
    params: InspirationQueryParams = Depends(),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    查询灵感库列表接口
    
    入参:
    - page: 页码，默认1
    - size: 每页数量，默认10
    - tag: 标签筛选（可选）
    - keyword: 关键词搜索（可选）
    
    出参:
    - items: 灵感列表，每个灵感包含以下字段：
        - id: 灵感ID
        - name: 灵感名称
        - summary: 灵感综述
        - tags: 标签列表
        - created_at: 创建时间
        - source: 灵感来源
        - updated_at: 更新时间
    - total: 总数
    - page: 当前页码
    - size: 每页数量
    """
    try:
        # 构建基础查询
        base_query = Inspiration.filter(
            is_deleted=False,
            user_id=current_user.id
        )
        
        # 关键词搜索
        if params.keyword:
            # 构建名称搜索条件
            name_query = Q(name__icontains=params.keyword)
            
            # 构建标签搜索条件 - 使用子查询
            tag_ids = await Inspiration.filter(
                tags__name__icontains=params.keyword
            ).values_list('id', flat=True)
            tag_query = Q(id__in=tag_ids)
            
            # 使用OR组合条件
            base_query = base_query.filter(name_query | tag_query)
        
        # 标签筛选
        if params.tag:
            base_query = base_query.filter(tags__name=params.tag)
            
        # 获取总数（使用子查询去重）
        total = await base_query.distinct().count()
        
        # 分页查询
        offset = (params.page - 1) * params.size
        inspirations_list = await base_query.distinct().prefetch_related(
            'tags'
        ).offset(offset).limit(params.size).order_by('-updated_at')
        
        # 处理返回数据
        processed_list = []
        for inspiration in inspirations_list:
            # 处理标签 - 使用字典构建标签，而不是直接使用InspirationTag类
            tags_data = []
            await inspiration.fetch_related('tags')
            for tag in inspiration.tags:
                if not tag.is_deleted:
                    # 使用字典构建标签数据
                    tag_dict = {
                        "id": str(tag.id),  # 确保ID是字符串
                        "name": tag.name
                    }
                    tags_data.append(tag_dict)
            
            # 处理来源数据
            source_data = []
            if inspiration.source:
                try:
                    # 如果 source 是字符串，尝试解析为 JSON
                    if isinstance(inspiration.source, str):
                        source_data = json.loads(inspiration.source)
                    # 如果 source 已经是列表或字典，直接使用
                    elif isinstance(inspiration.source, (list, dict)):
                        source_data = inspiration.source
                except json.JSONDecodeError as e:
                    logger.error(f"解析灵感来源失败: {str(e)}")
                    source_data = []
            
            # 构建处理后的灵感对象
            processed_inspiration = {
                "id": str(inspiration.id),
                "name": inspiration.name,
                "summary": inspiration.summary,
                "tags": tags_data,
                "source": source_data,
                "created_at": inspiration.created_at,
                "updated_at": inspiration.updated_at
            }
            processed_list.append(processed_inspiration)
        
        # 构建分页响应数据
        page_info = PageInfo(
            items=processed_list,
            total=total,
            page=params.page,
            size=params.size
        )
        
        return send_page_data(True, page_info)
    except Exception as e:
        logger.error(f"查询灵感列表失败: {str(e)}")
        return send_page_data(
            False, 
            PageInfo(items=[], total=0, page=params.page, size=params.size), 
            f"查询灵感列表失败: {str(e)}"
        )


@router.get(
    "/{inspiration_id}",
    response_model=ResponseModel[InspirationDetail],
    summary="获取灵感详情",
    description="根据ID获取灵感的详细信息。"
)
async def get_inspiration_detail(
    inspiration_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """获取灵感详情"""
    try:
        inspiration = await Inspiration.filter(id=inspiration_id, is_deleted=False).prefetch_related('tags').first()
        if not inspiration:
            return send_data(False, None, "灵感不存在或已删除")
        
        # 检查权限 - 只能查看自己的灵感
        if inspiration.user_id != current_user.id:
            return send_data(False, None, "无权限查看此灵感")
        
        # 处理标签数据
        tags_data = []
        for tag in inspiration.tags:
            if not tag.is_deleted:
                tag_dict = {
                    "id": str(tag.id),
                    "name": tag.name
                }
                tags_data.append(tag_dict)
        
        # 处理来源数据
        source_data = []
        if inspiration.source:
            try:
                # 如果 source 是字符串，尝试解析为 JSON
                if isinstance(inspiration.source, str):
                    source_data = json.loads(inspiration.source)
                # 如果 source 已经是列表或字典，直接使用
                elif isinstance(inspiration.source, (list, dict)):
                    source_data = inspiration.source
            except json.JSONDecodeError as e:
                logger.error(f"解析灵感来源失败: {str(e)}")
                source_data = []
        
        # 构建处理后的灵感对象
        processed_inspiration = InspirationDetail.model_validate({
            "id": inspiration.id,
            "name": inspiration.name,
            "source": source_data,
            "tags": tags_data,
            "content": inspiration.content or '',
            "summary": inspiration.summary or '',
            "original_article_truncated": inspiration.original_article_truncated or '',
            "ai_expanded": inspiration.ai_expanded or '',
            "ai_analysis": inspiration.ai_analysis or '',
            "ai_keynotes": inspiration.ai_keynotes or '',
            "ai_outline": inspiration.ai_outline or '',
            "ai_probe": inspiration.ai_probe or '',
            "ai_thought_process": inspiration.ai_thought_process or '',
            "is_deleted": inspiration.is_deleted,
            "created_at": inspiration.created_at,
            "updated_at": inspiration.updated_at,
            "deleted_at": inspiration.deleted_at,
            "user_id": inspiration.user_id,
            "insight_report_id": inspiration.insight_reports_id
        })
        
        return send_data(True, processed_inspiration)
    except Exception as e:
        logger.error(f"获取灵感详情失败: {str(e)}")
        return send_data(False, None, f"获取灵感详情失败: {str(e)}")


@router.delete(
    "/{inspiration_id}",
    response_model=ResponseModel[bool],
    summary="软删除灵感库",
    description="根据ID软删除灵感库。"
)
async def soft_delete_inspiration(
    inspiration_id: UUID,
    current_user: UserResponse = Depends(get_current_user)
):
    """软删除灵感库"""
    try:
        inspiration = await Inspiration.filter(id=inspiration_id, is_deleted=False).first()
        if not inspiration:
            return send_data(False, False, "灵感不存在或已删除")
        
        # 检查权限 - 只能删除自己的灵感
        if inspiration.user_id != current_user.id:
            return send_data(False, False, "无权限删除此灵感")
        
        # 标记为已删除
        inspiration.is_deleted = True
        inspiration.deleted_at = datetime.now()
        await inspiration.save()
        
        logger.info(f"用户 {current_user.username} 成功软删除灵感: {inspiration.name} (ID: {inspiration.id})")
        return send_data(True, True)
    except Exception as e:
        logger.error(f"删除灵感失败: {str(e)}")
        return send_data(False, False, f"删除灵感失败: {str(e)}")


@router.post(
    "/create-canvas",
    response_model=ResponseModel[CreateCanvasFromInspirationResponse],
    summary="根据灵感库ID创建灵感卡片",
    description="根据多个灵感库ID创建对应的灵感卡片，灵感类型是灵感，来源是灵感库，返回创建的灵感卡片详细信息"
)
async def create_canvas_from_inspiration(
    req: CreateCanvasFromInspirationRequest,
    request: Request
):
    """
    根据灵感库ID列表创建灵感卡片
    
    此接口将根据提供的灵感库ID列表，为每个灵感库创建对应的灵感卡片。
    创建的灵感卡片将复制灵感库的所有字段信息，包括：
    - 名称、综述、AI分析结果等内容字段
    - 标签信息（自动转换为灵感卡片标签）
    - 来源信息（标记为来自灵感库）
    
    权限控制：
    - 只能基于当前用户自己的灵感库创建灵感卡片
    - 如果提供的灵感库ID中有不存在或无权限的，将返回错误
    
    请求示例:
    ```json
    {
        "inspiration_ids": [
            "550e8400-e29b-41d4-a716-446655440001",
            "550e8400-e29b-41d4-a716-446655440002"
        ]
    }
    ```
    
    响应示例:
    ```json
    {
        "success": true,
        "data": {
            "created_canvases": [
                {
                    "id": "660e8400-e29b-41d4-a716-446655440001",
                    "name": "灵感卡片名称",
                    "source_type": "INSPIRATION",
                    "type": "INSPIRATION",
                    "summary": "灵感卡片概要",
                    "tags": ["标签1", "标签2"],
                    "created_at": "2024-01-01T00:00:00Z",
                    "user_id": "770e8400-e29b-41d4-a716-446655440001"
                }
            ],
            "success_count": 2,
            "total_count": 2
        },
        "message": "成功创建 2 个灵感卡片"
    }
    ```
    
    入参:
    - inspiration_ids: 灵感库ID列表，最多10个
    
    出参:
    - success: 是否创建成功
    - data: CreateCanvasFromInspirationResponse对象，包含创建的灵感卡片详细信息
    - message: 详细的操作结果信息
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 根据灵感库ID创建灵感卡片: {req.inspiration_ids}")
        
        # 验证灵感库ID是否存在且属于当前用户
        inspirations = await Inspiration.filter(
            id__in=req.inspiration_ids,
            user_id=current_user.id,
            is_deleted=False
        ).prefetch_related('tags')
        
        if len(inspirations) != len(req.inspiration_ids):
            missing_ids = set(req.inspiration_ids) - {insp.id for insp in inspirations}
            logger.warning(f"用户 {current_user.username} 请求的灵感库ID中有不存在或无权限的ID: {missing_ids}")
            return send_data(False, None, "部分灵感库不存在或无权限访问")
        
        created_canvases = []
        failed_count = 0
        
        for inspiration in inspirations:
            try:
                # 构建灵感来源数据
                source_data = [{
                    'source_id': str(inspiration.id),
                    'source_type': CanvasSourceType.INSPIRATION.value,
                    'source_name': inspiration.name
                }]
                
                # 创建灵感卡片，复制灵感库的字段信息
                canvas = await KnowledgeCanvas.create(
                    name=inspiration.name,
                    source_type=CanvasSourceType.INSPIRATION.value,
                    type=CanvasType.INSPIRATION.value,
                    summary=inspiration.summary or '',
                    original_article_truncated=inspiration.original_article_truncated or '',
                    inspiration_source=json.dumps(source_data, ensure_ascii=False),
                    ai_expanded=inspiration.ai_expanded or '',
                    ai_analysis=inspiration.ai_analysis or '',
                    ai_keynotes=inspiration.ai_keynotes or '',
                    ai_outline=inspiration.ai_outline or '',
                    ai_probe=inspiration.ai_probe or '',
                    user_id=current_user.id
                )
                
                # 复制标签 - 将灵感库的标签转换为灵感卡片标签
                canvas_tags = []
                if inspiration.tags:
                    canvas_tag_objs = []
                    for inspiration_tag in inspiration.tags:
                        if not inspiration_tag.is_deleted:
                            # 查找或创建对应的灵感卡片标签
                            canvas_tag, created = await KnowledgeCanvasTag.get_or_create(
                                name=inspiration_tag.name,
                                user_id=current_user.id,
                                defaults={"user_id": current_user.id, "is_deleted": False}
                            )
                            canvas_tag_objs.append(canvas_tag)
                            canvas_tags.append(canvas_tag.name)
                    
                    if canvas_tag_objs:
                        await canvas.tags.add(*canvas_tag_objs)
                
                # 构建创建的灵感卡片信息
                created_canvas_item = CreatedCanvasItem(
                    id=canvas.id,
                    name=canvas.name,
                    source_type=canvas.source_type,
                    type=canvas.type,
                    summary=canvas.summary or '',
                    original_article_truncated=canvas.original_article_truncated or '',
                    inspiration_source=source_data,
                    tags=canvas_tags,
                    ai_expanded=canvas.ai_expanded or '',
                    ai_analysis=canvas.ai_analysis or '',
                    ai_keynotes=canvas.ai_keynotes or '',
                    ai_outline=canvas.ai_outline or '',
                    ai_probe=canvas.ai_probe or '',
                    created_at=canvas.created_at,
                    updated_at=canvas.updated_at,
                    user_id=canvas.user_id
                )
                
                created_canvases.append(created_canvas_item)
                logger.info(f"成功创建灵感卡片: {canvas.name} (ID: {canvas.id})")
                
            except Exception as e:
                logger.error(f"创建灵感卡片失败，灵感库ID: {inspiration.id}, 错误: {str(e)}")
                failed_count += 1
                continue
        
        if len(created_canvases) == 0:
            return send_data(False, None, "所有灵感卡片创建失败")
        else:
            # 构建响应数据
            response_data = CreateCanvasFromInspirationResponse(
                created_canvases=created_canvases,
                success_count=len(created_canvases),
                total_count=len(req.inspiration_ids)
            )
            
            message = f"成功创建 {len(created_canvases)} 个灵感卡片"
            if failed_count > 0:
                message += f"，{failed_count} 个创建失败"
                
            return send_data(True, response_data, message)
            
    except Exception as e:
        logger.error(f"根据灵感库ID创建灵感卡片失败: {str(e)}")
        return send_data(False, None, f"创建灵感卡片失败: {str(e)}")

