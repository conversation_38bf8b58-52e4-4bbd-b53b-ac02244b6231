from typing import Optional
from app.api.repository.user_default_model import get_user_model
from app.api.schemas.user import UserResponse
from app.models.organization_model_use import UseCase
from fastapi import APIRouter, Depends, status, Body, Request
from uuid import UUID
import json

from app.api.deps import get_current_user, get_current_user_from_state
from app.models.user import User
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.services.llm_service import call_llm, call_llm_with_format_json
from app.core.config import settings
from app.services.insight.insight_prompts import generate_expand_prompt, insight_generate_outline_prompt, generate_key_points_prompt, generate_probe_questions_prompt, generate_probe_answer_prompt, generate_translate_prompt, generate_polish_prompt, generate_continue_prompt, generate_abbreviate_prompt
from app.models.model_config import ModelConfig
from app.services.insight.knowledge_canvas_service import generate_summary

logger = get_logger(__name__)
router = APIRouter()

# AI扩写接口
@router.post("/expand", response_model=ResponseModel[str],summary="AI扩写", deprecated=True)
async def expand_content(
    canvas_id: UUID = Body(..., embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    AI扩写接口
    
    入参:
    - canvas_id: 需要扩写的内容ID
    
    出参:
    - 扩写后的内容
    """
    try:
        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        if not canvas.name:
            return send_data(False, None, "灵感卡片名称为空")
            
        # 加载关联的标签
        await canvas.fetch_related('tags')
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_HANDLE_TEXT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成提示词
        tags_list = [tag.name for tag in canvas.tags] if hasattr(canvas, 'tags') else []
        messages = generate_expand_prompt(
            name=canvas.name,
            tags=tags_list,
            original_article_truncated=canvas.original_article_truncated
        )
        if not messages:
            return send_data(False, None, "生成提示词失败")
            
        # 调用LLM服务
        expanded_content = await call_llm(
            messages=messages,
            flag=f"expand_content_{canvas_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not expanded_content:
            return send_data(False, None, "AI扩写内容为空")
            
        # 保存扩写结果
        canvas.ai_expanded = expanded_content
        await canvas.save()
        
        return send_data(True, expanded_content)
        
    except Exception as e:
        logger.error(f"AI扩写失败: {str(e)}")
        return send_data(False, None, f"AI扩写失败: {str(e)}")

# AI分析接口
@router.post("/analyze", response_model=ResponseModel[dict], deprecated=True)
async def analyze_content(
    canvas_id: UUID = Body(..., embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    AI分析接口
    
    入参:
    - canvas_id: 需要分析的内容ID
    
    出参:
    - 分析结果
    """
    try:
        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        if not canvas.original_article:
            return send_data(False, None, "原始文章内容为空")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            

            
        return send_data(False, None, "该功能暂未实现")
            
    except Exception as e:
        logger.error(f"AI分析失败: {str(e)}")
        return send_data(False, None, f"AI分析失败: {str(e)}")

# AI总结接口
@router.post("/summarize", response_model=ResponseModel[str], deprecated=True)
async def summarize_content(
    canvas_id: UUID = Body(..., embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    AI总结接口
    
    入参:
    - canvas_id: 需要总结的内容ID
    
    出参:
    - 总结后的内容
    """
    try:
        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        return send_data(False, None, "该功能暂未实现")
            
    except Exception as e:
        logger.error(f"AI总结失败: {str(e)}")
        return send_data(False, None, f"AI总结失败: {str(e)}")

# AI简介+结构大纲接口
@router.post("/outline", response_model=ResponseModel[dict],summary="AI大纲", deprecated=True)
async def generate_outline(
    canvas_id: UUID = Body(..., embed=True, description="灵感卡片ID"),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    AI大纲+结构大纲接口
    
    入参:
    - canvas_id: 需要生成大纲的内容ID（在请求体中传递）
    
    出参:
    - 返回包含概要和大纲的字典
    """
    try:
        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        # 检查是否已有概要和大纲，如果都有则直接返回
        if canvas.summary and canvas.ai_outline:
            logger.info(f"灵感卡片 {canvas_id} 的概要和大纲已存在，直接返回")
            return send_data(True, {
                "summary": canvas.summary,
                "outline": canvas.ai_outline,
                "combined": f"{canvas.summary} {canvas.ai_outline}".strip()
            })
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)

        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成概要（只有当summary为空时才生成）
        summary = canvas.summary
        if not summary:
            logger.info(f"为灵感卡片 {canvas_id} 生成概要")
            summary = await generate_summary(canvas.original_article, canvas_id, model_config)
            if summary:
                canvas.summary = summary
                await canvas.save()
            
        # 生成大纲（只有当ai_outline为空时才生成）
        outline_content = canvas.ai_outline
        if not outline_content:
            logger.info(f"为灵感卡片 {canvas_id} 生成大纲")
            # 生成提示词
            messages = insight_generate_outline_prompt(name=canvas.name, content=canvas.original_article)
            if not messages:
                return send_data(False, None, "生成提示词失败")
                
            # 调用LLM服务
            outline_content = await call_llm(
                messages=messages,
                flag=f"generate_outline_{canvas_id}",
                model=model_config.model_name,
                apiKey=model_config.api_key,
                apiUrl=model_config.api_url
            )
            
            if not outline_content:
                return send_data(False, None, "AI生成大纲内容为空")
                
            # 保存大纲结果
            canvas.ai_outline = outline_content
            await canvas.save()
        
        # 返回概要和大纲
        return send_data(True, {
            "summary": summary,
            "outline": outline_content,
            "combined": f"{summary or ''} {outline_content or ''}".strip()
        })
        
    except Exception as e:
        logger.error(f"AI生成大纲失败: {str(e)}")
        return send_data(False, None, f"AI生成大纲失败: {str(e)}")

# 定义 JSON Schema
KEYNOTES_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "keynote_title": {
                "type": "string",
                "description": "重点标题"
            },
            "keynote_sub_title": {
                "type": "array",
                "description": "重点子标题列表",
                "items": {
                    "type": "string"
                }
            }
        },
        "required": ["keynote_title", "keynote_sub_title"],
        "additionalProperties": False
    }
   
}

@router.post("/keynotes", response_model=ResponseModel[dict],summary="AI重点", deprecated=True)
async def generate_keynotes(
    canvas_id: UUID = Body(..., embed=True),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    AI重点接口
    
    入参:
    - canvas_id: 需要生成重点的内容ID
    
    出参:
    - 生成的重点内容，包含标题和子标题的结构化数据
    """
    try:
        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成提示词
        messages = generate_key_points_prompt(content=canvas.original_article)
        if not messages:
            return send_data(False, None, "生成提示词失败")
            
        # 定义 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "keynotes_result",
                "strict": True,
                "schema": KEYNOTES_SCHEMA
            }
        }
            
        # 调用LLM服务
        keynotes_content = await call_llm_with_format_json(
            messages=messages,
            model=model_config.model_name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            response_format=response_format
        )
        
        if not keynotes_content:
            return send_data(False, None, "AI生成重点内容为空")
            
        try:
            # 解析JSON内容
            keynotes_data = json.loads(keynotes_content)
            
            # 保存重点结果
            canvas.ai_keynotes = keynotes_content
            await canvas.save()
            
            return send_data(True, {
                "keynotes": keynotes_data
            })
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return send_data(False, None, "AI生成的重点内容格式错误")
        
    except Exception as e:
        logger.error(f"AI生成重点失败: {str(e)}")
        return send_data(False, None, f"AI生成重点失败: {str(e)}")

# 定义 JSON Schema
PROBE_SCHEMA = {
    "type": "array",
    "items": {
        "type": "string",
        "description": "追问问题"
    }
}

@router.post("/probe_questions", response_model=ResponseModel[dict],summary="AI追问_问题列表", deprecated=True)
async def generate_probe(
    request: Request,
    canvas_id: UUID = Body(..., embed=True)
):
    """
    AI追问接口
    
    入参:
    - canvas_id: 需要生成追问的内容ID
    
    出参:
    - 生成的追问内容，包含问题列表
    """
    try:
        current_user = get_current_user_from_state(request)

        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        logger.info(f"canvas.original_article: {canvas.original_article}")
        # 生成提示词
        messages = generate_probe_questions_prompt(content=canvas.original_article)
        if not messages:
            return send_data(False, None, "生成提示词失败")
            
        # 定义 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "probe_result",
                "strict": True,
                "schema": PROBE_SCHEMA
            }
        }
            
        # 调用LLM服务
        probe_content = await call_llm_with_format_json(
            messages=messages,
            model=model_config.model_name,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            response_format=response_format
        )
        logger.info(f"AI生成追问内容: {probe_content}")
        if not probe_content:
            return send_data(False, None, "AI生成追问内容为空")
            
        try:
            # 解析JSON内容
            probe_data = json.loads(probe_content)
            
            # 保存追问结果
            canvas.ai_probe = probe_content
            await canvas.save()
            
            return send_data(True, {
                "probes": probe_data
            })
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return send_data(False, None, "AI生成的追问内容格式错误")
        
    except Exception as e:
        logger.error(f"AI生成追问失败: {str(e)}")
        return send_data(False, None, f"AI生成追问失败: {str(e)}")

@router.post("/probe_answer", response_model=ResponseModel[dict],summary="AI追问_问题回答", deprecated=True)
async def generate_probe_answer(
    request: Request,
    canvas_id: UUID = Body(..., embed=True),
    question: str = Body(..., embed=True)
):
    """
    AI追问回答接口
    
    入参:
    - canvas_id: 需要回答的内容ID
    - question: 需要回答的问题
    
    出参:
    - 问题的回答内容
    """
    try:
        current_user = get_current_user_from_state(request)

        # 获取灵感卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=canvas_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的灵感卡片")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成提示词
        messages = generate_probe_answer_prompt(content=canvas.original_article, question=question)
        if not messages:
            return send_data(False, None, "生成提示词失败")
            
        # 调用LLM服务
        answer_content = await call_llm(
            messages=messages,
            flag=f"probe_answer_{canvas_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not answer_content:
            return send_data(False, None, "AI生成回答内容为空")
            
        return send_data(True, {
            "answer": answer_content
        })
        
    except Exception as e:
        logger.error(f"AI生成回答失败: {str(e)}")
        return send_data(False, None, f"AI生成回答失败: {str(e)}")


@router.post("/translate", response_model=ResponseModel[str], summary="AI翻译")
async def translate_content(
    request: Request,
    content: str = Body(..., embed=True)
):
    """
    AI翻译接口
    
    根据输入的文本内容进行智能翻译：
    - 中文翻译为英文
    - 英文翻译为中文  
    - 其他语言翻译为中文
    
    入参:
    - content: 需要翻译的文本内容
    
    出参:
    - 翻译后的内容
    
    注意：
    - 该接口不保存翻译结果，仅返回翻译内容
    - 直接对传入的文本内容进行翻译
    """
    try:
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI翻译")

        # 检查输入内容
        if not content or not content.strip():
            return send_data(False, None, "翻译内容不能为空")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_HANDLE_TEXT.value)

        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成翻译提示词
        messages = generate_translate_prompt(content=content.strip())
        if not messages:
            return send_data(False, None, "生成翻译提示词失败")
            
        # 调用LLM服务
        translated_content = await call_llm(
            messages=messages,
            flag=f"translate_{current_user.id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not translated_content:
            return send_data(False, None, "AI翻译内容为空")
            
        logger.info(f"用户 {current_user.username} AI翻译成功")
        return send_data(True, translated_content)
        
    except Exception as e:
        logger.error(f"AI翻译失败: {str(e)}")
        return send_data(False, None, f"AI翻译失败: {str(e)}")


@router.post("/polish", response_model=ResponseModel[str], summary="AI润色")
async def polish_content(
    request: Request,
    content: str = Body(..., embed=True)
):
    """
    AI润色接口
    
    对输入的文本内容进行专业润色，提升表达质量和可读性：
    - 优化语言表达，使文本更加流畅自然
    - 改善句式结构，提高专业性
    - 纠正语法错误，优化用词选择
    - 保持原文核心观点不变
    
    入参:
    - content: 需要润色的文本内容
    
    出参:
    - 润色后的内容
    
    注意：
    - 该接口不保存润色结果，仅返回润色内容
    - 直接对传入的文本内容进行润色
    """
    try:
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI润色")

        # 检查输入内容
        if not content or not content.strip():
            return send_data(False, None, "润色内容不能为空")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_HANDLE_TEXT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成润色提示词
        messages = generate_polish_prompt(content=content.strip())
        if not messages:
            return send_data(False, None, "生成润色提示词失败")
            
        # 调用LLM服务
        polished_content = await call_llm(
            messages=messages,
            flag=f"polish_{current_user.id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not polished_content:
            return send_data(False, None, "AI润色内容为空")
            
        logger.info(f"用户 {current_user.username} AI润色成功")
        return send_data(True, polished_content)
        
    except Exception as e:
        logger.error(f"AI润色失败: {str(e)}")
        return send_data(False, None, f"AI润色失败: {str(e)}")

@router.post("/continue", response_model=ResponseModel[str], summary="AI续写")
async def continue_content(
    request: Request,
    content: str = Body(..., embed=True)
):
    """
    AI续写接口
    
    基于输入的文本内容进行智能续写：
    - 自然延续原文的思路和风格
    - 保持语言风格和叙事基调的一致性
    - 合理推进情节或深化观点
    - 字数控制在300-500字之间
    
    入参:
    - content: 需要续写的文本内容
    
    出参:
    - 续写后的内容
    
    注意：
    - 该接口不保存续写结果，仅返回续写内容
    - 直接对传入的文本内容进行续写
    - 续写内容与原文风格保持高度一致
    """
    try:
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI续写")

        # 检查输入内容
        if not content or not content.strip():
            return send_data(False, None, "续写内容不能为空")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_HANDLE_TEXT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成续写提示词
        messages = generate_continue_prompt(content=content.strip())
        if not messages:
            return send_data(False, None, "生成续写提示词失败")
            
        # 调用LLM服务
        continued_content = await call_llm(
            messages=messages,
            flag=f"continue_{current_user.id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not continued_content:
            return send_data(False, None, "AI续写内容为空")
            
        logger.info(f"用户 {current_user.username} AI续写成功")
        return send_data(True, continued_content)
        
    except Exception as e:
        logger.error(f"AI续写失败: {str(e)}")
        return send_data(False, None, f"AI续写失败: {str(e)}")


@router.post("/abbreviate", response_model=ResponseModel[str], summary="AI缩写")
async def abbreviate_content(
    request: Request,
    content: str = Body(..., embed=True)
):
    """
    AI缩写接口
    
    对输入的文本内容进行智能缩写，精炼表达：
    - 保留核心信息和主要观点
    - 删除冗余信息和次要细节
    - 缩写后字数严格少于原文
    - 保持逻辑清晰和表达流畅
    
    入参:
    - content: 需要缩写的文本内容
    
    出参:
    - 缩写后的内容
    
    注意：
    - 该接口不保存缩写结果，仅返回缩写内容
    - 直接对传入的文本内容进行缩写
    - 缩写后的文本字数会明显少于原文
    """
    try:
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI缩写")

        # 检查输入内容
        if not content or not content.strip():
            return send_data(False, None, "缩写内容不能为空")
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_HANDLE_TEXT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成缩写提示词
        messages = generate_abbreviate_prompt(content=content.strip())
        if not messages:
            return send_data(False, None, "生成缩写提示词失败")
            
        # 调用LLM服务
        abbreviated_content = await call_llm(
            messages=messages,
            flag=f"abbreviate_{current_user.id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not abbreviated_content:
            return send_data(False, None, "AI缩写内容为空")
            
        logger.info(f"用户 {current_user.username} AI缩写成功")
        return send_data(True, abbreviated_content)
        
    except Exception as e:
        logger.error(f"AI缩写失败: {str(e)}")
        return send_data(False, None, f"AI缩写失败: {str(e)}")
