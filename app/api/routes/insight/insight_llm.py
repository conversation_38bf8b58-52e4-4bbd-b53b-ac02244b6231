from app.api.repository.user_default_model import get_user_model
from app.models.insight.inspirations_canvas_report_relation import InspirationSourceType
from app.models.organization_model_use import UseCase
from fastapi import APIRouter, Body, Request
from uuid import UUID
from enum import Enum

from app.api.deps import get_current_user_from_state
from app.utils.utils import send_data, ResponseModel
from app.core.logging import get_logger

# 导入新的 repository 层
from app.api.repository.ai_service import (
    ai_analyze_content,
    ai_generate_outline,
    ai_generate_keynotes,
    ai_generate_probe_questions,
    ai_generate_probe_answer
)
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.services.llm_service import call_llm
from app.services.insight.insight_prompts import generate_expand_prompt

logger = get_logger(__name__)
router = APIRouter()



@router.post("/expand", response_model=ResponseModel[str], summary="知识卡片AI扩写接口")
async def expand_knowledge_canvas(
    request: Request,
    content_id: UUID = Body(..., embed=True, description="内容ID"),
    content_type: InspirationSourceType = Body(..., embed=True, description="内容类型：仅支持CANVAS-知识卡片")
):
    """
    知识卡片AI扩写接口 - 仅支持知识卡片的扩写功能
    
    该接口基于知识卡片的名称、标签和原文内容，生成扩写内容
    
    入参:
    - content_id: 需要扩写的知识卡片ID
    - content_type: 内容类型（仅支持CANVAS-知识卡片）
    
    出参:
    - 扩写后的内容字符串
    
    注意：
    - 该功能仅支持知识卡片，不支持灵感库
    - 扩写结果会自动保存到数据库的ai_expanded字段中
    """
    try:
        current_user = get_current_user_from_state(request)
        
        # 检查内容类型，扩写功能仅支持知识卡片
        if content_type != InspirationSourceType.CANVAS:
            logger.warning(f"用户 {current_user.username} 尝试对非知识卡片内容进行扩写，内容类型: {content_type.value}")
            return send_data(False, None, "扩写功能仅支持知识卡片")
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI扩写，内容ID: {content_id}, 类型: {content_type.value}")
        
        # 获取知识卡片数据
        canvas = await KnowledgeCanvas.get_or_none(id=content_id, is_deleted=False)
        if not canvas:
            return send_data(False, None, "未找到对应的知识卡片")
            
        if not canvas.name:
            return send_data(False, None, "知识卡片名称为空")
            
        # 加载关联的标签
        await canvas.fetch_related('tags')
            
        # 获取模型配置
        model_config = await get_user_model(current_user, UseCase.HANDLE_TEXT.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        # 生成提示词
        tags_list = [tag.name for tag in canvas.tags] if hasattr(canvas, 'tags') else []
        messages = generate_expand_prompt(
            name=canvas.name,
            tags=tags_list,
            original_article_truncated=canvas.original_article_truncated
        )
        if not messages:
            return send_data(False, None, "生成提示词失败")
            
        # 调用LLM服务
        expanded_content = await call_llm(
            messages=messages,
            flag=f"expand_content_{content_id}",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not expanded_content:
            return send_data(False, None, "AI扩写内容为空")
            
        # 保存扩写结果
        canvas.ai_expanded = expanded_content
        await canvas.save()
        
        logger.info(f"用户 {current_user.username} AI扩写成功，内容ID: {content_id}")
        return send_data(True, expanded_content)
        
    except Exception as e:
        logger.error(f"AI扩写失败: {str(e)}")
        return send_data(False, None, f"AI扩写失败: {str(e)}")


@router.post("/analyze", response_model=ResponseModel[dict], summary="AI分析接口")
async def analyze_content(
    request: Request,
    content_id: UUID = Body(..., embed=True, description="内容ID"),
    content_type: InspirationSourceType = Body(..., embed=True, description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库")
):
    """
    AI分析接口 - 基于内容生成专业研究分析报告
    
    该接口会根据内容的名称和具体内容，生成一份结构化的专业分析报告，包含：
    1. 综合评估概览：多维度评估（3-5个维度）
    2. 详细分析：每个维度的深入分析和关键点提炼
    
    入参:
    - content_id: 需要分析的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    
    出参:
    - analysis: 生成的分析报告内容（包含评估概览和详细分析）
    
    注意：
    - 如果该内容已有分析结果，将直接返回缓存的结果
    - 分析结果会自动保存到数据库中
    """
    try:
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI分析，内容ID: {content_id}, 类型: {content_type.value}")
        
        # 使用通用的AI分析方法
        return await ai_analyze_content(
            content_id=content_id,
            content_type=content_type.value,  # 转换为字符串值
            current_user=current_user
        )
            
    except Exception as e:
        logger.error(f"AI分析失败: {str(e)}")
        return send_data(False, None, f"AI分析失败: {str(e)}")


@router.post("/outline", response_model=ResponseModel[dict], summary="AI大纲接口")
async def generate_content_outline(
    request: Request,
    content_id: UUID = Body(..., embed=True, description="内容ID"),
    content_type: InspirationSourceType = Body(..., embed=True, description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库")
):
    """
    AI大纲+结构大纲接口
    
    入参:
    - content_id: 需要生成大纲的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    
    出参:
    - 返回包含概要和大纲的字典
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI大纲生成，内容ID: {content_id}, 类型: {content_type.value}")
        
        # 使用通用的AI大纲生成方法
        return await ai_generate_outline(
            content_id=content_id,
            content_type=content_type.value,  # 转换为字符串值
            current_user=current_user
        )
        
    except Exception as e:
        logger.error(f"AI生成大纲失败: {str(e)}")
        return send_data(False, None, f"AI生成大纲失败: {str(e)}")


@router.post("/keynotes", response_model=ResponseModel[dict], summary="AI重点接口")
async def generate_content_keynotes(
    request: Request,
    content_id: UUID = Body(..., embed=True, description="内容ID"),
    content_type: InspirationSourceType = Body(..., embed=True, description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库")
):
    """
    AI重点接口
    
    入参:
    - content_id: 需要生成重点的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    
    出参:
    - 生成的重点内容，包含标题和子标题的结构化数据
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI重点生成，内容ID: {content_id}, 类型: {content_type.value}")
        
        # 使用通用的AI重点生成方法
        # 注意：ai_generate_keynotes 已经返回 send_data 格式，直接返回即可
        result = await ai_generate_keynotes(
            content_id=content_id,
            content_type=content_type.value,  # 转换为字符串值
            current_user=current_user
        )
        
        # 确保返回格式为 send_data 格式
        return result
        
    except Exception as e:
        logger.error(f"AI生成重点失败: {str(e)}")
        return send_data(False, None, f"AI生成重点失败: {str(e)}")


@router.post("/probe_questions", response_model=ResponseModel[dict], summary="AI追问_问题列表接口")
async def generate_content_probe_questions(
    request: Request,
    content_id: UUID = Body(..., embed=True, description="内容ID"),
    content_type: InspirationSourceType = Body(..., embed=True, description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库")
):
    """
    AI追问接口
    
    入参:
    - content_id: 需要生成追问的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    
    出参:
    - 生成的追问内容，包含问题列表
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI追问问题生成，内容ID: {content_id}, 类型: {content_type.value}")
        
        # 使用通用的AI追问问题生成方法
        return await ai_generate_probe_questions(
            content_id=content_id,
            content_type=content_type.value,  # 转换为字符串值
            current_user=current_user
        )
        
    except Exception as e:
        logger.error(f"AI生成追问失败: {str(e)}")
        return send_data(False, None, f"AI生成追问失败: {str(e)}")


@router.post("/probe_answer", response_model=ResponseModel[dict], summary="AI追问_问题回答接口")
async def generate_content_probe_answer(
    request: Request,
    content_id: UUID = Body(..., embed=True, description="内容ID"),
    content_type: InspirationSourceType = Body(..., embed=True, description="内容类型：CANVAS-知识卡片，INSPIRATION-灵感库"),
    question: str = Body(..., embed=True, description="需要回答的问题")
):
    """
    AI追问回答接口
    
    入参:
    - content_id: 需要回答的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    - question: 需要回答的问题
    
    出参:
    - 问题的回答内容
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        
        # 记录操作日志
        logger.info(f"用户 {current_user.username} 请求AI追问回答，内容ID: {content_id}, 类型: {content_type.value}, 问题: {question[:50]}...")
        
        # 使用通用的AI追问回答生成方法
        return await ai_generate_probe_answer(
            content_id=content_id,
            content_type=content_type.value,  # 转换为字符串值
            current_user=current_user,
            question=question
        )
        
    except Exception as e:
        logger.error(f"AI生成回答失败: {str(e)}")
        return send_data(False, None, f"AI生成回答失败: {str(e)}")