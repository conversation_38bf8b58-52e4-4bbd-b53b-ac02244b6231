from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from typing import List, Optional
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.models.insight.knowledge_canvas_tag import KnowledgeCanvasTag
from app.models.user import User
from app.api.deps import get_current_user
from pydantic import BaseModel, Field
from app.api.schemas.insight.knowledge_canvas import CanvasType, CanvasSourceType
import random
import uuid
from datetime import datetime
import aiofiles
from pathlib import Path
from app.core.logging import get_logger
from app.core.config import settings
import json

logger = get_logger(__name__)


router = APIRouter()

class MockKnowledgeCanvasRequest(BaseModel):
    """模拟知识卡片请求模型"""
    name: str = Field(..., description="知识画布名称")
    source_type: CanvasSourceType = Field(..., description="来源类型：笔记、知识、文献、灵感")
    type: CanvasType = Field(..., description="画布类型：文献、知识、短文、视频、灵感")
    author: Optional[str] = Field(None, description="作者")
    reference: Optional[str] = Field(None, description="引用")
    source: Optional[str] = Field(None, description="来源")
    detailed_description: Optional[str] = Field(None, description="详细描述")
    original_article: Optional[str] = Field(None, description="原始文章")
    original_article_truncated: Optional[str] = Field(None, description="原始文章截取")
    tags: Optional[List[str]] = Field(None, description="标签列表")

async def save_uploaded_image(file: UploadFile, user_id: int, canvas_id: int) -> str:
    """
    保存用户上传的图片
    
    Args:
        file: 上传的文件
        user_id: 用户ID
        canvas_id: 灵感卡片ID
        
    Returns:
        str: 保存后的本地图片相对路径
    """
    try:
        # 构建保存路径，使用正斜杠
        save_dir = Path("images") / str(canvas_id)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        file_extension = file.filename.split('.')[-1] if file.filename else 'jpg'
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        save_path = save_dir / unique_filename
        
        # 保存文件
        async with aiofiles.open(save_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # 返回相对路径，确保使用正斜杠
        return str(save_path).replace('\\', '/')
        
    except Exception as e:
        logger.error(f"保存上传图片失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"保存上传图片失败: {str(e)}"
        )

@router.post("/mock/knowledge-canvas", summary="创建模拟知识卡片数据")
async def create_mock_knowledge_canvas(
    name: str = Form(...),
    source_type: CanvasSourceType = Form(...),
    type: CanvasType = Form(...),
    author: Optional[str] = Form(None),
    reference: Optional[str] = Form(None),
    source: Optional[str] = Form(None),
    detailed_description: Optional[str] = Form(None),
    original_article: Optional[str] = Form(None),
    original_article_truncated: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    image: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_user)
):
    """
    创建模拟知识卡片数据
    :param name: 知识画布名称
    :param source_type: 来源类型
    :param type: 画布类型
    :param author: 作者
    :param reference: 引用
    :param source: 来源
    :param detailed_description: 详细描述
    :param original_article: 原始文章
    :param original_article_truncated: 原始文章截取
    :param tags: 标签列表，JSON字符串格式，例如: ["tag1", "tag2"]
    :param image: 上传的图片文件
    :param current_user: 当前用户
    :return: 创建的数据
    """
    try:
        logger.info(f"创建模拟知识卡片数据: name={name}, source_type={source_type}, type={type}")
        
        # 解析tags参数
        parsed_tags = None
        if tags:
            try:
                parsed_tags = json.loads(tags)
                if not isinstance(parsed_tags, list):
                    parsed_tags = None
                    logger.warning(f"无效的tags格式: {tags}，应为JSON数组字符串")
            except json.JSONDecodeError:
                logger.warning(f"解析tags失败: {tags}，应为JSON数组字符串")
        
        # 创建知识卡片
        canvas = KnowledgeCanvas(
            name=name,
            source_type=source_type,
            type=type,
            author=author,
            reference=reference,
            source=source,
            detailed_description=detailed_description,
            original_article=original_article,
            original_article_truncated=original_article_truncated,
            user_id=current_user.id,  # 使用user_id而不是user对象
            note_update_at=datetime.now()
        )
        await canvas.save()
        
        # 如果有tags，通过m2m_manager设置
        if parsed_tags:
            try:
                # 为每个标签创建或获取KnowledgeCanvasTag对象
                for tag_name in parsed_tags:
                    # 检查标签是否已存在
                    tag = await KnowledgeCanvasTag.filter(
                        name=tag_name,
                        user_id=current_user.id,
                        is_deleted=False
                    ).first()
                    
                    if not tag:
                        # 创建新标签
                        tag = await KnowledgeCanvasTag.create(
                            name=tag_name,
                            user_id=current_user.id
                        )
                    
                    # 添加标签关联
                    await canvas.tags.add(tag)
                    
            except Exception as e:
                logger.warning(f"设置标签失败: {str(e)}")
        
        # 如果有上传图片，保存图片并更新知识卡片
        if image:
            try:
                image_path = await save_uploaded_image(image, current_user.id, canvas.id)
                canvas.image_url = image_path
                await canvas.save()
            except Exception as e:
                logger.error(f"处理上传图片失败: {str(e)}")
                # 继续处理，不中断整个请求
        
        return {
            "code": 200,
            "message": "成功创建模拟数据",
            "data": canvas
        }
    except Exception as e:
        logger.error(f"创建知识卡片失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"创建知识卡片失败: {str(e)}"
        ) 