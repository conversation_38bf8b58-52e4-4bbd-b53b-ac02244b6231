from fastapi import APIRouter
from app.api.routes.insight.knowledge_canvas import router as knowledge_canvas_router
from app.api.routes.insight.knowledge_note import router as knowledge_note_router
from app.api.routes.insight.knowledge_llm import router as knowledge_llm_router
from app.api.routes.insight.inspiration import router as inspiration_router
from app.api.routes.insight.knowledge_mock import router as knowledge_mock_router
from app.api.routes.insight.insight_llm import router as insight_llm_router
from app.api.routes.insight.insight_report import router as insight_report_router

router = APIRouter()

router.include_router(knowledge_canvas_router, prefix="/knowledge-canvas", tags=["灵感卡片"])
router.include_router(knowledge_note_router, prefix="/knowledge-note", tags=["泛知识"])
# 新的统一AI功能接口
router.include_router(insight_llm_router, prefix="/insight-llm", tags=["灵感LLM"])
# 以下路由已废弃，建议使用统一的insight-llm接口
router.include_router(knowledge_llm_router, prefix="/knowledge-llm", tags=["灵感卡片LLM(已废弃)"])
router.include_router(inspiration_router, prefix="/inspiration", tags=["灵感库"])
router.include_router(knowledge_mock_router, prefix="/insight", tags=["灵感卡片Mock数据"])
#insight报告相关接口
router.include_router(insight_report_router, prefix="/insight-report", tags=["insight报告"]) 