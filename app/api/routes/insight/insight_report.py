"""
insight报告文档路由模块

功能概述：
- 提供基于知识卡片和灵感库的报告生成功能
- 支持流式和同步两种生成模式
- 自动建立内容与报告的多对多关联关系
- 提供报告详情查询和下载功能

多对多关联关系管理：
本模块在报告生成成功后，会自动创建以下关联关系：
1. 通过 InspirationsCanvasReportRelation 表建立多对多关系
2. 支持 KNOWLEDGE_CANVAS（知识卡片）和 INSPIRATION（灵感库）两种内容类型
3. 避免重复创建相同的关联关系
4. 提供关联内容查询功能

使用示例：
1. 生成报告：POST /generate-report-sync
2. 查看报告：GET /{report_id}
3. 查看关联内容：GET /{report_id}/related-content
4. 下载报告：GET /{report_id}/download
"""
from fastapi import APIRouter, Depends, Request, HTTPException, status
from fastapi.responses import StreamingResponse
from uuid import UUID
import asyncio
import json
import time
from datetime import datetime

from app.api.deps import get_current_user_from_state
from app.api.schemas.insight.insight_report import GenerateReportRequest
from app.utils.utils import ResponseModel, ContentStatus, convert_markdown_to_docx
from app.utils.enum import ErrorType
from app.services.insight.insight_report_service import generate_content_report_stream, generate_content_report_sync, get_insight_report_detail, get_report_related_content
from app.core.logging import get_logger
from app.utils.content_manager import ContentManager
from app.models.insight.hi_insight_report import InsightReportStatus, HiInsightReport
# 创建路由实例
router = APIRouter()
logger = get_logger(__name__)

# 创建内容管理器用于流式处理
insight_report_content_manager = ContentManager()


@router.post(
    "/generate-report",
    response_model=ResponseModel[dict],
    summary="生成完整报告（流式）",
    deprecated=True,
    description="""
    根据知识卡片或灵感库内容生成指定类型的完整报告。
    

    
    流程说明：
    1. 验证内容存在性和用户权限
    2. 创建insight报告记录
    3. 启动异步AI报告生成任务
    4. 立即返回任务状态
    5. 通过流式接口获取实时生成内容
    
    请求示例:
    ```json
    {
        "content_id": "550e8400-e29b-41d4-a716-************",
        "content_type": "CANVAS" OR "INSPIRATION",
        "report_type": "RESEARCH_REPORT"
    }
    ```
    
    响应示例:
    ```json
    {
        "success": true,
        "data": {
            "report_id": "660e8400-e29b-41d4-a716-************",
            "status": "generating",
            "message": "报告生成任务已启动，请通过流式接口获取实时内容"
        },
        "message": "报告生成任务启动成功"
    }
    ```
    
    入参:
    - content_id: 需要生成报告的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    - report_type: 报告类型枚举值
    
    
    出参:
    - success: 是否启动成功
    - data: 包含report_id和任务状态的字典
    - message: 操作结果消息
    """
)
async def create_content_report(
    req: GenerateReportRequest,
    request: Request
):
    """
    生成完整报告接口（流式）
    
    启动基于用户提供的知识卡片或灵感库内容的报告生成任务。
    该接口会：
    1. 创建一个新的insight报告记录
    2. 启动异步AI报告生成任务
    3. 立即返回任务状态，无需等待生成完成
    4. 用户可通过流式接口实时获取生成内容
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求生成报告: 内容ID={req.content_id}, 类型={req.content_type.value}, 报告类型={req.report_type.value}")
        
        # 调用服务层启动流式生成报告
        result = await generate_content_report_stream(
            content_id=req.content_id,
            content_type=req.content_type.value,
            report_type=req.report_type.value,
            current_user=current_user,
            content_manager=insight_report_content_manager
        )
        
        return result
        
    except Exception as e:
        logger.error(f"生成报告接口失败: {str(e)}")
        from app.utils.utils import send_data
        return send_data(False, None, f"生成报告失败: {str(e)}")


@router.post(
    "/generate-report-sync",
    response_model=ResponseModel[dict],
    summary="生成完整报告（同步）",
    description="""
    根据知识卡片或灵感库内容生成指定类型的完整报告（同步版本）。
    
   
    支持的报告类型：
        OPENING_REPORT = "OPENING_REPORT" # 开题报告
        INDUSTRY_ANALYSIS = "INDUSTRY_ANALYSIS" # 行业分析
        SUBJECT_REPORT = "SUBJECT_REPORT"  # 课题报告
        PATENT_ANALYSIS = "PATENT_ANALYSIS"  # 专利分析
        TECH_REVIEW = "TECH_REVIEW"  # 技术综述
        RESEARCH_REPORT = "RESEARCH_REPORT" # 研究报告

    
    
    请求示例:
    ```json
    {
        "content_id": "550e8400-e29b-41d4-a716-************",
        "content_type": "CANVAS",
        "report_type": "RESEARCH_REPORT"
    }
    ```
    
    响应示例:
    ```json
    {
        "success": true,
        "data": {
            "report_id": "660e8400-e29b-41d4-a716-************",
            "title": "报告标题",
            "content": "完整的报告内容...",
            "content_name": "知识卡片名称",
            "content_id": "550e8400-e29b-41d4-a716-************",
            "content_type": "CANVAS",
            "report_type": "RESEARCH_REPORT",
            "status": "completed",
            "generation_info": {
                "generation_time": 120,
                "model_name": "gpt-4",
                "word_count": 5000,
                "input_tokens": 1000,
                "output_tokens": 2000,
                "total_tokens": 3000
            },
            "created_at": "2024-01-01T12:00:00",
            "file_path": "llm_file/insight_reports/xxx/report.txt"
        },
        "message": "报告生成成功"
    }
    ```
    
    入参:
    - content_id: 需要生成报告的内容ID（知识卡片ID或灵感库ID）
    - content_type: 内容类型（CANVAS-知识卡片，INSPIRATION-灵感库）
    - report_type: 报告类型枚举值
    
    
    出参:
    - success: 是否生成成功
    - data: 包含完整报告内容和统计信息的字典
    - message: 操作结果消息
    
    注意事项：
    - 该接口可能需要较长时间才能返回结果，请确保前端设置足够的超时时间
    - 建议在大型报告生成时使用流式版本以获得更好的用户体验
    - 生成的报告会同时保存在数据库和文件系统中
    """
)
async def create_content_report_sync(
    req: GenerateReportRequest,
    request: Request
):
    """
    生成完整报告接口（同步版本）
    
    同步生成基于用户提供的知识卡片或灵感库内容的报告。
    该接口会：
    1. 创建一个新的insight报告记录
    2. 收集相关背景信息
    3. 调用LLM同步生成完整报告
    4. 保存报告到文件并更新数据库
    5. 直接返回完整的报告内容
    
    与流式版本相比，该接口更适合需要一次性获取完整报告的场景。
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求同步生成报告: 内容ID={req.content_id}, 类型={req.content_type.value}, 报告类型={req.report_type.value}")
        
        # 调用服务层同步生成报告
        result = await generate_content_report_sync(
            content_id=req.content_id,
            content_type=req.content_type.value,
            report_type=req.report_type.value,
            current_user=current_user
        )
        
        return result
        
    except Exception as e:
        logger.error(f"同步生成报告接口失败: {str(e)}")
        from app.utils.utils import send_data
        return send_data(False, None, f"同步生成报告失败: {str(e)}")


@router.get("/{report_id}/stream", summary="流式获取报告生成内容", deprecated=True)
async def stream_insight_report(
    report_id: str,
    request: Request
):
    """
    流式返回insight报告生成内容（SSE格式）
    
    实时返回正在生成的报告内容，支持：
    - 实时内容流式传输
    - 生成状态跟踪
    - 错误处理和状态反馈
    - 生成完成通知
    """
    try:
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求流式获取报告内容: report_id={report_id}")
        
        # 验证报告权限
        report = await HiInsightReport.filter(id=report_id, user_id=current_user.id, is_deleted=False).first()
        if not report:
            return StreamingResponse(
                content=iter([f"data: {json.dumps({'content': '报告不存在或无权限访问', 'status': ContentStatus.ERROR})}\n\n"]), 
                media_type="text/event-stream"
            )
        
        # 检查是否正在生成
        if report.status == InsightReportStatus.PROCESSING:
            async def stream_realtime_content():
                send_length = 0
                read_count = 0
                project_content = insight_report_content_manager.get_project_content(report_id)
                
                if not project_content:
                    yield f"data: {json.dumps({'content': '找不到正在生成的内容', 'status': ContentStatus.ERROR})}\n\n"
                    return
                
                # 先发送已有的内容
                for chunk in project_content.read_chunks:
                    yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                
                # 持续检查新内容
                while report.status == InsightReportStatus.PROCESSING:
                    chunk = insight_report_content_manager.read_next_chunk(report_id)
                    if chunk:
                        yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                        read_count += 1
                    else:
                        # 暂无新内容，等待并发送心跳
                        await asyncio.sleep(0.5)
                        await report.refresh_from_db()
                        yield f"data: {json.dumps({'content': 'thinking', 'status': ContentStatus.HEART_BEAT})}\n\n"
                
                # 发送剩余未读内容
                project_content = insight_report_content_manager.get_project_content(report_id)
                if project_content and project_content.unread_chunks:
                    for chunk in project_content.unread_chunks:
                        yield f"data: {json.dumps({'content': chunk.content, 'status': chunk.status})}\n\n"
                        read_count += 1
                
                # 生成完成
                yield f"data: {json.dumps({'status': 'completed', 'total_chunks': read_count})}\n\n"
                insight_report_content_manager.clear_project(report_id)
                
            return StreamingResponse(
                content=stream_realtime_content(),
                media_type="text/event-stream"
            )
        
        # 如果已生成完成，从文件读取内容
        elif report.status == InsightReportStatus.COMPLETED and report.ai_generated_report:
            # 参考 project_report.py 的方式，从文件读取内容
            from app.utils.utils import stream_file_content_sse
            
            # 清理内容管理器
            insight_report_content_manager.clear_project(report_id)
            
            # 返回SSE流式响应（从文件读取）
            return StreamingResponse(
                content=stream_file_content_sse(report.ai_generated_report, current_user), 
                media_type="text/event-stream"
            )
        
        # 其他状态（失败、取消等）
        else:
            status_message = {
                InsightReportStatus.FAILED: '报告生成失败',
                InsightReportStatus.CANCELED: '报告生成已取消'
            }.get(report.status, '报告尚未生成或状态异常')
            
            return StreamingResponse(
                content=iter([f"data: {json.dumps({'content': status_message, 'status': ContentStatus.ERROR})}\n\n"]), 
                media_type="text/event-stream"
            )
        
    except Exception as e:
        logger.error(f"流式获取报告内容失败: {str(e)}")
        return StreamingResponse(
            content=iter([f"data: {json.dumps({'content': f'获取报告内容失败: {str(e)}', 'status': ContentStatus.ERROR})}\n\n"]), 
            media_type="text/event-stream"
        )


@router.post("/{report_id}/stop", summary="停止报告生成", deprecated=True)
async def stop_report_generation(
    report_id: str,
    request: Request
):
    """
    停止正在进行的报告生成任务
    """
    try:
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求停止报告生成: report_id={report_id}")
        
        # 验证报告权限
        report = await HiInsightReport.filter(id=report_id, user_id=current_user.id, is_deleted=False).first()
        if not report:
            from app.utils.utils import send_data
            return send_data(False, False, "报告不存在或无权限访问")
        
        # 检查当前状态
        if report.status != InsightReportStatus.PROCESSING:
            from app.utils.utils import send_data
            return send_data(False, False, "当前没有正在生成的报告")
        
        # 获取并取消异步任务
        project_content = insight_report_content_manager.get_project_content(report_id)
        if project_content and project_content.asyncioInstance:
            try:
                project_content.asyncioInstance.cancel()
                error_msg = "报告生成已被用户取消"
                insight_report_content_manager.add_content(report_id, error_msg, ContentStatus.ERROR)
                logger.info(f"已取消报告 {report_id} 的生成任务")
            except Exception as e:
                error_msg = f"取消报告生成任务时出错: {str(e)}"
                insight_report_content_manager.add_content(report_id, error_msg, ContentStatus.ERROR)
                logger.error(error_msg)
        
        # 更新报告状态
        report.status = InsightReportStatus.CANCELED
        await report.save()
        
        from app.utils.utils import send_data
        return send_data(True, True, "已成功停止报告生成")
        
    except Exception as e:
        logger.error(f"停止报告生成失败: {str(e)}")
        from app.utils.utils import send_data
        return send_data(False, False, f"停止报告生成失败: {str(e)}")


@router.get(
    "/{report_id}",
    response_model=ResponseModel[dict],
    summary="获取报告详情",
    description="""
    根据报告ID获取报告的详细信息，包括完整的报告内容。
    
    功能特点：
    - **完整内容**: 从文件系统中读取并返回完整的报告内容
    - **详细信息**: 包含报告的基础信息、生成统计、状态等
    - **权限控制**: 只能查看自己创建的报告
    - **状态感知**: 根据不同的报告状态返回相应的提示信息
    
    报告状态说明：
    - PROCESSING: 正在生成中，返回提示信息
    - COMPLETED: 生成完成，返回完整内容
    - FAILED: 生成失败，返回失败提示
    - CANCELED: 已取消，返回取消提示
    
    请求示例:
    ```
    GET /reports/660e8400-e29b-41d4-a716-************
    ```
    
    响应示例:
    ```json
    {
        "success": true,
        "data": {
            "id": "660e8400-e29b-41d4-a716-************",
            "title": "人工智能发展趋势研究报告",
            "report_type": "RESEARCH_REPORT",
            "status": "COMPLETED",
            "content": "这里是完整的报告内容...",
            "word_count": 5000,
            "generation_info": {
                "generation_time": 120,
                "model_name": "gpt-4",
                "input_tokens": 1000,
                "output_tokens": 2000,
                "total_tokens": 3000,
                "report_generation_time": "2024-01-01T12:00:00"
            },
            "created_at": "2024-01-01T11:58:00",
            "updated_at": "2024-01-01T12:00:00",
            "file_path": "llm_file/insight_reports/xxx/report.txt"
        },
        "message": "获取报告详情成功"
    }
    ```
    
    出参:
    - success: 是否获取成功
    - data: 报告详情数据，包含完整内容和统计信息
    - message: 操作结果消息
    
    注意事项：
    - 只能查看自己创建的报告
    - 如果报告正在生成中，content字段会显示提示信息
    - 文件读取失败时会在content字段中显示错误信息
    """
)
async def get_report_detail(
    report_id: str,
    request: Request
):
    """
    获取报告详情接口
    
    通过报告ID查询报告的详细信息，包括：
    1. 报告基本信息（标题、类型、状态等）
    2. 完整的报告内容（从文件中读取）
    3. 生成统计信息（耗时、token使用量等）
    4. 创建和更新时间
    
    该接口会根据报告状态返回不同的内容：
    - 已完成的报告：返回完整的报告内容
    - 正在生成的报告：返回生成进度提示
    - 失败的报告：返回失败原因
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求查看报告详情: report_id={report_id}")
        
        # 调用服务层获取报告详情
        result = await get_insight_report_detail(
            report_id=UUID(report_id),
            current_user=current_user
        )
        
        return result
        
    except ValueError as e:
        logger.error(f"无效的报告ID格式: {report_id}")
        from app.utils.utils import send_data
        return send_data(False, None, "无效的报告ID格式")
    except Exception as e:
        logger.error(f"获取报告详情接口失败: {str(e)}")
        from app.utils.utils import send_data
        return send_data(False, None, f"获取报告详情失败: {str(e)}")


@router.get("/{report_id}/download", summary="下载AI文档-Word")
async def download_insight_report(
    report_id: str,
    request: Request
):
    """
    下载AI文档-Word文档接口
    
    将已生成的AI文档转换为Word文档格式并下载。
    
    
    请求示例:
    ```
    GET /reports/660e8400-e29b-41d4-a716-************/download
    ```
    
    响应:
    - 成功: 返回Word文档文件流
    - 失败: 返回错误信息和HTTP错误状态码
    
    错误情况：
    - 400: 报告ID为空
    - 404: 报告不存在
    - 403: 无权限访问
    - 422: 报告尚未生成完成
    - 500: 服务器内部错误
    
    注意事项：
    - 只能下载状态为COMPLETED的报告
    - 报告文件必须存在才能下载
    - 下载的文件名会包含报告标题和时间戳
    """
    # 获取当前用户
    current_user = get_current_user_from_state(request)
    
    # 验证报告ID
    if not report_id:
        logger.warning(f"用户 {current_user.username} 请求下载报告时提供了空的报告ID")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="报告ID不能为空"
        )
    
    try:
        # 查询报告信息
        report = await HiInsightReport.filter(
            id=report_id, 
            is_deleted=False
        ).prefetch_related("user", "user__organization").first()
        
        if not report:
            logger.warning(f"用户 {current_user.username} 尝试下载不存在的报告: {report_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报告不存在"
            )
        
        # 权限验证：只能下载自己创建的报告
        if report.user_id != current_user.id:
            logger.warning(f"用户 {current_user.username} 尝试下载其他用户的报告: {report_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ErrorType.FORBIDDEN.value
            )
        
        # 检查报告状态
        if report.status != InsightReportStatus.COMPLETED:
            logger.warning(f"用户 {current_user.username} 尝试下载未完成的报告: {report_id}, 状态: {report.status}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="报告尚未生成完成，无法下载"
            )
        
        # 检查报告文件是否存在
        if not report.ai_generated_report:
            logger.warning(f"用户 {current_user.username} 尝试下载缺失文件的报告: {report_id}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="报告文件不存在"
            )
        
        # 转换为Word文档并返回
        logger.info(f"用户 {current_user.username} 开始下载报告: {report_id}")
        return convert_markdown_to_docx(report.ai_generated_report)
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"用户 {current_user.username} 下载报告 {report_id} 时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载报告失败: {str(e)}"
        )


@router.get(
    "/{report_id}/related-content",
    response_model=ResponseModel[dict],
    summary="获取报告关联内容",
    description="""
    获取与指定报告相关的灵感库和知识卡片信息。
    
    功能特点：
    - **关联查询**: 通过多对多关系表查询与报告相关的所有内容
    - **详细信息**: 返回关联内容的基本信息（ID、名称、类型等）
    - **权限控制**: 只能查看自己创建的报告的关联内容
    - **统计信息**: 包含关联内容的数量统计
    
    请求示例:
    ```
    GET /reports/660e8400-e29b-41d4-a716-************/related-content
    ```
    
    响应示例:
    ```json
    {
        "success": true,
        "data": {
            "report_id": "660e8400-e29b-41d4-a716-************",
            "related_content_count": 2,
            "related_content": [
                {
                    "relation_id": "770e8400-e29b-41d4-a716-446655440002",
                    "content_id": "550e8400-e29b-41d4-a716-************",
                    "content_type": "CANVAS",
                    "content_name": "人工智能发展趋势",
                    "created_at": "2024-01-01T12:00:00"
                },
                {
                    "relation_id": "880e8400-e29b-41d4-a716-446655440003",
                    "content_id": "660e8400-e29b-41d4-a716-446655440004",
                    "content_type": "INSPIRATION",
                    "content_name": "创新思维方法",
                    "created_at": "2024-01-01T12:01:00"
                }
            ]
        },
        "message": "获取报告关联内容成功"
    }
    ```
    
    出参:
    - success: 是否获取成功
    - data: 关联内容数据，包含统计信息和详细列表
    - message: 操作结果消息
    
    注意事项：
    - 只能查看自己创建的报告的关联内容
    - 返回的是通过多对多关系表建立的关联关系
    - 关联内容的详细信息需要通过相应的API进一步获取
    """
)
async def get_report_related_content_api(
    report_id: str,
    request: Request
):
    """
    获取报告关联内容接口
    
    通过报告ID查询与该报告相关的所有灵感库和知识卡片。
    该接口会：
    1. 验证报告权限（只能查看自己的报告）
    2. 查询多对多关系表中的所有关联记录
    3. 获取关联内容的基本信息
    4. 返回关联内容列表和统计信息
    
    该接口可用于：
    - 了解报告基于哪些内容生成
    - 追踪内容与报告之间的关联关系
    - 为用户提供相关内容的导航
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求查看报告关联内容: report_id={report_id}")
        
        # 调用服务层获取关联内容
        result = await get_report_related_content(
            report_id=UUID(report_id),
            current_user=current_user
        )
        
        return result
        
    except ValueError as e:
        logger.error(f"无效的报告ID格式: {report_id}")
        from app.utils.utils import send_data
        return send_data(False, None, "无效的报告ID格式")
    except Exception as e:
        logger.error(f"获取报告关联内容接口失败: {str(e)}")
        from app.utils.utils import send_data
        return send_data(False, None, f"获取报告关联内容失败: {str(e)}")


