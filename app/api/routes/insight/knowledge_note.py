from app.api.repository.user_default_model import get_user_model
from app.models.organization_model_use import UseCase
from fastapi import APIRouter, Depends, Query, HTTPException, status
from typing import List, Optional, Dict, Any
from datetime import datetime
import httpx
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON>earer
import json
from pydantic import BaseModel
import asyncio
import re
from bs4 import BeautifulSoup

from app.api.schemas.user import UserResponse
from app.utils.utils import PageQuery, send_data, ResponseModel, send_page_data, ResponsePageModel
from app.core.logging import get_logger
from app.api.deps import get_current_user, oauth2_scheme
from app.core.config import settings
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.models.insight.knowledge_canvas_tag import KnowledgeCanvasTag
from app.api.schemas.insight.knowledge_canvas import (
    CanvasSourceType,
    CanvasType,
    CreateKnowledgeCanvasRequest,
    KnowledgeCanvasResponse,
    KnowledgeNoteItem
)
from app.services.insight.knowledge_canvas_service import (
    download_and_save_image, 
    process_canvas_summary,
    generate_ai_questions,
    clean_and_truncate_html
)
from app.services.product_configs_service import ProductConfigsService
from app.models.model_config import ModelConfig

router = APIRouter()
logger = get_logger(__name__)

async def call_wzbj_api(
    endpoint: str,
    method: str = "GET",
    params: Optional[Dict] = None,
    json_data: Optional[Dict] = None,
    headers: Optional[Dict] = None,
    token: Optional[str] = None
) -> Dict:
    """
    调用外部API的通用方法
    
    Args:
        endpoint: API端点路径
        method: 请求方法 (GET/POST)
        params: URL参数
        json_data: POST请求的JSON数据
        headers: 请求头
        token: 认证token
    
    Returns:
        Dict: API响应数据
    """
    try:
        url = f"{settings.WZBJ_API_URL}{endpoint}"
        if headers is None:
            headers = {}
        if token:
            headers["Authorization"] = f"Bearer {token}"
            
        # 设置30秒超时
        timeout = httpx.Timeout(30.0)
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            if method.upper() == "GET":
                response = await client.get(url, params=params, headers=headers)
            else:
                response = await client.post(url, json=json_data, headers=headers)
                
            if response.status_code != 200:
                logger.error(f"API调用失败: {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"API调用失败: {response.text}"
                )
                
            return response.json()
    except Exception as e:
        logger.error(f"API调用异常: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"API调用异常: {str(e)}"
        )

@router.get(
    "/notes/list",
    response_model=ResponsePageModel[KnowledgeNoteItem],
    summary="查询泛知识列表",
    description="转发到其他系统，支持关键词、分页、排序等参数。"
)
async def query_notes(
    keyword: Optional[str] = None,
    page_query: PageQuery = Depends(),
    sort_field: str = Query('edit_time', regex='^(edit_time|title)$', description='排序字段，edit_time或title'),
    sort_order: str = Query('desc', regex='^(desc|asc)$', description='排序方式，desc或asc'),
    current_user: UserResponse = Depends(get_current_user),
    token: str = Depends(oauth2_scheme)
):
    """查询泛知识列表，转发到其他系统，并二次加工返回数据"""
    try:
        page = page_query.page
        size = page_query.size
        
        request_data = {
            "keyword": keyword,
            "pageNum": page,
            "pageSize": size,
            "sortField": sort_field,
            "sortOrder": sort_order
        }
        
        result = await call_wzbj_api(
            endpoint="/api/hiInsight/queryNotes",
            method="POST",
            json_data=request_data,
            token=token
        )
        
        notes = result.get("data", {}).get("list", [])
        total = result.get("data", {}).get("total", 0)
        
        processed = []
        for note in notes:
            raw_time = note.get("editTime") or note.get("createTime")
            # 直接使用原始时间，不做格式化处理
            note_item = KnowledgeNoteItem(
                basicId=str(note.get("basicId", "")),  # 确保是字符串类型
                title=str(note.get("title", "")),      # 确保是字符串类型
                url=str(note.get("url", "")) if note.get("url") else None,
                update_time=raw_time,
                tags=[str(tag.get("tagName", "")) for tag in note.get("tags", []) if tag.get("tagName")],
                summary=str(note.get("summary", "")) if note.get("summary") else None,
                type=str(note.get("type", "")) if note.get("type") else None # 1 富文本 2 md
            )
            processed.append(note_item)
        logger.info(f"processed: {processed}")    
        # 构建分页响应
        paginated_response = {
            "items": processed,
            "total": total,
            "page": page,
            "size": size
        }
        
        return send_page_data(True, paginated_response)
    except Exception as e:
        logger.error(f"查询泛知识列表失败: {str(e)}")
        return send_page_data(
            False,
            {
                "items": [],
                "total": 0,
                "page": page,
                "size": size
            },
            f"查询泛知识列表失败: {str(e)}"
        )
    

@router.get(
    "/notes/detail",
    response_model=ResponseModel[List[dict]],
    summary="批量获取泛知识详情",
    description="根据basicIds批量获取泛知识详情，转发到其他系统并二次加工返回数据。"
)
async def get_note_detail(
    basicIds: List[str] = Query(..., description="泛知识ID列表"),
    current_user: UserResponse = Depends(get_current_user),
    token: str = Depends(oauth2_scheme)
):
    """根据basicIds批量查询泛知识详情，转发到其他系统并二次加工返回数据"""
    try:
        params = [("basicIds", bid) for bid in basicIds]
        result = await call_wzbj_api(
            endpoint="/api/hiInsight/getNoteDetail",
            params=params,
            token=token
        )
        
        notes_list = result.get("data", {}).get("notes", [])
        processed = []
        for note in notes_list:
            notebasic = note.get("notebasic", {})
            notetext = note.get("notetext", {})
            
            raw_time = notetext.get("editTime") or notetext.get("createTime")
           
            processed.append({
                "basicId": notebasic.get("basicId"),
                "update_time": raw_time,
                "content": notetext.get("noteContent"),
                "type": notetext.get("type")
            })
           
        return send_data(True, processed)
    except Exception as e:
        logger.error(f"查询泛知识详情失败: {str(e)}")
        return send_data(False, None, f"查询泛知识详情失败: {str(e)}")

@router.post(
    "/notes/create",
    response_model=ResponseModel[KnowledgeCanvasResponse],
    summary="泛知识选择创建灵感卡片",
    description="""
    根据选择的泛知识ID列表创建灵感卡片。
    
    请求体格式：
    {
        "basic_ids": ["泛知识ID1", "泛知识ID2", ...]
    }
    """
)
async def create_knowledge_canvas(
    request: CreateKnowledgeCanvasRequest,
    current_user: UserResponse = Depends(get_current_user),
    token: str = Depends(oauth2_scheme)
):
    """创建新的灵感卡片，接收basic_ids数组"""
    try:
        
        #获取用户默认模型
        model_config = await get_user_model(current_user, UseCase.INSIGHT_GENERATE.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
            
        logger.info(f"model_config: {model_config}")
        # 1. 根据basic_ids获取泛知识详情
        params = [("basicIds", bid) for bid in request.basic_ids]
        result = await call_wzbj_api(
            endpoint="/api/hiInsight/getNoteDetail",
            params=params,
            token=token
        )
        notes_list = result.get("data", {}).get("notes", [])
        if not notes_list:
            return send_data(False, None, "未找到相关泛知识")
        
        # 2. 根据泛知识详情生成灵感卡片
        knowledge_canvases = []
        for note in notes_list:
            notebasic = note.get("notebasic", {})
            notetext = note.get("notetext", {})

            # 获取泛知识更新时间
            raw_time = notebasic.get("editTime") or notebasic.get("createTime")
            update_time = None
            if raw_time:
                try:
                    update_time = datetime.fromisoformat(raw_time.replace('Z', '+00:00'))
                except Exception:
                    update_time = datetime.strptime(raw_time[:10], '%Y-%m-%d')
            # 构建 source 数据
            source_data = [{
                'source_id': notebasic.get("basicId", ""),
                'source_type': CanvasSourceType.NOTE.value,
                'source_name': notebasic.get("title", "未命名泛知识")
            }]
            logger.info(f"获取泛知识详情: {notebasic}")
            # 创建灵感卡片
            canvas = await KnowledgeCanvas.create(
                name=notebasic.get("title", "未命名泛知识"),
                source_type=CanvasSourceType.NOTE.value,
                type=CanvasType.NOTE.value,
                summary="",  # 添加初始summary
                key_notes=json.dumps(note.get("key_notes", [])),  # 将列表转换为JSON字符串存储
                original_article=notetext.get("noteContent", ""), 
                original_article_truncated=clean_and_truncate_html(notetext.get("noteContent", "")),
                related_notes=json.dumps(note.get("related_notes", [])),  # 转换为JSON字符串
                source=json.dumps(source_data),  # 将 source 数据转换为 JSON 字符串
                image_url="",  # 初始化为空字符串
                user_id=current_user.id,
                note_type=notebasic.get("type"),
                created_at= datetime.now(),
                updated_at= datetime.now(),
                note_update_at=update_time
            )
            
           
            
            # AI提问
            # if model_config:
            #     ai_questions = await generate_ai_questions(
            #         content=notetext.get("noteContent", ""),
            #         api_key=model_config.api_key,
            #         api_url=model_config.api_url,
            #         model=model_config.model_name
            #     )
            #     canvas.ai_questions = ai_questions
            
            # 处理图片
            thumbnail = notebasic.get("thumbnail", "")
            if thumbnail:
                try:
                    local_image_path = await download_and_save_image(
                        image_url=thumbnail,
                        user_id=current_user.id,
                        canvas_id=canvas.id
                    )
                    canvas.image_url = local_image_path
                except Exception as e:
                    logger.error(f"处理图片失败: {str(e)}")
                    # 图片处理失败不影响其他操作，继续使用默认的空图片URL
            
            # 保存卡片（无论图片处理是否成功）
            await canvas.save()
            
            # 处理标签
            tags = note.get("tags", [])
            for tag_name in tags:
                # 查找或创建标签
                tag, created = await KnowledgeCanvasTag.get_or_create(
                    name=tag_name,
                    user_id=current_user.id,
                    defaults={
                        "is_deleted": False
                    }
                )
                # 添加标签关联
                await canvas.tags.add(tag)
            
            # 转换为响应模型
            canvas_response = KnowledgeCanvasResponse(
                id=canvas.id,
                name=canvas.name,
                source_type=canvas.source_type,
                type=canvas.type,
                summary=canvas.summary,  # 添加summary字段
                key_notes=note.get("key_notes", []),  # 直接使用API返回的列表
                related_notes=json.loads(canvas.related_notes) if canvas.related_notes else [],
                ai_questions=json.loads(canvas.ai_questions) if canvas.ai_questions else [],
                image_url=canvas.image_url,
                original_article_truncated=canvas.original_article_truncated,
                tags=tags,  # 使用API返回的标签列表
                created_at=canvas.created_at,
                updated_at=canvas.updated_at,
                is_deleted=canvas.is_deleted,
                note_update_at=canvas.note_update_at,
                note_type=canvas.note_type
            )
            
            # 处理概要生成
            await process_canvas_summary(
                canvas_id=canvas.id,
                html_content=notetext.get("noteContent", ""),
                model_config=model_config
            )
            knowledge_canvases.append(canvas_response)
        
        # 3. 返回创建的灵感卡片列表
        return send_data(True, knowledge_canvases[0] if knowledge_canvases else None)
        
    except Exception as e:
        logger.error(f"创建灵感卡片失败: {str(e)}")
        return send_data(False, None, f"创建灵感卡片失败: {str(e)}")
