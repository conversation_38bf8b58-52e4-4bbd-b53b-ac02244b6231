from typing import Optional, List
from app.utils.crypto import sm4_decrypt
from fastapi import APIRouter, Depends, status, Query, Request
from uuid import UUID
from datetime import datetime
from app.utils.utils import (
    send_data,
    ResponseModel,
    PageQuery,
    send_page_data,
    ResponsePageModel
)
from app.api.deps import get_current_user, get_current_user_from_state
from app.core.security import get_password_hash
from app.models.user import User
from app.models.organizations import Organizations
from app.api.schemas.user import UserCreate, UserResponse, UserUpdate
from app.core.logging import get_logger
from app.models.role import Role
from app.api.schemas.role import InsetRole, PERMISSION_LEVELS
from app.api.repository.user_report_usage import check_user_usage_limit
from tortoise.expressions import Q
from app.api.repository.user_report_usage import create_user_report_usage, get_count_data
from app.api.schemas.user_report_usage import UserReportUsageCreate, UserReportUsageResponse
from app.api.repository.user import get_user_model_list
from app.api.schemas.model_config import ModelConfigBase
from app.utils.enum import UserError

logger = get_logger(__name__)
router = APIRouter()

@router.get("/model", response_model=ResponseModel[List[ModelConfigBase]], summary="获取用户的所有可用模型列表")
async def get_model(
    request: Request
):
    try:
        current_user = get_current_user_from_state(request)
        list_data = await get_user_model_list(current_user)
        return send_data(True, list_data)
    except Exception as e:
        logger.error(str(e))
        return send_data(False, None, UserError.GET_MODEL_FAIL.value)

@router.get("/me", response_model=ResponseModel[UserResponse])
async def read_users_me(
    request: Request
):
    """获取当前用户信息"""
    # current_user = request.state.current_user
    current_user = get_current_user_from_state(request)
    try:
        return send_data(True, current_user)
    except Exception as e:
        return send_data(False, None, f"获取用户信息失败: {str(e)}")


@router.get("/", response_model=ResponsePageModel[UserResponse])
async def read_users(
    request: Request,
    organization_id: str = Query(default=None, description="所属机构"),
    keyword: str = Query(default=None, description="模糊搜索的关键词"),
    page_query: PageQuery = Depends()
):
    """获取所有用户"""
    current_user = get_current_user_from_state(request)
    try:
        page = page_query.page
        size = page_query.size
        # 计算偏移量
        skip = (page - 1) * size
        # 获取总记录数
        total = 0
        base_query = None
        organization = current_user.organization.id if current_user.organization else None
        # 超级管理员可以查看所有机构的用户，其他用户只能查看自己机构的用户
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            # 超级管理员查看所有用户
            base_query = User.filter(is_deleted=False)
            if organization_id:
                base_query = base_query.filter(organization_id=organization_id)
        else:
            # 普通管理员只能查看自己机构的用户
            base_query = User.filter(is_deleted=False,organization=organization)
        if keyword:
            queries = Q()
            for field in ["realname", "username", "company"]:
                queries |= Q(**{f"{field}__icontains": keyword})
            # 搜索关联的organization的name字段
            queries |= Q(organization__name__icontains=keyword)
            base_query = base_query.filter(queries)
        base_query = base_query.prefetch_related('organization', 'role')
        total = await base_query.count()
        users = await base_query.order_by("-updated_at").offset(skip).limit(size)
        result = [
            UserResponse.model_validate(user) for user in users
        ]
        for user in result:
            user.is_trial = user.organization and user.organization.is_trial
            usage_record = await get_count_data(
                user_id=user.id,
                organization_id=user.organization.id if user.organization else None,
                is_admin=user.role.identifier == InsetRole.ADMIN
            )
            user.used_count = usage_record.used_count if usage_record else 0
            user.max_allowed_count = usage_record.max_allowed_count if usage_record else 0
        # 构建分页响应
        paginated_response = {
            "items": result,
            "total": total,
            "page": page,
            "size": size
        }
        # 构建响应，添加organization_name
            
        return send_page_data(True, paginated_response)
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return send_page_data(False, {
            "items": [],
            "total": 0,
            "page": page,
            "size": size
        }, f"获取用户列表失败: {str(e)}")


@router.post("/", response_model=ResponseModel[UserResponse], status_code=status.HTTP_201_CREATED)
async def create_user(
    user_in: UserCreate,
    request: Request
):
    """创建新用户"""
    current_user = get_current_user_from_state(request)
    try:
        # 确定目标机构：超级管理员必须指定organization_id，普通管理员只能为自己机构创建用户
        if current_user.role.identifier == InsetRole.SUPER_ADMIN:
            # 超级管理员：必须指定organization_id
            if not user_in.organization_id:
                return send_data(False, None, "请先选择用户所属机构")
            target_organization_id = user_in.organization_id
        else:
            # 普通管理员：只能为自己机构创建用户
            target_organization_id = current_user.organization.id
            
        # 检查用户名是否存在
        user = await User.filter(username=user_in.username).first()
        if user:
            return send_data(False, None, "用户名已存在")
        # 解密密码
        try:
            decrypted_password = sm4_decrypt(user_in.password)
            if not decrypted_password:
                return send_data(False, None, "密码解密失败")
        except Exception as e:
            return send_data(False, None, f"密码解密失败: {str(e)}")
            
        # 检查角色是否存在于目标机构中
        role = await Role.get_or_none(id=user_in.role_id, organization=target_organization_id)    
        if not role:
            return send_data(False, None, "角色不存在")
        # 低等级用户不能创建同级或者更高级别的用户
        creator_level = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
        created_level = PERMISSION_LEVELS.get(role.identifier) or 0
        if (creator_level <= created_level):
            return send_data(False, None, "角色权限不够")
        organization = await Organizations.filter(id=target_organization_id).first()
        if not organization:
            return send_data(False, None, "机构不存在")
        user_obj = await User.create(
            username=user_in.username,
            hashed_password=get_password_hash(decrypted_password),
            company=user_in.company,
            achievement=user_in.achievement,
            position=user_in.position,
            realname=user_in.realname,
            role=role,
            organization=organization
        )
        # 为新用户创建报告使用次数记录
        if role.identifier in [InsetRole.ADMIN, InsetRole.SUPER_ADMIN]:
            # 管理员角色和超级管理员角色：max_allowed_count设置为None
            max_allowed_count = None
        else:
            # 普通用户：初始默认为0，后续通过专门接口配置
            max_allowed_count = user_in.max_allowed_count or 0
        try:
            await create_user_report_usage(UserReportUsageCreate(
                user_id=user_obj.id,
                max_allowed_count=max_allowed_count
            ))
        except Exception as e:
            logger.error(f"创建使用次数失败：{str(e)}")
            await user_obj.delete()
            return send_data(False, None, str(e))
        logger.info(f"为用户 {user_obj.username} 创建了报告使用次数记录，max_allowed_count: {max_allowed_count}（角色: {role.identifier}）")
        res = UserResponse.model_validate(user_obj)
        res.max_allowed_count = max_allowed_count
        res.used_count = 0
        return send_data(True, res)
    except Exception as e:
        return send_data(False, None, f"创建用户失败: {str(e)}")


@router.put("/{user_id}", response_model=ResponseModel[UserResponse])
async def update_user(
    user_id: UUID, 
    user_in: UserUpdate,
    request: Request
):
    """修改用户信息"""
    current_user = get_current_user_from_state(request)
    try:
        # 获取目标用户信息
        user = await User.filter(id=user_id, is_deleted=False).prefetch_related('organization', 'role').first()
        if not user:
            return send_data(False, None, "用户不存在")
        if user_in.username:
            user_result = await User.filter(username=user_in.username).first()
            if user_result.id != user_id:
                return send_data(False, None, "用户名已存在")
          
        # 权限检查：超级管理员可以修改任何用户，其他管理员只能修改自己机构的用户
        if current_user.role.identifier != InsetRole.SUPER_ADMIN:
            if not current_user.organization or user.organization.id != current_user.organization.id:
                return send_data(False, None, "权限不足，只能修改本机构用户")
        use_record: Optional[UserReportUsageResponse] = None
        if user_in.max_allowed_count is not None:
            try:
                max_allowed_count = user_in.max_allowed_count
                # 为新用户创建报告使用次数记录
                if user.role.identifier in [InsetRole.ADMIN, InsetRole.SUPER_ADMIN]:
                    # 管理员角色和超级管理员角色：max_allowed_count设置为None
                    max_allowed_count = None
                else:
                    # 普通用户：初始默认为0，后续通过专门接口配置
                    max_allowed_count = max_allowed_count or 0
                use_record = await create_user_report_usage(UserReportUsageCreate(
                    user_id=user_id,
                    max_allowed_count=max_allowed_count
                ))
            except Exception as e:
                logger.error(f"使用次数编辑失败：{str(e)}")
                return send_data(False, None, str(e))

        update_data = user_in.model_dump(exclude_unset=True)
        # 获取organization_id
        organization_id = None
        if "organization_id" in update_data:
            organization_id = update_data["organization_id"]
        elif user.role.identifier != InsetRole.SUPER_ADMIN:
            organization_id = user.organization.id
        # 如果更新密码，需要先解密再加密
        if "password" in update_data:
            try:
                decrypted_password = sm4_decrypt(update_data["password"])
                if not decrypted_password:
                    return send_data(False, None, "密码解密失败")
                # 移除原始加密密码，添加新的哈希密码
                update_data.pop("password")
                update_data["hashed_password"] = get_password_hash(decrypted_password)
            except Exception as e:
                return send_data(False, None, f"密码解密失败: {str(e)}")    
        # 处理role_id字段
        if "role_id" in update_data:
            # 判断角色是否存在
            role = await Role.filter(
                id=update_data["role_id"],
                is_deleted=False,
                organization_id=organization_id
            ).first()
            if not role:
                return send_data(False, None, "角色不存在")
            # 低等级用户不能创建更高级别的用户
            creator_level = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
            created_level = PERMISSION_LEVELS.get(role.identifier) or 0
            if (creator_level < created_level):
                return send_data(False, None, "角色权限不够")
        
        # 添加更新时间
        update_data["updated_at"] = datetime.now()
        
        # 更新用户信息
        await user.update_from_dict(update_data)
        await user.save()
        res = UserResponse.model_validate(user, from_attributes=True)
        res.max_allowed_count = user_in.max_allowed_count
        res.used_count = use_record.used_count if use_record else 0
        return send_data(True, res)
    except Exception as e:
        return send_data(False, None, f"更新用户信息失败: {str(e)}")


@router.delete("/{user_id}", response_model=ResponseModel[UserResponse])
async def delete_user(
    user_id: UUID,
    request: Request
):
    """删除用户（逻辑删除）"""
    current_user = get_current_user_from_state(request)
    try:
        user_result = await User.filter(
            id=user_id,
            is_deleted=False
        ).prefetch_related("role", "organization").first()
        if not user_result:
            return send_data(False, None, "用户不存在")
        
        user = UserResponse.model_validate(user_result, from_attributes=True)
            
        # 低等级用户不能创建同级或者更高级别的用户
        creator_level = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
        created_level = PERMISSION_LEVELS.get(user.role.identifier) or 0
        if (creator_level <= created_level):
            return send_data(False, None, "角色权限不够")
        
        user_result.is_deleted = True
        user_result.deleted_at = datetime.now()
        await user_result.save()
        await user_result.refresh_from_db()
        # 逻辑删除
        # await User.filter(id=user_id).update(is_deleted=True)
        
        # 获取更新后的用户信息
        # updated_user = await User.get(id=user_id)
        return send_data(True, UserResponse.model_validate(user_result))
    except Exception as e:
        return send_data(False, None, f"删除用户失败: {str(e)}")
