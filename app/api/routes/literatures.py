from app.api.repository.literatures import get_literature_by_project,get_literature_by_id
from app.api.schemas.literatures import LiteratureResponse
from fastapi import APIRouter
from typing import List
from app.utils.utils import send_data, ResponseModel

router = APIRouter()

@router.get("/project/{config_id}", response_model=ResponseModel[List[LiteratureResponse]])
async def get_literature_by_project_route(config_id: str):
    """根据项目配置ID获取文献"""
    try:
        literature = await get_literature_by_project(config_id)
        return send_data(True, literature)
    except Exception as e:
        return send_data(False, None, str(e))
@router.get("/research/{research_id}", response_model=ResponseModel[List[LiteratureResponse]])
async def get_literature_by_research_route(research_id: str):
    """根据研究ID获取文献"""
    try:
        literature = await get_literature_by_id(research_id)
        return send_data(True, literature)
    except Exception as e:
        return send_data(False, None, str(e))
