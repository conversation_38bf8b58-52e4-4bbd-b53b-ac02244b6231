from typing import List
from fastapi import APIRouter, status, Request
from uuid import uuid4
from pydantic import BaseModel
from app.models.project_member_joins import ProjectMemberJoin
from app.models.project_members import ProjectMember
from app.api.schemas.project_member_joins import ProjectMemberJoinCreate, ProjectMemberJoinResponse, ProjectMemberJoinUpdate
from app.utils.utils import send_data, ResponseModel
from app.models.user import User
from app.api.deps import get_current_user_from_state


# 定义一个简单的响应模型
class JoinIdResponse(BaseModel):
  join_id: str;


router = APIRouter()


# 管理员获取全部项目成员关联
@router.get("/list", response_model=ResponseModel[List[ProjectMemberJoinResponse]], status_code=status.HTTP_200_OK)
async def read_project_member_join():
    """获取特定项目成员关联详情（管理员接口）"""
    query = ProjectMemberJoin.all()
    joins = await query.prefetch_related("member").all()
    
    try:
        # 将ORM对象转换为响应格式，使用from_attributes=True支持嵌套关系
        result = [ProjectMemberJoinResponse.model_validate(join, from_attributes=True) for join in joins]
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"数据转换出错: {str(e)}")

# 管理员获取指定项目成员关联
@router.get("/{join_id}", response_model=ResponseModel[List[ProjectMemberJoinResponse]], status_code=status.HTTP_200_OK)
async def read_project_member_join(
    join_id: str
):
    """获取特定项目成员关联详情（管理员接口）"""
    joins = await ProjectMemberJoin.filter(join_id=join_id).prefetch_related("member").all()
    if not joins or len(joins) == 0:
        return send_data(False, None, "项目成员关联不存在")
    
    try:
        # 将ORM对象转换为响应格式，使用from_attributes=True支持嵌套关系
        result = [ProjectMemberJoinResponse.model_validate(join, from_attributes=True) for join in joins]
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"数据转换出错: {str(e)}")


# 管理员创建项目成员关联
@router.post("", response_model=ResponseModel[JoinIdResponse], status_code=status.HTTP_201_CREATED)
async def create_project_member_join(
    join_in: ProjectMemberJoinCreate,
    request: Request
):
    """创建新的项目成员关联（管理员接口）"""
    current_user = get_current_user_from_state(request)
    try:
        if not join_in or not join_in.member:
            return send_data(False, None, "缺少必要的关联信息")
        
        # 生成或使用提供的join_id
        current_join_id = join_in.join_id
        
        if current_join_id:
          # 检查join_id是否已存在
          existing_joins = await ProjectMemberJoin.filter(join_id=current_join_id).all()
          if existing_joins and len(existing_joins) > 0:
            for existing_join in existing_joins:
              await existing_join.delete()
        else:
          current_join_id = f"join_{uuid4()}"
        # print(current_join_id)
        # 开始创建新的关联
        for member_id in join_in.member:
            # 验证成员是否存在
            member = await ProjectMember.filter(id=member_id).first()
            if not member:
                return send_data(False, None, f"指定的项目成员 {member_id} 不存在")
            # 创建项目成员关联
            join = ProjectMemberJoin(
                join_id=current_join_id,
                member=member,
                user_id=current_user.id
            )
            await join.save()
        return send_data(True, JoinIdResponse(join_id=current_join_id))
    except Exception as e:
        return send_data(False, None, f"创建项目成员关联失败: {str(e)}")
