from typing import (
    Generic,
    TypeVar,
    Optional,
    AsyncGenerator,
    List
)
import tempfile
import pypandoc
from docx import Document
import docx
from docx.shared import RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
# 给请求增加唯一的requestID
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from fastapi.responses import FileResponse
from starlette.background import BackgroundTask
from uuid import uuid4
from pydantic.generics import GenericModel
import os
import json
# import asyncio
from pydantic import BaseModel
from fastapi import Query
from app.api.schemas.literatures import LiteratureResponse
from app.services.search_service import (
    fetch_webpage_text_async,
    perform_serpapi_search
)
from enum import Enum
from app.utils.enum import ErrorType
from app.services.llm_service import call_llm
from app.core.config import settings
from app.services.prompts import (
    REFERENCE_VALID_SYSTEM_PROMPT,
    REFERENCE_VALID_USER_PROMPT,
    REFERENCE_RELATED_SYSTEM_PROMPT,
    REFERENCE_RELATED_USER_PROMPT
)
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from uuid import uuid4
import re
from app.core.logging import get_logger
from app.api.schemas.user import UserResponse
from app.core.config import settings

logger = get_logger(__name__)

# 正文所有标题的纯文字最大匹配长度
PURE_TITLE_MAX_LENGTH=200

class SingleLiterature(BaseModel):
    reference: str
    url: str
class LiteratureTextAndOtherTitle(BaseModel):
    literature_text: str
    other_text: str
class ContentStatus(str, Enum):
    """内容状态"""
    ERROR = "error"  # 错误状态
    NORMAL = "normal"  # 正常文本
    HEART_BEAT = "heart_beat"
class CiteItem(BaseModel):
    origin: str
    nums: List[int]
    text: str
    remark: Optional[List[str]] = None
class SplitReference(BaseModel):
    text_before_citation_title: str
    citation_paragraph_without_title: str
    citation_title: str
    paragraph_after_citation: str
class Reference(BaseModel):
  index: int
  authors: str
  title: str
  journal: str|None
  year: str
  volume: str|None
  issue: str|None
  pages: str|None
  full_text: str
  url: str
  remark: Optional[List[str]] = None

T = TypeVar("T")
# 正文中引用的正则匹配
CITATION_NUM_RE = r'(.+?)(?<!>)(\[(\d+(?:-\d+)?(?:,\s*\d+(?:-\d+)?)*)\])'
# 一条参考文献的正则匹配
REFERENCE_RE = r'(?=\[\d+\]).+?\[(M|J|D|C|N|R|S|P|EB/OL|OL)\].+?[\d{3,4}].*?'

class FAULT(str, Enum):
    FAKE_REFERENCE = "参考文献可能不存在"
    NOT_REFERENCED = "未在正文引用"
    OPINION_UNFIT="观点表述可能有误"
    TYPE_ERROR="参考文献引用格式不标准"
    NOT_IN_REFERENCE="参考文献缺失"
def fault_normal(index: int, data: FAULT):
    return f"&#91;{index}&#93;{data.value}"
class ResponseModel(GenericModel, Generic[T]):
  success: bool
  code: int
  data: Optional[T] = None
  error: Optional[str] = ""# from fastapi.responses import JSONResponse

class PageInfo(GenericModel, Generic[T]):
    """分页信息"""
    total: int
    page: int
    size: int
    items: List[T] = []

class ResponsePageModel(GenericModel, Generic[T]):
    """分页响应模型"""
    success: bool
    code: int
    data: PageInfo[T]
    error: Optional[str] = ""
class PageQuery(BaseModel):
  page: Optional[int] = Query(1, ge=1, description="页码")
  size: Optional[int] = Query(10, ge=1, description="每页大小")

## 参考文献的标题识别函数
def citation_head_match(text: str, no_prefix: bool = False):
    # 匹配格式：### 1.参考文献 或 ## 1、参考文献
    pattern = re.compile(
        # r'(\s*(?:\d+|[一二三四五六七八九十])?.*?[、\.]\**?参考文献\**?)' if no_prefix else r'(#*\s*(?:\d+\.)?.*?[、\.]\**?参考文献\**?)',
        r'(\s{0,4}(?:\d+|[一二三四五六七八九十])?\s{0,2}[、\.]\s{0,2}\*{0,2}参考文献\*{0,2})' if no_prefix else r'(#*\s{0,4}(?:\d+|[一二三四五六七八九十])?\s{0,2}[、\.]\s{0,2}\*{0,2}参考文献\*{0,2})',
        re.IGNORECASE
    )
    match = pattern.search(text)
    if not match:
        # 匹配格式：## 参考文献
        pattern1 = re.compile(
            r'(\s{0,2}\*{0,2}参考文献\*{0,2})' if no_prefix else r'(#+\s{0,2}\*{0,2}参考文献\*{0,2})',
            re.IGNORECASE
        )
        match = pattern1.search(text)
    if match:
        logger.info(f"参考文献的标题为：{match.group(1)}")
    else:
        logger.info(f"没有参考文献的标题")
    return match
## markdown格式的标题识别函数
def title_head_match(text: str):
    # 匹配格式：### 1.标题 或 ## 1、标题
    pattern = re.compile(
        rf'(#{{1,5}}\s{{0,2}}(?:\d+)?+\s{{0,2}}[、\.]\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**\s*[^\n\r]{1,3})',
        re.IGNORECASE
    )
    match = pattern.search(text)

    if not match:
        # 匹配格式：## 标题
        pattern1 = re.compile(
            rf'(#{{1,5}}\s{{0,2}}\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**[^\n\r]{{1,3}})',
            re.IGNORECASE
        )
        match = pattern1.search(text)
    return match
def send_data(is_success: bool, data: Optional[T], error: str = "") -> ResponseModel[T]: 
    result = ResponseModel(
        success=is_success,
        code=0 if is_success else 500,
        data=data if is_success else None,
        error=(data or error) if not is_success else ""
    )
    logger.info(f"响应数据：{str(dict(result))[:200]}")
    return result

async def reference_related(
    content: str,
    expression: str,
    api_key: str,
    api_url: str,
    model: str
) -> bool:
    messages = [
        {
            "role": "system",
            "content": REFERENCE_RELATED_SYSTEM_PROMPT
        },
        {
            "role": "user",
            "content": REFERENCE_RELATED_USER_PROMPT.format(
                content=content,
                expression=expression
            )
        }
    ]
    response = await call_llm(
        messages=messages,
        flag="网页的提取内容和引用文本进行比对",
        apiKey=api_key,
        apiUrl=api_url,
        model=model
    )
    return response == 'Yes'

async def reference_valid(
    data: Reference,
    api_key: str,
    api_url: str,
    model: str,
    limit: int = settings.MAX_HALLUCINATION_DETECTION_SEARCH
) -> str:
    response = "No"
    urls = await perform_serpapi_search(
        query=data.full_text,
        limit=limit
    )
    for url in urls:
        page_content = await fetch_webpage_text_async(url)
        # 调用LLM
        messages = [
            {
                "role": "system",
                "content": REFERENCE_VALID_SYSTEM_PROMPT
            },
            {
                "role": "user",
                "content": REFERENCE_VALID_USER_PROMPT.format(
                    text=page_content,
                    literature=data.full_text
                )
            }
        ]
        response = await call_llm(
            messages=messages,
            flag="提取网页内容和参考文献进行比对",
            apiKey=api_key,
            apiUrl=api_url,
            model=model
        )
        if response != 'No':
            break
    return response
def send_page_data(
    is_success: bool,
    data: Optional[PageInfo[T]] = None,
    error: str = ""
) -> ResponsePageModel[T]:
    """
    构建分页响应数据
        
    Returns:
        ResponsePageModel: 分页响应数据
    """
    result = ResponsePageModel(
        success=is_success,
        code=0 if is_success else 500,
        data=data,
        error=error if not is_success else ""
    )
    logger.info(f"响应数据：{str(dict(result))[:200]}")
    return result

def save_text_to_file(
    content: str,
    file_path: str
) -> str:
  """
  将字符串内容保存或追加到指定文件中
  
  Args:
      content: 要保存的文本内容
      file_path: 文件的相对路径
      
  Returns:
      保存的文件相对路径
  """
  # 获取绝对路径
  abs_path = os.path.join(os.getcwd(), file_path)
  
  # 确保目录存在
  directory = os.path.dirname(abs_path)
  if directory and not os.path.exists(directory):
    os.makedirs(directory)
    
  # 追加内容到文件（如果文件不存在则创建）
  with open(abs_path, "w", encoding="utf-8") as f:
    f.write(content)
    
  # 返回相对路径
  return file_path
# 是不是 【### 文字标题】这种形式
def is_markdown_title(text: str) -> bool:
    pattern = r'^#+\s[^\n]+$'
    return re.match(pattern, text.strip()) is not None
# 将正文的引用角标改成小的 text形如[1]
def convert_citations_to_sup(text):
    # 让参考文献里面的带html标签开头的小标不要改变
    # 匹配形如 [1]、[23] 的引用，并替换为 <sup>[1]</sup> 形式
    return re.sub(CITATION_NUM_RE, r'\1<sup>\2</sup>', text) 
  
# 辅助函数：流式读取文件内容（SSE格式）
async def stream_file_content_sse(
    file_path: str,
    current_user: UserResponse,
    is_cut: Optional[bool] = False
) -> AsyncGenerator[str, None]:
    """
    流式读取文件内容并以SSE格式返回
    
    Args:
        file_path: 文件的相对路径或绝对路径
        
    Yields:
        SSE格式的文件内容
    """
    # 处理文件路径
    if os.path.isabs(file_path):
        abs_file_path = file_path
    else:
        abs_file_path = os.path.join(os.getcwd(), file_path)
    
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        yield f"data: {json.dumps({'error': f'{ErrorType.FILE_NOT_EXIST.value}: {file_path}'})}\n\n"
        return
    try:
        with open(abs_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            content = ""
            for line in lines:
                if len(content) >= settings.TRIAL_USER_MAX_TEXT and current_user.is_trial and is_cut:
                    break
                else:
                    content += line
            # content = ''.join(lines)
            content = content.strip()
            chunk = stream_handle_before_send(content)
            if chunk:
                yield f"data: {json.dumps({'content': chunk, 'status': ContentStatus.NORMAL})}\n\n"

        # 发送完成事件
        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
    
    except Exception as e:
        yield f"data: {json.dumps({'error': f'读取文件时出错: {str(e)}'})}\n\n"
# 将参考文献的形如 ****.http://和****.DOI:的格式改成markdown的链接形式
def format_to_markdown_link(text: str) -> str:
    try:
        result = split_by_citation_section(text)
        if not result:
            return text
        literature_section = result.citation_paragraph_without_title
        # 现将参考文献一条一条拆出来
        segments = re.split(r'\n\s*(?=\[\d+\]|\d+\.)', literature_section.strip())
        format_list: List[str] = []
        for i, item in enumerate(segments):
            pure_text = item.strip()
            doi_match = re.search(r'^(.*?)DOI:\s*([^\s]+)', pure_text)
            url_match = re.search(r'(^\[\d+\]\s.+?)url:\s{0,2}(http[s]?://[^\s]+)$', pure_text)
            if doi_match:
                label = doi_match.group(1).strip()
                doi_part = doi_match.group(2).strip()
                url=f"https://doi.org/{doi_part}"
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', label)
                format_list.append(f"[{i + 1}] [{full_text}]({url})")
            elif url_match:
                label = url_match.group(1).strip()
                url = url_match.group(2).strip()
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', label)
                format_list.append(f"[{i + 1}] [{full_text}]({url})")
            else:
                full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', pure_text)
                format_list.append(f"[{i + 1}] {full_text}")
        # 这里使用两个换行符的目的是为了前端的md编辑器可以把每一条文献识别为一个段落。
        reference = ("\n\n").join(format_list)
        res = [
            result.text_before_citation_title,
            result.citation_title,
            reference
        ]
        if result.paragraph_after_citation:
            res.append(result.paragraph_after_citation)
        # 这里使用两个换行符的目的是为了每一部份都被识别为一个段落。
        return ("\n\n").join(res)  
    except Exception as e:
        logger.error(f"整理参考文献成markdown链接格式时发生错误：{str(e)}")
        return text
            
# 将markdown的链接形式的文档进行提取和以DOI:结尾的文献进行提取
def normal_single_literature(text: str) -> SingleLiterature:
    pattern = r'(\[\d+\])\s*\[(.*?)\]\((.*?)(?:\s+".*?")?\)'
    match = re.search(pattern, text)
    doi_match = re.search(r'^(.*?)DOI:\s*([^\s]+)', text)
    label = text
    url = ""
    if match:
        num = match.group(1).strip()
        reference = match.group(2).strip()
        label = f"{num} {reference}"
        url = match.group(3).strip()
    elif doi_match:
        label = doi_match.group(1).strip()
        doi_part = doi_match.group(2).strip()
        url=f"https://doi.org/{doi_part}"
    return SingleLiterature(
        reference=label,
        url=url
    )
def extract_reference_item_list(text: str):
    segments = re.split(r'\n\s*(?=\[\d+\]|\d+\.)', text.strip())
    result_list: List[Reference] = []
    for i, item in enumerate(segments):
        temp = normal_single_literature(item)
        seg = temp.reference
        pattern = re.compile(r"""
            ^\s*(?:\[(?P<index_bracket>\d+)\]|\s*(?P<index>\d+)\.)\s+   # [1] 或 1.
            (?P<authors>.+?)\.\s+                                 # 作者部分（第一个完整句号）
            (?P<title>.+?)\[(M|J|D|C|N|R|S|P|EB|OL|EB/OL)\]\.\s+                                        # 标题部分（第一个完整句号）
            (?:(?P<journal>.+?)[\.,]\s+)?                                     # 期刊名（第一个完整句号）
            (?P<year>\d{4})[,]{0,1}\s*                                            # 年份
            [;]{0,1}\s*(?:(?P<volume>\d+))?  #可选卷号                                    # 卷号
            (?:\((?P<issue>S{0,1}.{0,5}\s*\d+)\))?   # 可选期号
            :{0,1}\s*(?:(?P<pages>[A-Za-z\d\-\.e]+))?  #可选页码                     # 页码
            \.\s*$                                                  # 结尾句号
        """, re.VERBOSE)
        # seg不能用full_text替换，因为full_text没有前面的序号了
        match = pattern.match(seg)
        full_text = re.sub(r'^\s*(?:\[\d+\]|\d+\.)\s*', '', seg)
        # 去掉前后的序号、和前后的换行符
        if match:
            groups = match.groupdict()
            index = groups["index"] or groups["index_bracket"]
            result = Reference(**{
                "index": int(index),
                "authors": groups["authors"],
                "title": groups["title"],
                "journal": groups["journal"],
                "year": groups["year"],
                "volume": groups["volume"] if groups["volume"] else None,
                "issue": groups["issue"] if groups["issue"] else None,
                "pages": groups["pages"],
                "full_text": full_text,
                "url": temp.url
            })
            result_list.append(result)
        else:
            result = Reference(**{
                "index": i + 1,
                "authors": "",
                "title": "",
                "journal": "",
                "year": "",
                "volume": None,
                "issue": None,
                "pages": "",
                "full_text": full_text,
                "url": temp.url,
                "remark": [FAULT.TYPE_ERROR.value]
            })
            result_list.append(result)
    print(result_list)
    return result_list
# 去除正文的标题标题
def remove_markdown_h1_and_text(all_text: str, title: str) -> str:
    # 匹配第一个 Markdown 一级标题（形如 "# 标题内容"）
    # 只要开头指定长度的文本进行匹配
    split_index = len(title)*2+20
    text = all_text[0:split_index]
    # 正则匹配 Markdown 一级标题：只匹配以一个 # 开头的行（不能是 ##）
    h1_pattern = r'(?m)^#\s+.+\n?'
    result = re.sub(h1_pattern, '', text)

    # 删除指定标题文本的第一次出现（转义特殊字符）
    if title:
        escaped_title = re.escape(title)
        result = re.sub(escaped_title, '', result, count=1)

    return f"# {title}\n\n" + result + all_text[split_index:]
def read_file_content(file_path: str) -> str:
    """
    读取文件内容并返回
    
    Args:
        file_path: 文件的相对路径或者绝对路径
        
    Returns:
        str: 文件的内容
        
    Raises:
        FileNotFoundError: 当文件不存在时
        IOError: 当读取文件出错时
    """
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    # 检查文件是否存在
    if not os.path.exists(abs_path):
      error_msg = f"{ErrorType.FILE_NOT_EXIST.value}: {file_path}"
      logger.error(error_msg)
      raise Exception(error_msg)
    
    try:
        # 读取文件内容
        with open(abs_path, "r", encoding="utf-8") as f:
            content = f.read()
        result = content.strip()
        return result
    except IOError as e:
        raise Exception(f"读取文件时出错: {str(e)}")
# 将markdown文档按照标题加正文或者标题的的组合切分
def split_markdown_text_by_paragraph(md_text: str) -> List[str]:
    pattern = r'^(#+\s+.*)$'
    lines = md_text.splitlines()
    sections = []
    current_section = []

    for line in lines:
        if re.match(pattern, line):
            # 遇到新标题时，保存当前段落
            if current_section:
                sections.append('\n\n'.join(current_section).strip())
            current_section = [line]  # 新段落从标题开始
        else:
            current_section.append(line)

    # 保存最后一个段落
    if current_section:
        sections.append('\n\n'.join(current_section).strip())

    return sections

def extract_section_after_citation(literature_text: str):
    try:
        match = title_head_match(literature_text)
        if match:
            split_text = match.group(1).strip()
            start, end = match.span()

            pure_literature = literature_text[:start].strip()
            other_text = f"{split_text}\n{literature_text[end:].strip()}"
            return LiteratureTextAndOtherTitle(
                literature_text=pure_literature,
                other_text=other_text
            )
        else:
            return LiteratureTextAndOtherTitle(
                literature_text=literature_text,
                other_text=''
            )
    except Exception as e:
        msg = f"从参考文献后续文本提取段落失败：str{e}"
        logger.error(msg)
        raise ValueError(msg)

def split_by_citation_section(text: str) -> SplitReference|None:
    """
    根据参考文献的标题将文本分割，并保留分隔符。
    返回一个对象。
    """
    logger.info("准备执行split_by_citation_section函数")
    match = citation_head_match(text)
    logger.info("citation_head_match函数执行完毕了")
    if not match:
        return None

    split_text = match.group(1).strip()
    start, end = match.span()
    right = text[end:].strip()
    logger.info("准备执行extract_section_after_citation函数")
    extract_data = extract_section_after_citation(right)
    logger.info("extract_section_after_citation函数执行完毕")
    logger.info(f"参考文献的标题是: {split_text}")
    return SplitReference(**{
        # 参考文献标题前面的文本
        'text_before_citation_title': text[:start].strip(),
        # 参考文献内容段落
        'citation_paragraph_without_title': extract_data.literature_text,
        # 参考文献那几个标题文字
        'citation_title': split_text,
        # 参考文献段落后面的段落
        'paragraph_after_citation': extract_data.other_text
    })
def split_text_by_sentences(text: str, regexp=r'(。|\*\*|\n|\|\?|\!|？|！)'):
    # 用正则拆分，保留标点
    parts = re.split(regexp, text)
    # 拼接句子 + 标点
    sentences = []
    for i in range(0, len(parts) - 1, 2):
        sentence = parts[i] + parts[i + 1]
        if sentence.strip():  # 跳过空白行
            sentences.append(sentence.strip())
    
    return sentences
def extract_citations_in_paper_by_sentence(text: str) -> List[CiteItem]:
    sentences = split_text_by_sentences(text)
    full_sentences: List[CiteItem] = []
    for sentence in sentences:
        cite_pattern = re.compile(r'(?P<origin>.+?)(?P<cite>\[(\d+(?:-\d+)?(?:,\s*\d+(?:-\d+)?)*)\])')
        match = cite_pattern.match(sentence)
        if match:
            dict_data = match.groupdict()
            num: List[int] = []
            cite = re.sub(r'^\[(.*)\]$', r'\1', dict_data["cite"])
            if ',' in cite:
                num = [int(item) for item in re.split(r',\s*', cite)]
            elif '-' in cite:
                start, end = map(int, cite.split('-'))
                num.extend(range(start, end + 1))
            else:
                num.append(int(cite))
            text = dict_data["origin"]
            sentence = CiteItem(**{
                "origin": sentence,
                "nums": num,
                "text": text
            })
            full_sentences.append(sentence)
    return full_sentences
# 如果markdown的两个正文段落用\n连接则改为\n\n
# def change_markdown_paragraph_join_way(text: str):
#     return re.sub(
#         r'(?<![#>\-\*\d\.|`])([^\n\S]*\S[^\n]*?)\n(?=[^\n\S]*\S[^\n]*?(?![#>\-\*\d\.|`]))',
#         r'\1\n\n',
#         text
#     )
def change_markdown_paragraph_join_way(text: str):
    lines = text.splitlines()
    result = []
    skip_join = {'#', '-', '*', '>', '|', '`'}

    for i in range(len(lines)):
        line = lines[i]
        result.append(line)
        # 尝试插入额外空行的条件：
        if (
            i < len(lines) - 1
            and lines[i].strip()
            and lines[i + 1].strip()
            and lines[i].lstrip()[0] not in skip_join
            and lines[i + 1].lstrip()[0] not in skip_join
        ):
            result.append('')  # 插入空行

    return '\n'.join(result)

def insert_before_last_char(s: str, insert_text: str) -> str:
    if len(s) < 1:
        return ''
    return s[:-1] + insert_text + s[-1]

class RequestIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # 生成 request_id，可根据需要从 header 获取或用 uuid 生成
        request_id = request.headers.get("X-Request-ID", str(uuid4()))
        
        # 设置 request_id 到 request.state
        request.state.request_id = request_id
        
        try:
            # 只为调试目的读取body和query参数
            if request.method in ["POST", "PUT", "PATCH"]:
                # 读取body用于日志记录
                body = await request.body()
                if body:
                    logger.info({"request_id": request_id, "data": body})
                
                # 重新构造receive函数，确保body可以被再次读取
                async def receive():
                    return {"type": "http.request", "body": body}
                
                # 更新request的receive方法
                request._receive = receive
            elif request.query_params:
                logger.info({"request_id": request_id, "data": dict(request.query_params)})

        except Exception as e:
            logger.error("request-id中间层报错:", e)

        # 调用后续逻辑
        response = await call_next(request)
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        return response
def handle_before_save(text: str):
    pipeline = [bold_to_h2_reference, format_to_markdown_link, change_markdown_paragraph_join_way]
    for fn in pipeline:
        text = fn(text)
    return text
def bold_to_h2_reference(md_text: str) -> str:
    """
    将 Markdown 中的加粗 '参考文献' 转换为二级标题 '## 参考文献'
    """
    # 匹配 **参考文献** 或 __参考文献__ 两种加粗写法
    logger.info("bold_to_h2_reference")
    pattern = r"^[ \t]*(\*\*|__)\s*参考文献\s*(\*\*|__)[ \t]*$"
    return re.sub(pattern, "## 参考文献 \n\n", md_text, flags=re.MULTILINE)
def stream_handle_before_send(text: str):
    pipeline = [convert_citations_to_sup]
    for fn in pipeline:
        text = fn(text)
    citation_head_match(text)
    return text
# 将文本转换成可以作为文件名称的文本
def sanitize_filename(filename, replace_with="_"):
    """
    替换文件名中的非法字符
    :param filename: 原始文件名（不含路径）
    :param replace_with: 替换非法字符的字符（默认 "_"）
    :return: 安全的文件名
    """
    # 定义非法字符的正则表达式（包括空格、控制字符、Windows/Unix非法字符）
    illegal_chars = r'[<>:"/\\|?*\x00-\x1f]'  # \x00-\x1f 是控制字符
    # 替换非法字符
    safe_name = re.sub(illegal_chars, replace_with, filename)
    # 去除首尾空格和点（避免 ".filename" 或 "filename."）
    safe_name = safe_name.strip().strip('.')
    # 合并连续替换字符（如多个 "_" 变成单个 "_"）
    safe_name = re.sub(r'_{2,}', '_', safe_name)
    # Windows 保留名称检查（可选）
    windows_reserved = [
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
        'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
        'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]
    if safe_name.upper() in windows_reserved:
        safe_name = f"_{safe_name}"
    return safe_name
# 获取段落的序号属性
def get_num_props(paragraph:any):
    """
    获取段落的编号属性 (numId, ilvl)，如果存在
    """
    pPr = paragraph._p.pPr
    if pPr is not None and pPr.numPr is not None:
        numId = pPr.numPr.numId.val
        ilvl = pPr.numPr.ilvl.val
        return numId, ilvl
    return None, None
# 给段落设置编号属性
def set_num_props(paragraph, numId, ilvl):
    """
    给段落设置编号属性 (numId, ilvl)
    """
    p = paragraph._p

    # 确保 <w:pPr> 存在
    if p.pPr is None:
        pPr = docx.oxml.OxmlElement('w:pPr')
        p.insert(0, pPr)
    else:
        pPr = p.pPr

    # 构建 <w:numPr>
    numPr = docx.oxml.OxmlElement('w:numPr')

    ilvl_el = docx.oxml.OxmlElement('w:ilvl')
    ilvl_el.set(docx.oxml.ns.qn('w:val'), str(ilvl))
    numId_el = docx.oxml.OxmlElement('w:numId')
    numId_el.set(docx.oxml.ns.qn('w:val'), str(numId))

    numPr.append(ilvl_el)
    numPr.append(numId_el)

    pPr.append(numPr)

def set_hyperlink(
    paragraph: any,
    url: str,
    text: str,
    is_error: Optional[bool] = False
):
    try:
        part = paragraph.part
        r_id = part.relate_to(url, reltype="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", is_external=True)

        # 创建 <w:hyperlink> 元素
        hyperlink = docx.oxml.OxmlElement('w:hyperlink')
        hyperlink.set(docx.oxml.ns.qn('r:id'), r_id)

        # 创建 run
        new_run = docx.oxml.OxmlElement('w:r')
        rPr = docx.oxml.OxmlElement('w:rPr')

        # 设置颜色
        color = docx.oxml.OxmlElement('w:color')
        color.set(docx.oxml.ns.qn('w:val'), 'FF0000' if is_error else '0000FF')  # 蓝色
        rPr.append(color)

        underline = docx.oxml.OxmlElement('w:u')
        underline.set(docx.oxml.ns.qn('w:val'), 'single')
        rPr.append(underline)

        font = docx.oxml.OxmlElement('w:rFonts')
        font.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        font.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        font.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        rPr.append(font)

        sz = docx.oxml.OxmlElement('w:sz')
        sz.set(docx.oxml.ns.qn('w:val'), '24')  # 12pt
        rPr.append(sz)

        new_run.append(rPr)

        # 添加文本
        text_elm = docx.oxml.OxmlElement('w:t')
        text_elm.text = text
        new_run.append(text_elm)

        # 将 run 添加到 hyperlink
        hyperlink.append(new_run)
    except Exception as e:
        logger.error(f"set_hyperlink报错: {str(e)}")
    paragraph._element.append(hyperlink)
def format_docx_file(doc):
    """
    统一处理Word文档格式化
    
    Args:
        doc: python-docx的Document对象
        
    Returns:
        doc: 格式化后的Document对象
    """
    is_in_citation_paragraph = False
    # 处理所有段落
    for i, paragraph in enumerate(doc.paragraphs):
        para_text: str = paragraph.text.strip()
        # 判断是否为标题段落
        is_heading = paragraph.style.name.startswith('Heading')
        
        # 如果是标题且是否为参考文献段落的标识为True
        if is_heading and is_in_citation_paragraph:
            is_in_citation_paragraph = False
        # 判断是否是参考文献标题
        if citation_head_match(para_text, True) and is_heading:
            is_in_citation_paragraph = True

        # 标题进行处理
        if is_heading:
            # 标题段落保持原样式，但更改字体为宋体
            paragraph.style.font.italic = False
            for run in paragraph.runs:
                # 安全地设置字体
                try:
                    # 直接设置字体名称（中英文）
                    run.font.name = '宋体'
                    # 尝试使用更安全的方式设置东亚字体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        if hasattr(run._element.rPr, 'rFonts'):
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                            run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                    run.font.bold = True  # 确保标题是粗体
                    # 设置字体颜色为黑色
                    run.font.color.rgb = RGBColor(0, 0, 0)
                    
                    run.font.italic = False  # 移除斜体属性
                    # 确保XML级别也移除斜体
                    if hasattr(run._element, 'rPr') and run._element.rPr is not None:
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:i')):
                            run._element.rPr.remove(i)
                        for i in run._element.rPr.findall(docx.oxml.ns.qn('w:iCs')):
                            run._element.rPr.remove(i)
                except Exception as e:
                    logger.warning(f"设置标题样式出错: {str(e)}")
        # 非标题进行处理
        else:
            paragraph.style = doc.styles['Normal']
            
            # 检查每个run，保留原始的加粗样式
            for run in paragraph.runs:
                # 保存原始加粗状态
                original_bold = run.bold
                
                # 直接设置字体名称（中英文）
                run.font.name = '宋体'
                # 同时设置东亚语言字体（确保中文显示为宋体）
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                # 设置西文字体为Times New Roman
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                
                # 恢复原始加粗状态，而不是强制设为False
                run.font.bold = original_bold
        is_citation_item = re.search(REFERENCE_RE, para_text)
        # is_citation_item = len(re.findall(REFERENCE_RE, para_text)) == 1  
        # 设置段落格式：段落间距、行距等
        try:
            if is_heading:
                # 标题段落设置
                paragraph.paragraph_format.space_before = docx.shared.Pt(12)  # 标题前增加间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 标题后统一间距
                
                # 一级标题(Heading 1)居中显示
                if paragraph.style.name == 'Heading 1':
                    paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
            elif is_in_citation_paragraph:
                pure_text = ""
                url = ""
                for i, child in enumerate(paragraph._element):
                    if child.tag.endswith('hyperlink'):
                        # 获取关系ID r:id
                        r_id = child.get(docx.oxml.ns.qn('r:id'))
                        # 从关系映射中获取 URL
                        if r_id and r_id in doc.part._rels:
                            url = doc.part._rels[r_id].target_ref
                        else:
                            url = None
                        for node in child.iter():
                            if node.tag.endswith('t'):  # 文本节点
                                pure_text += node.text or ''
                    else:
                        for node in child.iter():
                            if node.tag.endswith('t'):  # 文本节点
                                pure_text += node.text or ''
                citation_error_pattern = r'^(\[\d+\].+\.)(\(.+?\))$'
                match = re.search(citation_error_pattern, pure_text)
                if match:
                    paragraph.clear()
                    text = match.group(1).strip()
                    error_text = match.group(2).strip()
                    if not url:
                        main_run = paragraph.add_run(text)
                        main_run.font.underline = True
                        # 直接设置字体名称（中英文）
                        main_run.font.name = '宋体'
                        # 画下划线
                        rPr = main_run._element.rPr
                        # 同时设置东亚语言字体（确保中文显示为宋体）
                        rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                        # 设置西文字体为Times New Roman
                        rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                        rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                        u = docx.oxml.OxmlElement('w:u')
                        u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
                        u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
                        rPr.append(u)
                    else:
                        set_hyperlink(paragraph, url, text, True)
                    # 幻觉审查错误的文本标记为红色
                    remark_run = paragraph.add_run(error_text)
                    remark_run.font.color.rgb = RGBColor(255, 0, 0)

                    remark_run.font.name = '宋体'
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                    # 设置西文字体为Times New Roman
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                    remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                else:
                    if url:
                        paragraph.clear()
                        set_hyperlink(paragraph, url, pure_text, False)
                # 首行悬挂缩进
                paragraph.paragraph_format.first_line_indent = docx.shared.Pt(-21)  # 悬挂缩进
                paragraph.paragraph_format.left_indent = docx.shared.Pt(21)  # 左缩进
                # 段前段后间距
                paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                # 使用数值设置行距为1.5倍
                paragraph.paragraph_format.line_spacing = 1.5
            else:
                # 正文段落设置 - 统一所有正文段落的间距
                paragraph.paragraph_format.space_before = docx.shared.Pt(0)  # 移除段前间距
                paragraph.paragraph_format.space_after = docx.shared.Pt(12)  # 统一设置段后间距为12磅
                # 使用数值设置行距为1.5倍
                paragraph.paragraph_format.line_spacing = 1.5
                p = paragraph._p  # 获取底层 XML 对象
                pPr = p.pPr
                # 这是所有不带有doc序号（类似html标签的ul/ol li这种）的段落
                if not (pPr is not None and pPr.numPr is not None):
                    paragraph.paragraph_format.first_line_indent = docx.shared.Pt(21)  # 首行缩进两个中文字符
                # 这说明是正文里面写了参考文献的格式
                if is_citation_item:
                    logger.info(f"正文里面写了参考文献的格式: {para_text}")
        except Exception as e:
            logger.warning(f"设置段落格式时出错: {str(e)}")
        
        
        # 正文里面引用参考文献的处理
        try:
            literature_pattern = r'\S+\[\d+(?:,\s*\d+)*\].*?[。！!?？\n\.]'
            search_match = re.search(literature_pattern, para_text)
            if search_match:
                allBoldTextMap = []
                for run in paragraph.runs:
                    key = run.text
                    if run.font and run.font.bold:
                        allBoldTextMap.append(key)
                paragraph.clear()
                parts = split_text_by_sentences(para_text, r'(。|：|\n|\|\?|\!|？|！)')
                for part in parts:
                    # 这是幻觉审查里面要变颜色和加下划线的正文引用部分
                    pattern_one = r'(?P<main>.+?\[\d+(?:,\s*\d+)*\])(?P<remark>\([^\)]+?\))(?P<dot>.+?)'
                    # 这是正常的正文引用部分
                    pattern_two = r'(?P<main>.+?\[\d+(?:,\s*\d+)*\])(?P<dot>.+?)'
                    one_match = re.match(pattern_one, part)
                    two_match = re.match(pattern_two, part)
                    main = ""
                    remark = ""
                    dot = ""
                    if one_match:
                        try:
                            main = one_match.group('main')
                            remark = one_match.group('remark')
                            dot = one_match.group('dot')
                            main_match = re.match(CITATION_NUM_RE, main)
                            if main_match:
                                main_content = main_match.group(1).strip()
                                main_run = paragraph.add_run(main_content)
                                main_run.font.underline = True
                                main_run.font.name = '宋体'
                                main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                                # 画下划线
                                rPr = main_run._element.rPr
                                u = docx.oxml.OxmlElement('w:u')
                                u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
                                u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
                                rPr.append(u)

                                # 角标处理
                                main_ref = main_match.group(2).strip()
                                main_ref_run = paragraph.add_run(main_ref)
                                main_ref_run.font.superscript = True
                                main_ref_run.font.name = 'Times New Roman'
                            else:
                                logger.info(f"不符合引用的段落：{main}")
                                not_fit_run = paragraph.add_run(main)
                                not_fit_run.font.name = '宋体'
                            # 幻觉审查错误的文本标记为红色
                            remark_run = paragraph.add_run(remark)
                            remark_run.font.color.rgb = RGBColor(255, 0, 0)

                            remark_run.font.name = '宋体'
                            remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                            # 设置西文字体为Times New Roman
                            remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                            remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                            # 结束的标点符号
                            dot_run = paragraph.add_run(dot)
                            dot_run.font.name = '宋体'
                        except Exception as e:
                            logger.error(f"pattern_one里面报错:{str(e)}")
                    elif two_match:
                        try:
                            main = two_match.group('main').strip()
                            dot = two_match.group('dot')
                            # 角标处理
                            main_match = re.match(CITATION_NUM_RE, main)
                            main_content = main_match.group(1).strip()
                            main_run = paragraph.add_run(main_content)
                            main_run.font.name = '宋体'
                            main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                            main_ref = main_match.group(2).strip()
                            main_ref_run = paragraph.add_run(main_ref)
                            main_ref_run.font.superscript = True
                            main_ref_run.font.name = 'Times New Roman'
                            # 结束的标点符号
                            dot_run = paragraph.add_run(dot)
                            dot_run.font.name = '宋体'
                            dot_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                            combineText = main + dot
                            # 这是因为有这种引用： 我是程序员[1],（注意这里不是句号）我爱中国
                            if combineText != part:
                                left_data = part[len(combineText):]
                                left_run = paragraph.add_run(left_data)
                                left_run.font.name = '宋体'
                                left_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                        except Exception as e:
                            logger.error(f"pattern_two里面报错:{str(e)}")
                    # 这是正常不存在引用序号的文本
                    else:
                        other_part_run = paragraph.add_run(part)
                        other_part_run.font.name = '宋体'
                        other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
                        # 设置西文字体为Times New Roman
                        other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
                        other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                        try:
                            if part in allBoldTextMap:
                                other_part_run.font.bold = True
                        except Exception as e:
                            logger.info(f"这个段落不适合作为key: {str(e)}")
        except Exception as e:
            logger.error(f"处理有引用的段落的报错：{str(e)}")
        # 正文里面引用参考文献的处理
        # try:
        #     literature_pattern = r'\S+\[\d+(?:,\s*\d+)*\].*?[。！!?？\n\.；]'
        #     search_match = re.search(literature_pattern, para_text)
        #     if search_match:
        #         allBoldTextMap = []
        #         for run in paragraph.runs:
        #             key = run.text
        #             if run.font and run.font.bold:
        #                 allBoldTextMap.append(key)
        #         paragraph.clear()
        #         parts = split_text_by_sentences(para_text, r'(。|：|\n|\|\?|\!|？|！|\.|；)')
        #         for part in parts:
        #             # 这是幻觉审查里面要变颜色和加下划线的正文引用部分
        #             pattern_one = r'(?P<main>.+?\[\d+(?:,\s*\d+)*\])(?P<remark>\([^\)]+?\))(?P<dot>.+?)'
        #             # 这是正常的正文引用部分
        #             pattern_two = r'(?P<main>.+?\[\d+(?:,\s*\d+)*\])(?P<dot>.+?)'
        #             one_match = re.match(pattern_one, part)
        #             two_match = re.match(pattern_two, part)
        #             main = ""
        #             remark = ""
        #             dot = ""
        #             if one_match:
        #                 try:
        #                     main = one_match.group('main')
        #                     remark = one_match.group('remark')
        #                     dot = one_match.group('dot')
        #                     main_match = re.match(CITATION_NUM_RE, main)
        #                     if main_match:
        #                         main_content = main_match.group(1).strip()
        #                         main_run = paragraph.add_run(main_content)
        #                         main_run.font.underline = True
        #                         main_run.font.name = '宋体'
        #                         main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                         # 设置西文字体为Times New Roman
        #                         main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        #                         main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        #                         # 画下划线
        #                         rPr = main_run._element.rPr
        #                         u = docx.oxml.OxmlElement('w:u')
        #                         u.set(docx.oxml.ns.qn('w:val'), 'single')  # 单实线下划线
        #                         u.set(docx.oxml.ns.qn('w:color'), "FF0000")  # 设置下划线颜色为红色
        #                         rPr.append(u)

        #                         # 角标处理
        #                         main_ref = main_match.group(2).strip()
        #                         main_ref_run = paragraph.add_run(main_ref)
        #                         main_ref_run.font.superscript = True
        #                         main_ref_run.font.name = 'Times New Roman'
        #                     else:
        #                         logger.info(f"不符合引用的段落：{main}")
        #                         not_fit_run = paragraph.add_run(main)
        #                         not_fit_run.font.name = '宋体'
        #                         not_fit_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                         # 设置西文字体为Times New Roman
        #                         not_fit_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        #                         not_fit_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        #                     # 幻觉审查错误的文本标记为红色
        #                     remark_run = paragraph.add_run(remark)
        #                     remark_run.font.color.rgb = RGBColor(255, 0, 0)

        #                     remark_run.font.name = '宋体'
        #                     remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                     # 设置西文字体为Times New Roman
        #                     remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        #                     remark_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        #                     # 结束的标点符号
        #                     dot_run = paragraph.add_run(dot)
        #                     dot_run.font.name = '宋体'
        #                 except Exception as e:
        #                     logger.error(f"pattern_one里面报错:{str(e)}")
        #             elif two_match:
        #                 try:
        #                     main = two_match.group('main').strip()
        #                     dot = two_match.group('dot')
        #                     # 角标处理
        #                     main_match = re.match(CITATION_NUM_RE, main)
        #                     main_content = main_match.group(1).strip()
        #                     main_run = paragraph.add_run(main_content)
        #                     # main_run.font.name = '宋体'
        #                     # main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                     main_run.font.name = '宋体'
        #                     main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                     # 设置西文字体为Times New Roman
        #                     main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        #                     main_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        #                     main_ref = main_match.group(2).strip()
        #                     main_ref_run = paragraph.add_run(main_ref)
        #                     main_ref_run.font.superscript = True
        #                     main_ref_run.font.name = 'Times New Roman'
        #                     # 结束的标点符号
        #                     dot_run = paragraph.add_run(dot)
        #                     dot_run.font.name = '宋体'
        #                     dot_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                     combineText = main + dot
        #                     # 这是因为有这种引用： 我是程序员[1],（注意这里不是句号）我爱中国
        #                     if combineText != part:
        #                         left_data = part[len(combineText):]
        #                         left_run = paragraph.add_run(left_data)
        #                         # left_run.font.name = '宋体'
        #                         # left_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                         left_run.font.name = '宋体'
        #                         left_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                         # 设置西文字体为Times New Roman
        #                         left_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        #                         left_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
                                
        #                 except Exception as e:
        #                     logger.error(f"pattern_two里面报错:{str(e)}")
        #             # 这是正常不存在引用序号的文本
        #             else:
        #                 other_part_run = paragraph.add_run(part)
        #                 other_part_run.font.name = '宋体'
        #                 other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:eastAsia'), '宋体')
        #                 # 设置西文字体为Times New Roman
        #                 other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:ascii'), 'Times New Roman')
        #                 other_part_run._element.rPr.rFonts.set(docx.oxml.ns.qn('w:hAnsi'), 'Times New Roman')
        #                 try:
        #                     if part in allBoldTextMap:
        #                         other_part_run.font.bold = True
        #                 except Exception as e:
        #                     logger.info(f"这个段落不适合作为key: {str(e)}")
        # except Exception as e:
        #     logger.error(f"处理有引用的段落的报错：{str(e)}")
    return doc

# 通用的Markdown转Word处理函数
def convert_markdown_to_docx(
    file_path: str
):
    """
    将Markdown文件转换为格式化的Word文档
    
    Args:
        file_path: Markdown文件的绝对路径或者相对路径
        
    Returns:
        tuple: (临时文件路径, 下载文件名)
    """
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    filename = os.path.basename(abs_path)
    output_filename = filename.replace(".txt", ".docx")
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp:
        temp_docx_file = tmp.name
    try:
        text = read_file_content(abs_path)
    except Exception as e:
        raise e
    # 转换文件
    pypandoc.convert_text(
        change_markdown_paragraph_join_way(text),
        'docx',
        outputfile=temp_docx_file,
        format='markdown',
        extra_args=[
            '--standalone',
            '--shift-heading-level-by=0'  # 保持原始标题级别: # -> 标题1, ## -> 标题2, ### -> 标题3
        ]
    )
    
    # 使用python-docx处理文档格式
    doc = Document(temp_docx_file)
    doc = format_docx_file(doc)
    doc.save(temp_docx_file)
    
    # return temp_docx_file, output_filename
    # 返回文件下载响应
    return FileResponse(
        path=temp_docx_file,
        filename=output_filename,
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        background=BackgroundTask(lambda: os.unlink(temp_docx_file) if os.path.exists(temp_docx_file) else None)
    )
# 参考文献幻觉审查时参考文献的拼接方式
def hallucination_combine_citation(
    # 参考文献除url的文本可能包含序号也可能不包含序号，就看index有没有传
    full_text: str,
    # 参考文献的序号
    index: Optional[int] = None,
    # 参考文献的链接
    url: Optional[str] = None,
    # 错误信息
    error_list: List[str] = []
):
    text = f"{[index]} " if index else ""
    if error_list:
        text += f"<span style='text-decoration: underline;text-decoration-color: red;'>"
        if url:
            text += f"[{full_text}]({url})</span><span style='color: red'>&#40;{'、'.join(error_list)}&#41;</span>"
        else:
            text += f"{full_text}</span><span style='color: red'>&#40;{'、'.join(error_list)}&#41;</span>"
    else:
        if url:
            text += f"[{full_text}]({url})"
        else:
            text += f"{full_text}"
    return text

#将word文档转成markdown，这里只是简单的
def docx_file_to_markdown(
    file_path: str
):
    abs_path = ""
    # 获取绝对路径
    if os.path.isabs(file_path):
        abs_path = file_path
    else:
        abs_path = os.path.join(os.getcwd(), file_path)
    output = pypandoc.convert_file(abs_path, 'md')
    return output