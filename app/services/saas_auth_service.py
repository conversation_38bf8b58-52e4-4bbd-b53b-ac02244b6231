import httpx
from typing import Tuple, Optional, Dict, List, Set
from datetime import timed<PERSON>ta
import json

from app.core.redis_client import get_redis_client
from app.models import UserReportUsage, Menu, OrganizationRoleMenu, OrganizationMenu, ModelConfig, OrganizationModelUses, OrganizationModels
from app.models.organization_model_use import UseCase
from app.models.saas_platform import SaasPlatform
from app.models.saas_user_mapping import SaasUserMapping
from app.models.user import User
from app.models.organizations import Organizations
from app.models.role import Role
from app.core.security import create_access_token, get_password_hash, generate_complex_password
from app.core.config import settings
from app.core.logging import get_logger
from app.api.schemas.role import InsetRole
from tortoise.transactions import atomic
from tortoise.functions import Sum

logger = get_logger(__name__)

# 默认配置的菜单列表
DEFAULT_MENUS: List[str] = ['idea_gen']


async def fetch_saas_user_details(platform_config: SaasPlatform, code: str) -> Dict[str, str]:
    """
    通过code与SaaS平台交互，换取Token并获取用户信息。
    增加了更长的超时时间和更详细的日志以调试502错误。
    """
    # 设置一个更长的超时时间（例如30秒），防止因网络延迟导致502错误
    timeout = httpx.Timeout(30.0, connect=5.0)

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            # --- 1. 获取Token ---
            token_url = platform_config.token_url
            token_payload = {
                "clientId": platform_config.client_id,
                "clientSecret": platform_config.client_secret,
                "code": code,
            }
            logger.info(f"SaaS平台-{platform_config.platform_name}：准备向 {token_url} 发送POST请求获取Token, 请求体: {json.dumps(token_payload, ensure_ascii=False)}")

            token_response = await client.post(token_url, json=token_payload)
            token_response.raise_for_status()  # 如果状态码不是 2xx，则会引发HTTPStatusError
            token_data = token_response.json()
            logger.info(f"SaaS平台-{platform_config.platform_name}：获取Token响应: {token_data}")

            if token_data.get("err_code") != 0:
                logger.error(f"SaaS平台-{platform_config.platform_name}：获取Token失败: {token_data.get('err_info')}")
                raise Exception(f"获取Token失败: {token_data.get('err_info')}")

            access_token = token_data.get("access_token")
            if not access_token:
                raise Exception("返回的access_token为空")

            logger.info(f"SaaS平台-{platform_config.platform_name}：成功获取Token。")

            # --- 2. 获取用户信息 ---
            userinfo_url = platform_config.userinfo_url
            headers = {"Authorization": f"Bearer {access_token}"}
            logger.info(f"SaaS平台-{platform_config.platform_name}：准备向 {userinfo_url} 发送GET请求获取用户信息...")
            logger.info(f"SaaS平台-{platform_config.platform_name}：用户信息请求头: {headers}")

            userinfo_response = await client.get(userinfo_url, headers=headers)
            userinfo_response.raise_for_status()
            userinfo_data = userinfo_response.json()
            logger.info(f"SaaS平台-{platform_config.platform_name}：用户信息响应: {userinfo_data}")

            if userinfo_data.get("err_code") != 0:
                raise Exception(f"获取用户信息失败: {userinfo_data.get('err_info')}")

            external_id, mobile, name = userinfo_data.get("id"), userinfo_data.get("mobile"), userinfo_data.get("name")
            if not all([external_id, mobile, name]):
                raise Exception("返回的用户信息不完整")

            logger.info(f"SaaS平台-{platform_config.platform_name}：成功获取用户信息: {name} ({mobile})")
            return {"external_id": external_id, "mobile": mobile, "name": name}

    except httpx.TimeoutException as e:
        logger.error(f"SaaS平台-{platform_config.platform_name}：请求超时, URL={e.request.url}", exc_info=True)
        raise Exception(f"SaaS平台-{platform_config.platform_name}：请求超时")
    except httpx.HTTPStatusError as e:
        # 记录下请求和响应的详细信息，便于排查
        logger.error(f"SaaS平台-{platform_config.platform_name}：返回错误状态码: URL={e.request.url}, Status={e.response.status_code}",
                     exc_info=True)
        logger.error(f"SaaS平台-{platform_config.platform_name}：响应内容: {e.response.text}")
        raise Exception(f"SaaS平台-{platform_config.platform_name}返回错误")
    except Exception as e:
        logger.error(f"SaaS平台-{platform_config.platform_name}：通信时发生未知错误: {e}", exc_info=True)
        raise Exception(f"SaaS平台-{platform_config.platform_name}: 通信失败: {e}")

async def assign_menus_to_organization(organization: Organizations, menu_identifiers: List[str]):
    """
    为机构分配一系列菜单及其所有子菜单。

    :param organization: 目标机构
    :param menu_identifiers: 需要分配的顶级菜单的标识符列表
    """
    if not menu_identifiers:
        return

    logger.info(f"SaaS平台-{organization.name}: 开始为机构分配菜单: {menu_identifiers}")

    # 1. 一次性获取所有菜单，在内存中构建树形关系
    all_menus = await Menu.filter(is_deleted=False).all()
    menu_map: Dict[str, Menu] = {menu.id: menu for menu in all_menus}
    parent_child_map: Dict[str, List[str]] = {}
    for menu in all_menus:
        if menu.parent_id:
            if menu.parent_id not in parent_child_map:
                parent_child_map[menu.parent_id] = []
            parent_child_map[menu.parent_id].append(menu.id)

    # 2. 递归函数，用于查找一个菜单下的所有子孙菜单
    def find_all_child_ids(menu_id: str, collected_ids: Set[str]):
        if menu_id in parent_child_map:
            for child_id in parent_child_map[menu_id]:
                if child_id not in collected_ids:
                    collected_ids.add(child_id)
                    find_all_child_ids(child_id, collected_ids)

    # 3. 找出所有需要关联的菜单ID
    all_menu_ids_to_link: Set[str] = set()
    root_menus_to_assign = await Menu.filter(identifier__in=menu_identifiers, is_deleted=False).all()

    for root_menu in root_menus_to_assign:
        all_menu_ids_to_link.add(root_menu.id)
        find_all_child_ids(root_menu.id, all_menu_ids_to_link)

    # 4. 批量创建关联关系
    for menu_id in all_menu_ids_to_link:
        try:
            await OrganizationMenu.create(
                organization_id=organization.id,
                menu_id=menu_id
            )
        except Exception as e:
            menu_name = menu_map.get(menu_id, Menu(name=f"ID: {menu_id}")).name
            logger.error(f"SaaS平台-{organization.name}: 为机构添加菜单'{menu_name}'时出错: {e}", exc_info=True)

    logger.info(f"SaaS平台-{organization.name}: 为机构成功关联 {len(all_menu_ids_to_link)} 个菜单。")


async def assign_menus_to_role(organization: Organizations, role: Role, menu_identifiers: List[str]):
    """
    为指定角色分配一系列菜单及其所有子菜单。

    :param organization: 目标机构
    :param role: 目标角色
    :param menu_identifiers: 需要分配的顶级菜单的标识符列表
    """
    if not menu_identifiers:
        return

    logger.info(f"SaaS平台-{organization.name}: 开始为角色 '{role.name}' 分配菜单: {menu_identifiers}")

    # 1. 一次性获取所有菜单，在内存中构建树形关系
    all_menus = await Menu.filter(is_deleted=False).all()
    menu_map: Dict[str, Menu] = {menu.id: menu for menu in all_menus}
    parent_child_map: Dict[str, List[str]] = {}
    for menu in all_menus:
        if menu.parent_id:
            if menu.parent_id not in parent_child_map:
                parent_child_map[menu.parent_id] = []
            parent_child_map[menu.parent_id].append(menu.id)

    # 2. 递归函数，用于查找一个菜单下的所有子孙菜单
    def find_all_child_ids(menu_id: str, collected_ids: Set[str]):
        if menu_id in parent_child_map:
            for child_id in parent_child_map[menu_id]:
                if child_id not in collected_ids:
                    collected_ids.add(child_id)
                    find_all_child_ids(child_id, collected_ids)

    # 3. 找出所有需要关联的菜单ID
    all_menu_ids_to_link: Set[str] = set()
    root_menus_to_assign = await Menu.filter(identifier__in=menu_identifiers, is_deleted=False).all()

    for root_menu in root_menus_to_assign:
        all_menu_ids_to_link.add(root_menu.id)
        find_all_child_ids(root_menu.id, all_menu_ids_to_link)

    # 4. 批量创建关联关系
    for menu_id in all_menu_ids_to_link:
        try:
            await OrganizationRoleMenu.get_or_create(
                organization_id=organization.id,
                role_id=role.id,
                menu_id=menu_id
            )
        except Exception as e:
            menu_name = menu_map.get(menu_id, Menu(name=f"ID: {menu_id}")).name
            logger.error(f"SaaS平台-{organization.name}: 关联角色'{role.name}'到菜单'{menu_name}'时出错: {e}", exc_info=True)

    logger.info(f"SaaS平台-{organization.name}: 为角色'{role.name}'成功关联 {len(all_menu_ids_to_link)} 个菜单。")

async def create_organization_and_roles(platform_config: SaasPlatform) -> Organizations:
    """
    确保SaaS平台对应的虚拟机构、管理员和普通用户角色及其权限都存在。
    """
    logger.info(f"SaaS平台-{platform_config.platform_name}：虚拟机构不存在，创建新机构: {platform_config.platform_name}")
    # 从配置中读取默认机构使用次数
    default_org_usage_count = settings.DEFAULT_ORG_USAGE_COUNT
    
    v_org, _ = await Organizations.get_or_create(
        code=platform_config.platform,
        defaults={
            "name": platform_config.platform_name,
            "type": "合作机构",
            "contact_person": platform_config.platform_name,
            "contact_phone": "13800138000",
            "contact_email": "<EMAIL>",
            "is_trial": "true",
            "limit_count": default_org_usage_count,  # 使用配置的默认值
            "use_count": 0  # 初始化使用次数为0
        }
    )
    # 为机构配置菜单
    await assign_menus_to_organization(v_org, DEFAULT_MENUS)

    # 从配置中读取默认机构模型
    default_org_model = settings.DEFAULT_ORG_MODEL
    model = await ModelConfig.filter(model_name=default_org_model).first()
    if not model:
        raise Exception(f"SaaS平台-{platform_config.platform_name}: 模型配置错误")
    # 为机构配置默认模型
    await OrganizationModels.create(
        organization_id=v_org.id,
        model_id=model.id,
    )
    # 为机构配置用途的默认模型
    for use_case in UseCase:
        await OrganizationModelUses.create(
            organization_id=v_org.id,
            model_id=model.id,
            default_way=use_case
        )

    # 为新机构创建管理员角色和用户
    admin_role, _ = await Role.get_or_create(
        identifier=InsetRole.ADMIN,
        organization=v_org,
        defaults={"name": "管理员"}
    )
    admin_password = generate_complex_password()
    admin_user, created = await User.get_or_create(
        username=f"{v_org.code}_admin",
        organization=v_org,
        defaults={
            "hashed_password": get_password_hash(admin_password),
            "realname": f"{v_org.name}管理员",
            "role": admin_role
        }
    )

    # 为管理员角色配置菜单
    await assign_menus_to_role(v_org, admin_role, DEFAULT_MENUS)

    # 为管理员创建使用次数记录，管理员不受限制
    await UserReportUsage.create(user_id_id=admin_user.id, used_count=0, max_allowed_count=None)

    # 确保普通用户角色存在，并仅在首次创建时关联菜单
    normal_user_role, _ = await Role.get_or_create(
        identifier='user', organization=v_org, defaults={"name": "普通用户"}
    )

    # 为普通用户角色配置菜单
    await assign_menus_to_role(v_org, normal_user_role, DEFAULT_MENUS)

    return v_org


async def get_or_create_user(platform_identifier: str, platform_name: str, user_details: Dict[str, str]) -> User:
    """
    根据SaaS平台返回的用户信息，查找或在本系统中创建用户。
    """
    external_id = user_details["external_id"]
    mobile = user_details["mobile"]
    name = user_details["name"]

    # 检查映射表
    user_mapping = await SaasUserMapping.get_or_none(platform=platform_identifier, external_id=external_id)
    if user_mapping:
        user = await User.get_or_none(id=user_mapping.user_id, is_deleted=False)
        return user

    # 如果用户已经在其他机构存在了，也可以登录，只不过不计入这个机构
    user = await User.get_or_none(username=mobile, is_deleted=False)
    if user:
        logger.info(f"SaaS平台-{platform_name}：通过手机号找到{platform_name}现有用户: {user.username}，创建映射关系")
        await SaasUserMapping.create(user_id=user.id, platform=platform_identifier, external_id=external_id, mobile=mobile, name=name)
        return user

    # 查询有没有机构
    org = await Organizations.get_or_none(code=platform_identifier)
    if not org:
        # 没有机构创建虚拟机构
        platform_config = SaasPlatform(platform=platform_identifier, platform_name=platform_name)
        org = await create_organization_and_roles(platform_config)

    normal_user_role = await Role.get(identifier='user', organization=org)
    # 创建本次登录的用户
    user_password = generate_complex_password()
    new_user, _ = await User.get_or_create(
        username=mobile,
        organization=org,
        defaults={
            "hashed_password": get_password_hash(user_password),
            "realname": name,
            "mobile": mobile,
            "role": normal_user_role
        }
    )
    
    # 从配置中读取默认用户使用次数
    default_user_usage_count = settings.DEFAULT_USER_USAGE_COUNT

    # 获取该机构下所有用户的ID
    users_ids = await User.filter(organization_id=org.id, is_deleted=False).values_list('id', flat=True)
    
    # 计算这些用户的总分配次数
    total_allocated = 0
    if users_ids:
        result = await UserReportUsage.filter(user_id_id__in=users_ids).annotate(
            total=Sum('max_allowed_count')
        ).values_list('total', flat=True)
        if result and result[0] is not None:
            total_allocated = result[0]
    
    # 计算机构真正的剩余使用次数
    org_remaining_count = 0
    if org.limit_count is not None:
        org_remaining_count = max(0, org.limit_count - total_allocated)
    logger.info(f"SaaS平台-{platform_name}：机构 {org.name} 总限制 {org.limit_count}，已分配 {total_allocated}，剩余 {org_remaining_count}")
    
    # 确定给用户分配的次数
    user_allowed_count = 0
    if org_remaining_count > 0:
        # 如果机构剩余次数小于默认用户次数，则给用户分配剩余的机构次数
        user_allowed_count = min(default_user_usage_count, org_remaining_count)
    
    # 为新用户创建使用次数记录
    await UserReportUsage.create(user_id_id=new_user.id, used_count=0, max_allowed_count=user_allowed_count)
    logger.info(f"SaaS平台-{platform_name}：成功创建新用户: {new_user.username}，分配使用次数: {user_allowed_count}")

    # 为新用户创建映射关系
    await SaasUserMapping.create(user_id=new_user.id, platform=platform_identifier, external_id=external_id, mobile=mobile, name=name)
    return new_user


async def issue_jwt_and_cache(user: User) -> str:
    """
    为用户生成JWT，并根据配置存入Redis。
    """
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=json.dumps({"username": user.username}),
        expires_delta=access_token_expires
    )

    if settings.IS_OPEN_SSO == 'ON':
        # 将 token 存入 redis，key 为 sso:{username}，value 为 access_token
        redis = get_redis_client()
        redis_key = f"sso:{user.username}"
        # 如果已经有用户登录过，那么删除之前的 token
        if await redis.get(redis_key):
            await redis.delete(redis_key)
        await redis.set(
            redis_key,
            access_token,
            ex=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    return access_token

@atomic()
async def login_from_saas(platform_identifier: str, code: str, redirect_url: Optional[str]) -> Tuple[
    str, Optional[str]]:
    """
    处理来自SaaS平台的登录请求。
    """
    # 获取平台配置
    platform_config = await SaasPlatform.get_or_none(platform=platform_identifier, is_active=True)
    if not platform_config:
        raise Exception(f"SaaS平台 '{platform_identifier}' 不存在或未激活")

    # 与SaaS平台交互，获取用户信息
    user_details = await fetch_saas_user_details(platform_config, code)

    # 在本系统中查找或创建用户
    user = await get_or_create_user(
        platform_identifier=platform_config.platform,
        platform_name=platform_config.platform_name,
        user_details=user_details
    )

    # 生成JWT并存入Redis
    access_token = await issue_jwt_and_cache(user)

    return access_token, redirect_url
