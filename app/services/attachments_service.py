import os
import asyncio
from typing import List, Dict, Optional, Any
from uuid import UUID
import uuid
import PyPDF2
# from app.api.routes.model_configs import get_current_user_models
# from app.models.model_config import ModelConfig
from app.services import prompts
from app.utils.utils import send_data
import docx
from io import BytesIO
import mimetypes
import chardet
import re
from fastapi import UploadFile

from app.models.requirements_attachments_files import RequirementsAttachmentFiles
from app.models.project_configs import ProjectConfig
from app.models.user import User
from app.services.llm_service import call_llm
from app.services.prompts import generate_requirements_analysis_prompt
from app.core.logging import get_logger
from app.api.schemas.requirements_attachment_files import RequirementsTypeContent

logger = get_logger(__name__)

async def extract_text_from_file(file_path: str) -> Optional[str]:
    """
    从文件中提取文本内容
    
    Args:
        file_path: 文件路径
        
    Returns:
        提取的文本内容，如果不支持的文件类型则返回None
    """
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return None
    
    file_ext = os.path.splitext(file_path)[1].lower()
    
    try:
        # 尝试猜测文件类型
        mime_type, _ = mimetypes.guess_type(file_path)
        logger.info(f"文件路径: {file_path}, 扩展名: {file_ext}, MIME类型: {mime_type}")
        
        # 先尝试检测文件是否为文本文件
        is_text_file = False
        try:
            with open(file_path, 'rb') as file:
                # 读取文件前4KB来检测
                raw_data = file.read(4096)
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']
                logger.info(f"检测到的编码: {encoding}, 置信度: {confidence}")
                
                # 如果置信度高且编码是文本编码，可能是文本文件
                if confidence > 0.7 and encoding and encoding.lower() in ['utf-8', 'ascii', 'utf-16', 'gb2312', 'gbk', 'gb18030']:
                    is_text_file = True
        except Exception as e:
            logger.warning(f"检测文件编码时出错: {str(e)}")
        
        # 如果确定是文本文件，无论扩展名如何都直接读取
        if is_text_file:
            logger.info(f"检测到文本文件，直接读取: {file_path}")
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    return file.read()
            except Exception as text_e:
                logger.warning(f"尝试以文本方式读取失败: {str(text_e)}")
        
        # PDF文件处理
        if file_ext == '.pdf' or mime_type == 'application/pdf':
            try:
                text = ""
                with open(file_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    for page_num in range(len(reader.pages)):
                        page = reader.pages[page_num]
                        text += page.extract_text() + "\n"
                return text
            except Exception as pdf_error:
                logger.error(f"PDF解析失败: {str(pdf_error)}")
                return f"[PDF解析错误: {str(pdf_error)}]"
        
        # Word文档处理
        elif file_ext in ['.docx', '.doc'] or mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
            try:
                logger.info(f"开始处理Word文档: {file_path}")
                
                # 验证文件是否存在且可访问
                if not os.path.isfile(file_path):
                    logger.error(f"Word文件不存在或无法访问: {file_path}")
                    return f"[文件不存在或无法访问: {file_path}]"
                
                # 检查文件大小是否为0
                if os.path.getsize(file_path) == 0:
                    logger.error(f"Word文件大小为0: {file_path}")
                    return f"[空文件: {file_path}]"
                
                # 多种方法尝试提取文本
                extraction_methods = [
                    # 方法1: 使用python-docx
                    lambda: extract_with_python_docx(file_path),
                    # 方法2: 尝试将其作为ZIP读取
                    lambda: extract_from_docx_as_zip(file_path) if file_ext == '.docx' else None,
                    # 方法3: 读取二进制文件并尝试提取文本
                    lambda: extract_text_from_binary(file_path)
                ]
                
                for method_idx, extraction_method in enumerate(extraction_methods):
                    try:
                        logger.info(f"尝试使用方法 {method_idx+1} 提取文本")
                        text = extraction_method()
                        if text:
                            logger.info(f"方法 {method_idx+1} 成功提取文本，长度: {len(text)}")
                            return text
                        else:
                            logger.warning(f"方法 {method_idx+1} 未能提取到文本")
                    except Exception as method_error:
                        logger.error(f"方法 {method_idx+1} 失败: {str(method_error)}")
                
                # 如果所有方法都失败，返回错误
                logger.error("所有文本提取方法都失败")
                return "[无法提取文档内容]"
            except Exception as e:
                logger.error(f"Word文档处理外部错误: {str(e)}")
                return f"[Word文档处理错误: {str(e)}]"
        
        # 纯文本文件处理
        elif file_ext in ['.txt', '.md', '.json', '.csv']:
            try:
                # 尝试不同的编码打开文件
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin-1']
                for enc in encodings_to_try:
                    try:
                        with open(file_path, 'r', encoding=enc) as file:
                            content = file.read()
                            logger.info(f"成功以 {enc} 编码读取文件")
                            return content
                    except UnicodeDecodeError:
                        continue
                
                # 如果所有编码都失败，尝试使用二进制方式读取
                return extract_text_from_binary(file_path)
            except Exception as text_error:
                logger.error(f"文本文件读取失败: {str(text_error)}")
                return f"[文本文件读取错误: {str(text_error)}]"
        
        else:
            logger.warning(f"不支持的文件类型: {file_ext}, MIME: {mime_type}")
            # 尝试作为二进制文件处理
            return extract_text_from_binary(file_path)
            
    except Exception as e:
        logger.error(f"提取文件内容时出错: {str(e)}")
        return f"[文件处理错误: {str(e)}]"

# 辅助函数：使用python-docx提取文本
def extract_with_python_docx(file_path: str) -> Optional[str]:
    try:
        doc = docx.Document(file_path)
        text = ""
        
        # 提取段落文本
        for para in doc.paragraphs:
            text += para.text + "\n"
        
        # 如果提取的文本为空，尝试读取表格内容
        if not text.strip():
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
        
        return text
    except Exception as e:
        logger.error(f"python-docx提取失败: {str(e)}")
        return None

# 辅助函数：将.docx文件作为ZIP文件读取
def extract_from_docx_as_zip(file_path: str) -> Optional[str]:
    try:
        import zipfile
        from xml.etree.ElementTree import XML
        
        WORD_NAMESPACE = '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}'
        PARA = WORD_NAMESPACE + 'p'
        TEXT = WORD_NAMESPACE + 't'
        
        with zipfile.ZipFile(file_path) as zip_ref:
            if 'word/document.xml' in zip_ref.namelist():
                xml_content = zip_ref.read('word/document.xml')
                tree = XML(xml_content)
                paragraphs = []
                for paragraph in tree.iter(PARA):
                    texts = [node.text for node in paragraph.iter(TEXT) if node.text]
                    if texts:
                        paragraphs.append(''.join(texts))
                return '\n'.join(paragraphs)
            else:
                logger.warning("ZIP文件中没有找到word/document.xml")
                return None
    except Exception as e:
        logger.error(f"ZIP方法提取失败: {str(e)}")
        return None

# 辅助函数：从二进制文件中提取文本
def extract_text_from_binary(file_path: str) -> str:
    try:
        with open(file_path, 'rb') as file:
            binary_data = file.read()
        
        # 尝试检测编码
        result = chardet.detect(binary_data)
        encoding = result['encoding']
        confidence = result['confidence']
        logger.info(f"二进制文件检测到的编码: {encoding}, 置信度: {confidence}")
        
        # 尝试将二进制数据解码为文本
        if encoding and confidence > 0.5:
            try:
                text = binary_data.decode(encoding)
                
                # 只保留可打印字符，移除控制字符
                printable_text = re.sub(r'[^\x20-\x7E\n\r\t\u4e00-\u9fff]', ' ', text)
                
                # 清理文本，删除连续的空格、空行等
                cleaned_text = re.sub(r'\s+', ' ', printable_text)
                cleaned_text = re.sub(r'\n\s*\n', '\n\n', cleaned_text)
                
                return cleaned_text
            except UnicodeDecodeError:
                logger.warning(f"无法以 {encoding} 解码二进制数据")
        
        # 如果解码失败，尝试提取所有可能的ASCII/Unicode字符串
        text_fragments = re.findall(b'[\x20-\x7E\n\r\t]{4,}', binary_data)
        if text_fragments:
            decoded_fragments = [fragment.decode('ascii', errors='ignore') for fragment in text_fragments]
            return '\n'.join(decoded_fragments)
        
        # 最后尝试使用latin-1编码强制解码
        forced_text = binary_data.decode('latin-1', errors='ignore')
        printable_text = re.sub(r'[^\x20-\x7E\n\r\t\u4e00-\u9fff]', ' ', forced_text)
        return printable_text
    except Exception as e:
        logger.error(f"二进制文本提取失败: {str(e)}")
        return f"[二进制文本提取错误: {str(e)}]"

