import asyncio
import functools
import json
import re
import traceback
from typing import Dict, List, Optional, Any, Tuple, AsyncGenerator
from app.api.schemas.project_configs import LiteratureLibraryType
from app.models.dictionary import Dictionary
from pydantic import BaseModel
from pymed import PubMed
from app.core.config import settings
from app.core.logging import get_logger
from app.models.research import Research, ResearchStatus, ResearchResource
from app.services import llm_service, search_service, prompts
from app.api.schemas.research import Reference
from app.api.schemas.literatures import LiteratureCreate
from app.api.repository.literatures import create_literature, get_literature_by_id
from app.models.literature_library import LiteratureLibrary

pubmed = PubMed(tool=settings.PUBMED_TOOL_NAME, email=settings.PUBMED_EMAIL)

# 获取logger实例
logger = get_logger(__name__)


async def create_research(query: str) -> Research:
    """创建新研究"""
    logger.info(f"创建新研究，查询: {query[:100]}...")
    research = await Research.create(
        query=query,
        search_queries=[],
        contexts=[],
        status=ResearchStatus.PENDING
    )
    logger.info(f"研究创建成功，ID: {research.id}")
    return research


async def generate_search_queries(
    title: str,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    # google | google_scholar
    search_engine: str = "google"
) -> List[str]:
    """
    为研究生成搜索查询
    
    Args:
        research: 研究实例
    
    Returns:
        搜索查询列表
    """
    logger.info(f"研究【{title}】: 开始生成搜索查询")
    # 准备提示词
    prompt = await prompts.generate_search_queries_prompt(
        title,
        count='5',
        search_engine=search_engine
    )
    messages = [
        {"role": "system", "content": prompts.SEARCH_QUERIES_PROMPT_SYSTEM},
        {"role": "user", "content": prompt}
    ] if search_engine == 'google' else [
        {"role": "user", "content": prompt}
    ]
    # 调用LLM
    logger.info(f"研究 【{title}】: 调用LLM生成google查询的关键词")
    try:
        response = await llm_service.call_llm(
            messages=messages,
            flag="调用LLM生成搜索查询的关键词",
            apiKey=api_key,
            apiUrl=api_url,
            model=model
        )
        if not response:
            logger.error(f"研究【{title}】: LLM没有返回google查询的关键词")
            return []
        
        # 记录原始响应，用于调试
        logger.info(f"研究【{title}】: 原始响应: {response}")
        
        # 解析响应，提取搜索查询 - 使用多种可能的格式
        try:
            if search_engine == 'google':
                # 尝试直接作为JSON解析
                if response.strip().startswith('[') and response.strip().endswith(']'):
                    try:
                        search_queries = json.loads(response)
                        if isinstance(search_queries, list) and all(isinstance(q, str) for q in search_queries):
                            logger.info(f"研究【{title}】: 成功解析LLM生成的关键词，关键词数量: {len(search_queries)}")
                            return search_queries
                    except json.JSONDecodeError:
                        logger.debug(f"研究【{title}】: 返回的关键词非有效JSON，尝试其他解析方法")
            else:
                try:
                    text = response.strip()
                    lines = re.split(r'\n+', text)
                    return lines
                except Exception as e:
                    logger.error(f"大模型生成谷歌学术搜索的关键出现错误：{str(e)}")      
            # 清理响应中的Markdown代码块标记
            cleaned_response = response
            
            # 移除Markdown代码块标记
            if "```" in cleaned_response:
                code_block_pattern = r"```(?:python|json)?\s*([\s\S]*?)```"
                matches = re.findall(code_block_pattern, cleaned_response)
                if matches:
                    cleaned_response = matches[0].strip()
            
            # 尝试用eval解析Python列表（安全方式）
            try:
                # 确保内容确实是列表格式
                if (cleaned_response.strip().startswith('[') and cleaned_response.strip().endswith(']')):
                    search_queries = eval(cleaned_response)
                    if isinstance(search_queries, list) and all(isinstance(q, str) for q in search_queries):
                        logger.info(f"研究 【{title}】: 成功用eval解析查询，数量: {len(search_queries)}")
                        return search_queries
            except Exception as e:
                logger.debug(f"研究 【{title}】: eval解析失败: {str(e)}")
            
            # 使用正则表达式提取引号包围的字符串
            logger.debug(f"研究 【{title}】: 尝试用正则表达式提取查询")
            queries = re.findall(r'(?:"([^"]*?)"|\'([^\']*?)\')', cleaned_response)
            extracted_queries = [q[0] if q[0] else q[1] for q in queries if q[0] or q[1]]
            if extracted_queries:
                logger.info(f"研究 【{title}】: 用正则表达式提取了 {len(extracted_queries)} 个查询")
                return extracted_queries
            
            # 拆分为行，并过滤出看起来像搜索查询的行
            logger.debug(f"研究 【{title}】: 尝试按行拆分提取查询")
            lines = cleaned_response.split('\n')
            line_queries = []
            
            for line in lines:
                line = line.strip()
                # 跳过空行和明显不是查询的行
                if not line or line.startswith('#') or len(line) < 5:
                    continue
                    
                # 移除行首的序号、破折号等
                line = re.sub(r'^[\d\-\*\.\)]+\.?\s*', '', line)
                
                if line and len(line) > 5:  # 设置最小长度过滤掉太短的内容
                    line_queries.append(line)
            
            if line_queries:
                logger.info(f"研究 【{title}】: 按行提取了 {len(line_queries)} 个查询")
                return line_queries[:10]  # 限制最多10个查询
                
            # 如果所有方法都失败，返回空列表
            logger.error(f"研究 【{title}】: 无法从响应中提取查询")
            return []
            
        except Exception as e:
            logger.error(f"研究【{title}】: 解析搜索查询时出错: {str(e)}")
            logger.error(f"研究 【{title}】: 错误详情: {traceback.format_exc()}")
            logger.debug(f"研究 【{title}】: 响应: {response}")
            
            # 尝试最基本的提取方法作为最后手段
            try:
                # 如果响应很短，可能就是一个查询
                if len(response) < 200 and '\n' not in response:
                    return [response.strip()]
                    
                # 否则按行拆分
                lines = [line.strip() for line in response.split('\n') if line.strip()]
                if lines:
                    return lines[:10]  # 限制最多10个结果
            except Exception:
                pass
                
            return []
    except Exception as e:
        logger.info(f"generate_search_queries函数报错了: {str(e)}")
        return []

async def is_page_useful(
    user_query: str,
    page_content: str,
    url: str,
    api_key: str = "",
    api_url: str = "",
    model: str = ""
) -> bool:
    """
    评估页面是否对研究有用
    
    Args:
        user_query: 用户查询
        page_content: 页面内容
    
    Returns:
        页面是否有用
    """
    # 截断过长的内容
    max_content_length = 20000  # 限制内容长度
    if len(page_content) > max_content_length:
        page_content = page_content[:max_content_length] + "..."
    
    # 准备提示词
    prompt = await prompts.evaluate_page_usefulness_prompt(user_query, page_content)
    messages = [
        {"role": "system", "content": prompts.PAGE_USEFULNESS_PROMPT_SYSTEM},
        {"role": "user", "content": prompt}
    ]
    
    # 调用LLM
    response = await llm_service.call_llm(messages=messages, flag="评估页面有用性",apiKey=api_key,apiUrl=api_url,model=model)
    if not response:
        return False
    
    # 清理响应
    response = response.strip().lower()
    
    # 检查响应是否为Yes
    if response == "yes":
        logger.info(f"研究【{user_query}】: 页面有用，返回True, url: {url}")
        return True
    else:
        logger.info(f"研究 【{user_query}】: 页面无用，返回False, url: {url}")
        return False


async def extract_relevant_context(
    user_query: str,
    search_query: str,
    page_content: str,
    api_key: str = "",
    api_url: str = "",
    model: str = ""
) -> Optional[str]:
    """
    从页面内容中提取相关上下文
    
    Args:
        user_query: 用户查询
        search_query: 搜索查询
        page_content: 页面内容
    
    Returns:
        提取的上下文，如果失败则返回None
    """
    # 截断过长的内容
    max_content_length = 20000  # 限制内容长度
    if len(page_content) > max_content_length:
        page_content = page_content[:max_content_length] + "..."
    
    # 准备提示词
    prompt = await prompts.extract_context_prompt(user_query, search_query, page_content)
    messages = [
        {"role": "system", "content": prompts.EXTRACT_CONTEXT_PROMPT_SYSTEM},
        {"role": "user", "content": prompt}
    ]
    
    # 调用LLM
    response = await llm_service.call_llm(
        messages=messages,
        flag="提取网页内容的相关上下文，用于生成报告",
        apiKey=api_key,
        apiUrl=api_url,
        model=model
    )
    return response


async def get_new_search_queries(
    research: Research,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    max_contexts: int = 0
) -> Optional[List[str]]:
    """
    基于已收集的上下文，获取新的搜索查询
    
    Args:
        research: 研究实例
    
    Returns:
        新搜索查询列表，如果不需要更多搜索返回None
    """
    logger.info(f"研究 {research.id}: 分析已有上下文，生成新搜索查询")
    
    # 检查上下文数量，如果已经收集足够多的上下文，则不生成新查询
    if len(research.contexts) >= max_contexts:
        logger.info(f"研究 {research.id}: 已收集足够多的上下文({len(research.contexts)}条)，不再生成新查询")
        return None
        
    # 检查查询数量，如果已有太多查询，则不再生成新查询
    if len(research.search_queries) >= settings.MAX_SEARCH_QUERIES:
        logger.info(f"研究 {research.id}: 搜索查询数量已达上限({len(research.search_queries)}个)，不再生成新查询")
        return None
    
    # 准备提示词
    prompt = await prompts.new_search_queries_prompt(
        research.query, 
        research.search_queries,
        research.contexts
    )
    messages = [
        {"role": "system", "content": prompts.NEW_SEARCH_QUERIES_PROMPT_SYSTEM},
        {"role": "user", "content": prompt}
    ]
    
    # 调用LLM
    logger.debug(f"研究 {research.id}: 调用LLM生成新搜索查询")
    response = await llm_service.call_llm(
        messages=messages,
        flag="基于已收集的上下文，生成新的搜索查询",
        apiKey=api_key,
        apiUrl=api_url,
        model=model
    )
    if not response:
        logger.error(f"研究 {research.id}: LLM返回空响应")
        return []
    
    # 检查是否完成
    if "<done>" in response.lower() or "完成" in response or "足够" in response:
        logger.info(f"研究 {research.id}: 搜索完成标记，不需要更多查询")
        return None
    
    # 安全解析响应，提取搜索查询
    try:
        # 清理响应
        cleaned = response
        
        # 处理可能的代码块
        if "```" in cleaned:
            code_block_pattern = r"```(?:python|json)?\s*([\s\S]*?)```"
            matches = re.findall(code_block_pattern, cleaned)
            if matches:
                cleaned = matches[0].strip()
        
        # 尝试使用多种方法解析查询
        new_queries = []
        
        # 方法1: 尝试作为JSON解析
        if cleaned.strip().startswith('[') and cleaned.strip().endswith(']'):
            try:
                parsed = json.loads(cleaned)
                if isinstance(parsed, list) and all(isinstance(q, str) for q in parsed):
                    new_queries = parsed
                    logger.info(f"研究 {research.id}: 成功用JSON解析新查询，数量: {len(new_queries)}")
            except json.JSONDecodeError:
                logger.debug(f"研究 {research.id}: JSON解析失败，尝试其他方法")
        
        # 方法2: 如果JSON解析失败，尝试用eval解析
        if not new_queries and cleaned.strip().startswith('[') and cleaned.strip().endswith(']'):
            try:
                parsed = eval(cleaned)
                if isinstance(parsed, list) and all(isinstance(q, str) for q in parsed):
                    new_queries = parsed
                    logger.info(f"研究 {research.id}: 成功用eval解析新查询，数量: {len(new_queries)}")
            except Exception as e:
                logger.debug(f"研究 {research.id}: eval解析失败: {str(e)}")
        
        # 方法3: 使用正则表达式提取引号包围的字符串
        if not new_queries:
            logger.debug(f"研究 {research.id}: 尝试用正则表达式提取查询")
            queries = re.findall(r'(?:"([^"]*?)"|\'([^\']*?)\')', cleaned)
            extracted_queries = [q[0] if q[0] else q[1] for q in queries if q[0] or q[1]]
            if extracted_queries:
                new_queries = extracted_queries
                logger.info(f"研究 {research.id}: 用正则表达式提取了 {len(new_queries)} 个查询")
        
        # 方法4: 按行拆分
        if not new_queries:
            logger.debug(f"研究 {research.id}: 尝试按行拆分提取查询")
            lines = cleaned.split('\n')
            line_queries = []
            
            for line in lines:
                line = line.strip()
                # 跳过空行和明显不是查询的行
                if not line or line.startswith('#') or len(line) < 5:
                    continue
                    
                # 移除行首的序号、破折号等
                line = re.sub(r'^[\d\-\*\.\)]+\.?\s*', '', line)
                
                if line and len(line) > 5:  # 设置最小长度过滤掉太短的内容
                    line_queries.append(line)
            
            if line_queries:
                new_queries = line_queries
                logger.info(f"研究 {research.id}: 按行提取了 {len(new_queries)} 个查询")
        
        # 限制查询数量
        if new_queries:
            # 过滤掉重复的查询
            filtered_queries = []
            for query in new_queries:
                if query not in research.search_queries and query not in filtered_queries:
                    filtered_queries.append(query)
            
            # 限制最多添加5个新查询
            if filtered_queries:
                filtered_queries = filtered_queries[:5]
                logger.info(f"研究 {research.id}: 过滤后剩余 {len(filtered_queries)} 个新查询")
                return filtered_queries
            else:
                logger.info(f"研究 {research.id}: 过滤后没有新查询")
                return None  # 没有新查询，返回None表示搜索完成
        
        # 如果所有方法都失败，返回空列表
        logger.error(f"研究 {research.id}: 无法从响应中提取查询")
        return []
        
    except Exception as e:
        logger.error(f"研究 {research.id}: 解析新搜索查询时出错: {str(e)}")
        logger.debug(f"研究 {research.id}: 响应: {response}")
        logger.debug(traceback.format_exc())
        return []


async def process_url(
    title: str,
    url: any,
    research_id: str,
    search_query: str,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    # 是否开启有用context总结文献的功能
    is_summary_literature = False
) -> Optional[str]:
    """
    处理单个URL：获取内容，判断有用性，提取上下文
    
    Args:
        url: 网页URL
        search_query: 用于获取此URL的搜索查询
    
    Returns:
        处理后的资源对象，如果处理失败则返回None
    """
    origin = url["origin"] if isinstance(url, dict) else ""
    url = url["url"] if isinstance(url, dict) else url
    logger.info(f"研究【{title}】: 处理URL, 开始获取页面内容: {url}")
    # 获取网页内容
    page_content = await search_service.fetch_webpage_text_async(url)
    if not page_content:
        logger.warning(f"研究【{title}】: 无法获取页面内容: {url}")
        return None
    
    # 清理页面内容，移除无效的UTF-8字符（尤其是空字节0x00）
    if page_content:
        # 移除控制字符（包括空字节），但保留基本格式（如换行符等）
        page_content = ''.join(char for char in page_content if char == '\n' or char == '\t' or char >= ' ')
        # 额外处理：确保有效的UTF-8
        page_content = page_content.encode('utf-8', 'ignore').decode('utf-8', 'ignore')
    
    # 判断有用性
    logger.info(f"研究【{title}】: 评估页面有用性: {url}")
    try:
        is_useful = await is_page_useful(
            title,
            page_content,
            url,
            api_key,
            api_url,
            model
        )
    except Exception as e:
        print(f"is_page_useful评估页面有用性失败: {str(e)}")
        logger.info("is_page_useful评估页面有用性失败")
        return None
    
    try:
    # 如果有用，提取上下文
        if is_useful:
            logger.info(f"研究 【{title}】: 页面有用，提取上下文: {url}")
            logger.info(f"研究 【{title}】: api_key: {api_key}, api_url: {api_url}, model: {model}")
            context = await extract_relevant_context(
                title,
                search_query,
                page_content,
                api_key,
                api_url,
                model
            )
            if context:
                if is_summary_literature:
                    reference_data = None
                    try:
                        reference_data = await is_valid_reference(
                            page_content,
                            url,
                            api_key,
                            api_url,
                            model,
                            origin["title"]
                        )
                    except Exception as e:
                        logger.error(f"is_valid_reference调用报错：{str(e)}")
                    logger.info(f"reference_data:{reference_data}")
                    literature_list = await get_literature_by_id(research_id)
                    if reference_data and len(literature_list) < settings.MAX_LITERATURES_COUNT:
                        logger.info(f"literature已经收集了{len(literature_list)}篇")
                        reference_data["research_id"] = research_id
                        create_data = LiteratureCreate.model_validate(reference_data, from_attributes=True)
                        print("reference_data_research_id:",create_data.research_id)
                        await create_literature(create_data)
                        return None
                    else:
                        logger.info(f"没有总结出文献的context内容：\n{context}")
                        logger.info(f"没有总结出文献的serpAPI返回的内容：\n{str(origin)}")
                        logger.info(f"没有总结出文献的网页地址：{url}")
                        return context
                logger.info(f"研究 【{title}】: 已将提取的上下文添加到研究中")
                # 将上下文添加到研究中
                return context
        else:
            logger.info(f"研究【{title}】: 页面不相关，跳过: {url}")
            return None
    except Exception as e:
        logger.error(f"研究  【{title}】: 处理URL时出错: {str(e)}")
        return None
        
async def is_valid_reference(
    page_content: str,
    url: str,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    title: Optional[str] = ""
) -> dict:
    """
    评估页面是否为学术论文
    
    Args:
        page_content: 页面内容
    Returns:
        有用的参考文献
    """
    # 截断过长的内容
    max_content_length = 20000  # 限制内容长度
    if len(page_content) > max_content_length:
        page_content = page_content[:max_content_length] + "..."
    # 准备提示词
    print("-----------------------提示词前面-----------------------")
    prompt = await prompts.evaluate_is_reference_prompt(
        text=page_content,
        title=title
    )
    # logger.info(f"提取文献的提示词内容：{prompt}")
    print("-----------------------提示词后面-----------------------")
    messages = [
        {"role": "system", "content": prompts.REFERENCE_EXTRACT_PROMPT_SYSTEM},
        {"role": "user", "content": prompt}
    ]
    # 调用LLM
    response = await llm_service.call_llm(
        messages=messages,
        flag="评估页面是否包含有用的参考文献",
        apiKey=api_key,
        apiUrl=api_url,
        model=model
    )
    if not response:
        return None
    try:
        print(f"返回的文献格式内容：{response}")
        match = re.search(r"json\s*(\{\{.*?\}\}|\{.*?\}|\[.*?\])\s*", response, re.DOTALL | re.IGNORECASE) or re.search(r'(\{.*\})', response, re.DOTALL)
        if match:
            json_str = match.group(1)
            result_str = json.loads(json_str)
            # 返回格式形如[{
            #     is_valid_reference: bool
            #     text: list[str]
            #     doi: str
            #     url: str
            # }]

            if isinstance(result_str, list):
                logger.warning(f"返回的文献内容为列表，应该为字典，请检查返回内容")
                return None
            else:
                # 返回格式形如{
                #     is_valid_reference: bool
                #     text: list[str]
                #     doi: str
                #     url: str
                # }
                text_data = result_str.get("text")
                is_valid = result_str.get("is_valid_reference")
                logger.info(f"is_valid_reference返回的is_valid：{is_valid}")
                if isinstance(text_data, list):
                    logger.warning(f"返回的文献text为列表，应该为字符串，请检查返回内容")
                    return None
                elif is_valid:
                    title_text = result_str.get("title")
                    authors = result_str.get("authors")
                    journal = result_str.get("journal")
                    volume = result_str.get("volume")
                    if title_text and authors and journal and volume:
                        result_str["url"] = url
                        return result_str
                    else:
                        return None
                return None
        else:
            return None
    except json.JSONDecodeError:
        return []
    except Exception as e:
        logger.error(f"is_valid_reference函数报错: {str(e)}")

async def process_search_query(
    title: str,
    query: str,
    research_id: str,
    api_key: str = "",
    api_url: str = "",
    model: str = "",
    limit: int = settings.SEARCH_RESULTS_LIMIT,
    is_summary_literature = False,
    search_method_flag: Optional[str] = None,
    search_engine_site_url: Optional[str] = None
) -> List[str]:
    """
    处理单个搜索查询，搜索并处理结果
    
    Args:
        query: 搜索查询
        limit: 结果数量限制
    
    Returns:
        收集的上下文列表
    """
    logger.info(f"研究【{title}】: 处理搜索查询: {query}")
    logger.info(f"研究【{title}】: 模型参数: search_method_flag: {search_method_flag} api_key: {api_key} api_url: {api_url} model: {model}")
    # urls = await search_service.perform_google_search(query, limit=limit)
    urls = []
    contain_origin = True
    #选择参考文献走 指定网站检索和Pubmed检索，否者走谷歌检索
    if search_method_flag:
        # 从dictionary表中查询literature_library对应的值  
        if search_method_flag == LiteratureLibraryType.PUBMED.value:
             #获取URLS
            logger.info(f"pubmed搜索查询: {search_method_flag} , query: {query}")          
            urls = await search_service.search_pubmed(query, limit=limit)
        else:
            #获取URLS
            logger.info(f"scholar搜索查询: {search_method_flag} , query: {query}")      
            logger.info(f"scholar搜索查询: {search_method_flag} , query: {query} search_engine_site_url: {search_engine_site_url} contain_origin: {contain_origin}")      
            urls = await search_service.perform_serpapi_search(
                query=query,
                limit=limit,
                contain_origin=contain_origin,
                search_engine="google_scholar",
                site_url=search_engine_site_url
            )
    else:
        logger.info(f"谷歌搜索查询: {settings.SEARCH_ENGINE} , query: {query}")
        urls = await search_service.perform_serpapi_search(
            query=query,
            limit=limit,
            contain_origin=contain_origin,
            search_engine = settings.SEARCH_ENGINE
        )
        
    logger.info(f"研究【{title}】: 搜索返回 {len(urls)} 个结果")
    if not urls:
        logger.warning(f"研究 【{title}】: 搜索无结果: {query}")
        return []
    
    logger.info(f"研究 【{title}】: 搜索返回 {len(urls)} 个结果")
    # 收集的上下文
    contexts = []
    
    # 并发处理URL
    tasks = [process_url(
        title=title,
        url=url,
        research_id=research_id,
        search_query=query,
        api_key=api_key,
        api_url=api_url,
        model=model,
        is_summary_literature=is_summary_literature
    ) for url in urls]
    resources = await asyncio.gather(*tasks)
    
    # 收集成功提取的上下文
    for resource in resources:
        if resource:
            contexts.append(resource)
    
    logger.info(f"研究 【{title}】: 查询 '{query}' 处理完成，收集了 {len(contexts)} 个上下文")
    return contexts


async def run_research_process(research_id: str,api_key: str = "",api_url: str = "",model: str = "") -> None:
    """
    运行研究流程
    
    Args:
        research_id: 研究ID
    """
    logger.info(f"开始研究流程，研究ID: {research_id}")
    # 获取研究实例
    research = await Research.filter(id=research_id).first()
    if not research:
        logger.error(f"研究不存在: {research_id}")
        return
    
    try:
        # 更新状态为进行中
        research.status = ResearchStatus.SEARCHING
        await research.save()
        logger.info(f"研究 {research.id}: 状态更新为搜索中")
        
        # 生成初始搜索查询
        logger.info(f"研究 {research.id}: 生成初始搜索查询")
        initial_queries = await generate_search_queries(research,api_key,api_url,model)
        if not initial_queries:
            logger.error(f"研究 {research.id}: 无法生成初始搜索查询")
            research.status = ResearchStatus.FAILED
            await research.save()
            return
        
        # 更新搜索查询
        research.search_queries = initial_queries
        await research.save()
        logger.info(f"研究 {research.id}: 已保存 {len(initial_queries)} 个初始搜索查询")
        
        # 迭代研究过程
        iteration = 0
        max_iterations = settings.RESEARCH_ITERATION_LIMIT
        
        logger.info(f"研究 {research.id}: 开始迭代研究过程，最大迭代次数: {max_iterations}")
        while iteration < max_iterations:
            logger.info(f"研究 {research.id}: 开始迭代 {iteration+1}/{max_iterations}")
            # 获取当前迭代的查询
            current_queries = research.search_queries
            
            # 处理每个查询
            for query in current_queries[research.iterations:]:
                # 处理搜索查询
                await process_search_query(research=research, query=query,api_key=api_key,api_url=api_url,model=model)
                
                # 增加迭代计数
                research.iterations += 1
                await research.save()
                logger.debug(f"研究 {research.id}: 迭代计数增加到 {research.iterations}")
            
            # 获取新的搜索查询
            logger.info(f"研究 {research.id}: 分析已收集信息，准备下一轮迭代")
            research.status = ResearchStatus.ANALYZING
            await research.save()
            
            new_queries = await get_new_search_queries(
                research,
                api_key,
                api_url,model
            )
            
            # 如果不需要更多查询，退出循环
            if new_queries is None:
                logger.info(f"研究 {research.id}: 不需要更多查询，信息已足够")
                break
            
            # 更新搜索查询
            if new_queries:
                logger.info(f"研究 {research.id}: 添加 {len(new_queries)} 个新搜索查询")
                research.search_queries = research.search_queries + new_queries
                research.status = ResearchStatus.SEARCHING
                await research.save()
            else:
                # 无新查询，退出循环
                logger.info(f"研究 {research.id}: 无新查询生成，结束迭代")
                break
            
            iteration += 1
        
        # 生成最终报告
        logger.info(f"研究 {research.id}: 准备生成最终报告")
        research.status = ResearchStatus.ANALYZING
        await research.save()
        
        report = await generate_final_report(
            research,
            api_key=api_key,
            api_url=api_url,
            model=model
        )
        
        # 更新研究
        research.report_content = report
        research.status = ResearchStatus.COMPLETED
        await research.save()
        logger.info(f"研究 {research.id}: 研究完成，报告已生成")
        
    except Exception as e:
        logger.error(f"研究 {research.id}: 研究过程出错: {str(e)}")
        logger.debug(traceback.format_exc())
        
        # 更新研究状态为失败
        research.status = ResearchStatus.FAILED
        await research.save()
        logger.info(f"研究 {research.id}: 状态已更新为失败")


async def stream_research_report(research_id: str) -> AsyncGenerator[bytes, None]:
    """
    流式生成研究报告
    
    Args:
        research_id: 研究ID
    
    Yields:
        SSE格式的响应数据
    """
    logger.info(f"开始流式生成研究报告，研究ID: {research_id}")
    # 获取研究实例
    research = await Research.filter(id=research_id).first()
    if not research:
        logger.error(f"研究不存在: {research_id}")
        yield f"data: {json.dumps({'status': 'error', 'message': '研究不存在'})}\n\n".encode('utf-8')
        return
    
    # 如果报告已经生成完毕，直接返回
    if research.status == ResearchStatus.COMPLETED and research.report_content:
        logger.info(f"研究 {research.id}: 报告已完成，流式返回内容")
        # 分块返回报告
        chunk_size = 2000  # 每个块的大小
        content = research.report_content
        
        # 初始标题块
        title_json = json.dumps({'content': '# 研究报告\n\n'})
        yield f"data: {title_json}\n\n".encode('utf-8')
        
        # 分块发送
        for i in range(0, len(content), chunk_size):
            chunk = content[i:i+chunk_size]
            await asyncio.sleep(0.5)  # 模拟流式生成延迟
            chunk_json = json.dumps({'content': chunk})
            yield f"data: {chunk_json}\n\n".encode('utf-8')
            logger.debug(f"研究 {research.id}: 发送报告块，大小: {len(chunk)}")
        
        # 完成信号
        logger.info(f"研究 {research.id}: 报告内容流式传输完成")
        yield f"data: {json.dumps({'status': 'completed'})}\n\n".encode('utf-8')
        return
    
    # 如果研究正在进行中，生成流式进度报告
    if research.status in [ResearchStatus.SEARCHING, ResearchStatus.ANALYZING]:
        logger.info(f"研究 {research.id}: 研究正在进行中，返回进度报告")
        header_json = json.dumps({'content': '# 研究进行中\n\n'})
        yield f"data: {header_json}\n\n".encode('utf-8')
        
        status_json = json.dumps({'content': f'- 当前状态: {research.status}\n'})
        yield f"data: {status_json}\n\n".encode('utf-8')
        
        iterations_json = json.dumps({'content': f'- 已完成迭代: {research.iterations}\n'})
        yield f"data: {iterations_json}\n\n".encode('utf-8')
        
        contexts_json = json.dumps({'content': f'- 已收集上下文数量: {len(research.contexts)}\n'})
        yield f"data: {contexts_json}\n\n".encode('utf-8')
        
        # 展示已有的上下文摘要
        if research.contexts:
            logger.debug(f"研究 {research.id}: 返回已收集上下文摘要")
            contexts_header_json = json.dumps({'content': '\n## 已收集的部分信息\n\n'})
            yield f"data: {contexts_header_json}\n\n".encode('utf-8')
            
            for i, context in enumerate(research.contexts[:5]):  # 只展示前5个
                summary = context[:200] + "..." if len(context) > 200 else context
                summary_json = json.dumps({'content': f'### 信息 {i+1}\n\n{summary}\n\n'})
                yield f"data: {summary_json}\n\n".encode('utf-8')
        
        progress_json = json.dumps({'content': '\n研究仍在进行中，请稍后查看完整报告。'})
        yield f"data: {progress_json}\n\n".encode('utf-8')
        
        logger.info(f"研究 {research.id}: 进度报告流式传输完成")
        complete_json = json.dumps({'status': 'completed'})
        yield f"data: {complete_json}\n\n".encode('utf-8')
        return
    
    # 如果研究失败，返回错误信息
    if research.status == ResearchStatus.FAILED:
        logger.info(f"研究 {research.id}: 研究失败，返回错误信息")
        failed_header_json = json.dumps({'content': '# 研究失败\n\n'})
        yield f"data: {failed_header_json}\n\n".encode('utf-8')
        
        failed_msg_json = json.dumps({'content': '研究过程中出现错误，请重试。'})
        yield f"data: {failed_msg_json}\n\n".encode('utf-8')
        
        failed_status_json = json.dumps({'status': 'error', 'message': '研究失败'})
        yield f"data: {failed_status_json}\n\n".encode('utf-8')
        return
    
    # 如果研究尚未开始，返回等待信息
    logger.info(f"研究 {research.id}: 研究尚未开始，返回等待信息")
    waiting_header_json = json.dumps({'content': '# 研究尚未开始\n\n'})
    yield f"data: {waiting_header_json}\n\n".encode('utf-8')
    
    waiting_msg_json = json.dumps({'content': '研究尚未开始，请先启动研究流程。'})
    yield f"data: {waiting_msg_json}\n\n".encode('utf-8')
    
    waiting_complete_json = json.dumps({'status': 'completed'})
    yield f"data: {waiting_complete_json}\n\n".encode('utf-8') 




