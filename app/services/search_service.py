import functools
import re
import json
import aiohttp
import base64
import asyncio
import traceback
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
from pymed import PubMed
from app.core.config import settings
from app.core.logging import get_logger

pubmed = PubMed(tool=settings.PUBMED_TOOL_NAME, email=settings.PUBMED_EMAIL)

# 获取logger实例
logger = get_logger(__name__)

# 使用SerpAPI执行谷歌学术搜索，返回结果URL列表
async def perform_serpapi_search(
    query: str,
    limit: int = 10,
    contain_origin: bool = False,
    search_engine = settings.SEARCH_ENGINE,
    site_url: str = None
):
    """
    使用 SerpAPI 执行 Google学术 搜索，返回结果URL列表
    
    Args:
        query: 搜索查询
        limit: 返回的最大URL数量（最多取前limit个）
    
    Returns:
        搜索结果的URL列表
    """
    if site_url:
        logger.info(f"执行SerpAPI{search_engine}搜索: '{query[:100]}...'，网站: {site_url}，限制: {limit}条结果")
        # 构建带有site:限制的查询
        search_query = f"{query} site:{site_url}"
    else:
        logger.info(f"执行SerpAPI{search_engine}搜索: '{query[:100]}...'，限制: {limit}条结果")
        search_query = query

    
    if not settings.SERPAPI_API_KEY:
        logger.error("未设置 SERPAPI_API_KEY 环境变量")
        raise ValueError("未设置 SERPAPI_API_KEY 环境变量")
    
    serpapi_url = settings.SERPAPI_URL
    timeout = aiohttp.ClientTimeout(total=180) # 超时3分钟
    params = {
        "q": search_query,
        "api_key": settings.SERPAPI_API_KEY,
        "engine": search_engine,
        "num": limit
    }
    query = {
        "url": serpapi_url,
        "params": params,
        "timeout": timeout
    }

    proxy = settings.PROXY_URL
    proxy_auth_username = settings.PROXY_USERNAME
    proxy_auth_password = settings.PROXY_PASSWORD
    # 如果配置了代理服务器信息
    if proxy and proxy_auth_password and proxy_auth_username:
        # aiohttp 需要 proxy headers 来传递 Basic Auth
        credentials = f"{proxy_auth_username}:{proxy_auth_password}"
        encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
        proxy_headers = {
            "Proxy-Authorization": f"Basic {encoded_credentials}"
        }
        query["proxy"] = proxy
        query["proxy_headers"] = proxy_headers
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                **query
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"SerpAPI 请求失败，状态码: {response.status}")
                    logger.error(f"错误详情: {error_text}")
                    return []
                
                data = await response.json()
                if "organic_results" not in data:
                    logger.warning(f"SerpAPI 没有返回搜索结果: {query}")
                    return []
                
                urls = [
                    result["link"]
                    for result in data["organic_results"]
                    if "link" in result
                ] if not contain_origin else [
                    {"url": result["link"], "origin": result}
                    for result in data["organic_results"]
                    if "link" in result
                ]
                result_urls = urls[:limit]
                logger.info(f"SerpAPI {search_engine}搜索成功，获取 {len(result_urls)} 条结果")
                logger.debug(f"搜索结果URL: {result_urls}")
                return result_urls
    except Exception as e:
        logger.error(f"SerpAPI 请求出错: {str(e)}")
        logger.debug(traceback.format_exc())
        return []

# 使用 SerpAPI 执行 Google 搜索，返回结果URL列表
# async def perform_serpapi_search(query: str, limit: int = 10, site: str = "",search_engine = settings.SEARCH_ENGINE) -> List[str]:
#     """
#     使用 SerpAPI 执行 Google 搜索，返回结果URL列表
    
#     Args:
#         query: 搜索查询
#         limit: 返回的最大URL数量（最多取前limit个）
#         site: 限制搜索的网站域名 (例如: "pubmed.ncbi.nlm.nih.gov")，可选
    
#     Returns:
#         搜索结果的URL列表
#     """
#     if site:
#         logger.info(f"执行SerpAPI网站搜索: '{query[:100]}...'，网站: {site}，限制: {limit}条结果")
#         # 构建带有site:限制的查询
#         search_query = f"{query} site:{site}"
#     else:
#         logger.info(f"执行SerpAPI搜索: '{query[:100]}...'，限制: {limit}条结果")
#         search_query = query
    
#     if not settings.SERPAPI_API_KEY:
#         logger.error("未设置 SERPAPI_API_KEY 环境变量")
#         raise ValueError("未设置 SERPAPI_API_KEY 环境变量")
    
#     serpapi_url = settings.SERPAPI_URL
#     params = {
#         "q": search_query,
#         "api_key": settings.SERPAPI_API_KEY,
#         "engine": search_engine,
#         "num": limit
#     }
    
#     try:
#         async with aiohttp.ClientSession() as session:
#             async with session.get(serpapi_url, params=params) as response:
#                 if response.status != 200:
#                     error_text = await response.text()
#                     logger.error(f"SerpAPI 请求失败，状态码: {response.status}")
#                     logger.debug(f"错误详情: {error_text}")
#                     return []
                
#                 data = await response.json()
#                 if "organic_results" not in data:
#                     logger.warning(f"SerpAPI 没有返回搜索结果: {query}")
#                     return []
                
#                 urls = [
#                     result["link"]
#                     for result in data["organic_results"]
#                     if "link" in result
#                 ]
#                 result_urls = urls[:limit]
#                 logger.info(f"SerpAPI 搜索成功，获取 {len(result_urls)} 条结果")
#                 logger.debug(f"搜索结果URL: {result_urls}")
#                 return result_urls
#     except Exception as e:
#         logger.error(f"SerpAPI 请求出错: {str(e)}")
#         logger.debug(traceback.format_exc())
#         return []



# 使用Jina进行网页内容提取
async def fetch_webpage_text_async(url: str) -> Optional[str]:
    """
    Asynchronously retrieve the text content of a webpage using Jina.
    The URL is appended to the Jina endpoint.
    """
    if not settings.JINA_API_KEY:
        logger.error("错误: 未设置JINA API密钥，请在.env文件中配置")
        return ""
        
    full_url = f"{settings.JINA_BASE_URL}{url}"
    headers = {
        "Authorization": f"Bearer {settings.JINA_API_KEY}",
        "X-Return-Format": "text",
        "X-Engine": "browser"
    }
    try:
        async with aiohttp.ClientSession() as client:
            async with client.get(full_url, headers=headers) as resp:
                if resp.status == 200:
                    return await resp.text()
                else:
                    text = await resp.text()
                    logger.error(f"Jina fetch error for {url}: {resp.status} - {text}")
                    return ""
    except Exception as e:
        logger.error(f"Error fetching webpage text with Jina: {e}")
        return ""

async def fetch_webpage_text(url: str) -> Optional[str]:
    """
    获取网页内容并提取文本
    
    Args:
        url: 网页URL
    
    Returns:
        提取的文本内容，如果失败则返回None
    """
    logger.info(f"开始获取网页内容: {url}")
    try:
        # 添加超时配置
        timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
        
        # 设置请求头，模拟浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        logger.debug(f"发送网页请求: {url}，超时设置: {timeout.total}秒")
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=timeout, allow_redirects=True) as response:
                if response.status != 200:
                    logger.warning(f"获取网页失败: {url}, 状态码: {response.status}, 错误: {response.reason}")
                    return None
                
                # 检查内容类型
                content_type = response.headers.get('Content-Type', '').lower()
                if 'text/html' not in content_type and 'application/xhtml+xml' not in content_type:
                    logger.warning(f"不支持的内容类型: {content_type}, URL: {url}")
                    return None
                
                # 获取原始HTML
                logger.debug(f"获取HTML内容: {url}")
                html = await response.text(errors='replace')
                
                # 简单提取文本内容
                # 去除HTML标签
                logger.debug(f"处理HTML内容: {url}")
                text = re.sub(r'<style.*?>.*?</style>', ' ', html, flags=re.DOTALL)
                text = re.sub(r'<script.*?>.*?</script>', ' ', text, flags=re.DOTALL)
                text = re.sub(r'<[^>]+>', ' ', text)
                
                # 清理空白字符
                text = re.sub(r'\s+', ' ', text).strip()
                
                # 简单的文本规范化
                text = text.replace('&nbsp;', ' ').replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
                
                # 截断过长的文本
                max_length = 100000  # 限制文本长度
                if len(text) > max_length:
                    logger.info(f"网页内容过长，已截断: {url}，原始长度: {len(text)}")
                    text = text[:max_length] + "..."
                
                logger.info(f"成功获取网页内容: {url}，内容长度: {len(text)}")
                return text
    except aiohttp.ClientError as e:
        logger.error(f"HTTP请求错误: {url}, 错误: {str(e)}")
        return None
    except asyncio.TimeoutError:
        logger.error(f"请求超时: {url}")
        return None
    except UnicodeDecodeError as e:
        logger.error(f"解码错误: {url}, 错误: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取网页时出现未知错误: {url}, 错误: {str(e)}")
        logger.debug(traceback.format_exc())
        return None 
    
async def search_pubmed(
    query: str, 
    limit: int = 10, 
    sort: str = "pub_date", 
    min_date: Optional[str] = None, 
    max_date: Optional[str] = None,
    article_type: Optional[str] = None,
    contain_origin: bool = False
)  :
    """
    使用PyMed库执行医学文献搜索，只返回URL列表
    
    Args:
        query: 搜索关键词
        limit: 返回结果数量限制
        sort: 排序方式 (relevance, pub_date)
        min_date: 最早日期 (格式: YYYY/MM/DD 或 YYYY)
        max_date: 最晚日期 (格式: YYYY/MM/DD 或 YYYY)
        article_type: 文章类型 (例如: "review", "clinical_trial")
        contain_origin: 是否包含原始文章数据
        
    Returns:
        当contain_origin=False时返回PubMed文章URL列表
        当contain_origin=True时返回包含URL和原始文章数据的字典列表
    """
    result_list = []
    
    logger.info(f"执行文献搜索: '{query[:100]}...'，限制: {limit}条结果")
    
    # 构建搜索查询
    search_query = query
    
    # 添加日期范围
    if min_date:
        search_query += f" AND {min_date}[PDAT]"
    if max_date:
        search_query += f" AND {max_date}[PDAT]"
    
    # 添加文章类型
    if article_type:
        search_query += f" AND {article_type}[PTYP]"
    
    try:
        logger.info(f"开始PyMed查询: {search_query}")
        # 使用线程池执行同步PubMed搜索
        loop = asyncio.get_event_loop()
        search_results = await loop.run_in_executor(
            None, 
            functools.partial(pubmed.query, search_query, max_results=limit)
        )
        
        if search_results is None:
            logger.warning("PyMed搜索返回None")
            return []
            
        # 将generator转换为列表
        search_list = list(search_results)
        logger.info(f"搜索结果转换为列表后的长度: {len(search_list)}")
        
        for article in search_list:
            try:
                # 获取文章ID
                article_id = article.pubmed_id.strip() if hasattr(article, 'pubmed_id') and article.pubmed_id else None
                if not article_id:
                    continue
                    
                # 处理多个ID的情况
                if '\n' in article_id:
                    article_id = article_id.split('\n')[0].strip()
                
                # 处理PMID前缀
                if article_id.startswith("PMID:"):
                    article_id = article_id.replace("PMID:", "").strip()
                
                # 生成URL
                url = f"https://pubmed.ncbi.nlm.nih.gov/{article_id}/"
                
                if contain_origin:
                    # 包含原始文章数据
                    result_list.append({"url": url, "origin": article.toDict()})
                else:
                    # 只包含URL
                    result_list.append(url)
                
            except Exception as e:
                logger.error(f"处理文章URL时出错: {str(e)}")
                continue
                
        logger.info(f"文献搜索完成，成功获取 {len(result_list)} 个结果")
        
    except Exception as e:
        logger.error(f"文献搜索请求出错: {str(e)}")
        logger.debug(traceback.format_exc())
    
    return result_list
