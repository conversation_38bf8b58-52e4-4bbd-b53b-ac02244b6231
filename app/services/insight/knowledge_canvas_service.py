from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import os
import aiofiles
from pathlib import Path
from tortoise.expressions import Q
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.api.schemas.insight.knowledge_canvas import (
    CanvasSourceType,
    CanvasType,
    KnowledgeCanvasResponse,
    KnowledgeCanvasQueryParams,
    RelatedNote,
    AIQuestion
)
from app.core.logging import get_logger
from app.core.config import settings
import httpx
from fastapi import HTTPException, status # type: ignore
import re
from bs4 import BeautifulSoup
from app.services.llm_service import call_llm
import aiohttp # type: ignore
from app.models.model_config import ModelConfig
from app.services.insight.insight_prompts import generate_summary_prompt, generate_ai_questions_prompt, generate_tags_prompt
from app.services.llm_service import call_llm_with_format_json

logger = get_logger(__name__)


def clean_html_content(html_content: str) -> str:
    """
    清理HTML内容，提取纯文本
    
    Args:
        html_content: 包含HTML标签的内容
        
    Returns:
        清理后的纯文本内容
    """
    try:
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        # 获取纯文本
        text = soup.get_text(separator=' ', strip=True)
        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    except Exception as e:
        logger.error(f"清理HTML内容失败: {str(e)}")
        return html_content

def clean_and_truncate_html(html_content: str, max_length: int = 300) -> str:
    """
    清理HTML内容并截取纯文本
    
    Args:
        html_content: 包含HTML标签的内容
        max_length: 最大截取长度，默认300字
        
    Returns:
        清理后的纯文本内容，如果超过最大长度则截取并添加...
    """
    try:
        # 使用clean_html_content清理HTML
        clean_text = clean_html_content(html_content)
        
        # 截取文本
        if len(clean_text) > max_length:
            return clean_text[:max_length] + "..."
        return clean_text
    except Exception as e:
        logger.error(f"清理和截取HTML内容失败: {str(e)}")
        return html_content

async def generate_summary(content: str, canvas_id: int, model_config: ModelConfig) -> str:
    """
    使用LLM生成内容摘要
    
    Args:
        content: 需要总结的内容
        canvas_id: 灵感卡片ID
        model_config: 模型配置
        
    Returns:
        生成的摘要
    """
    try:
        # 使用提示词生成函数
        messages = generate_summary_prompt(content)
        if not messages:
            logger.error(f"生成摘要提示词失败，canvas_id: {canvas_id}")
            return "生成摘要失败"
        
        # 调用LLM生成摘要
        summary = await call_llm(
            messages=messages,
            flag="generate_summary",
            model=model_config.model_name,
            apiKey=model_config.api_key,
            apiUrl=model_config.api_url
        )
        
        if not summary:
            logger.error(f"生成摘要失败，canvas_id: {canvas_id}")
            return "生成摘要失败"
            
        return summary
        
    except Exception as e:
        logger.error(f"生成摘要时发生错误: {str(e)}")
        return "生成摘要时发生错误"

async def process_canvas_summary(canvas_id: int, html_content: str, model_config: ModelConfig) -> None:
    """
    异步处理灵感卡片概要
    
    Args:
        canvas_id: 灵感卡片ID
        html_content: 原始HTML内容
        model_config: 模型配置
    """
    try:
        # 清理HTML内容
        clean_content = clean_html_content(html_content)
        
        # 生成摘要
        summary = await generate_summary(clean_content, canvas_id, model_config)
        
        # 更新灵感卡片
        canvas = await KnowledgeCanvas.get(id=canvas_id)
        if canvas:
            canvas.summary = summary
            await canvas.save()
            logger.info(f"成功更新灵感卡片摘要，canvas_id: {canvas_id}")
        else:
            logger.error(f"未找到灵感卡片，canvas_id: {canvas_id}")
            
    except Exception as e:
        logger.error(f"处理灵感卡片摘要时发生错误: {str(e)}")

async def download_and_save_image(image_url: str, user_id: int, canvas_id: int) -> str:
    """
    下载并保存图片到本地
    
    Args:
        image_url: 图片URL
        user_id: 用户ID
        canvas_id: 灵感卡片ID
        
    Returns:
        str: 保存后的本地图片路径
    """
    try:
        # 构建完整的图片URL
        full_image_url = f"{image_url}"
        # 构建保存路径，使用正斜杠
        save_dir = Path("images") / str(canvas_id)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 从URL中获取文件名
        file_name = image_url.split('/')[-1]
        save_path = save_dir / file_name
        
        # 下载图片，允许重定向
        async with httpx.AsyncClient(follow_redirects=True) as client:
            try:
                response = await client.get(full_image_url)
                response.raise_for_status()  # 检查响应状态
                
                # 保存图片
                async with aiofiles.open(save_path, 'wb') as f:
                    await f.write(response.content)
                
                # 返回相对路径，确保使用正斜杠
                return str(save_path).replace('\\', '/')
                
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP错误: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"下载图片失败: HTTP {e.response.status_code}"
                )
            except httpx.RequestError as e:
                logger.error(f"请求错误: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"下载图片失败: {str(e)}"
                )
        
    except Exception as e:
        logger.error(f"下载保存图片失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载保存图片失败: {str(e)}"
        )

async def generate_ai_questions(content: str, api_key: str = "", api_url: str = "", model: str = "") -> str:
    """
    使用LLM生成AI提问
    
    Args:
        content: 需要生成提问的内容
        api_key: API密钥
        api_url: API接口地址
        model: 模型名称
        
    Returns:
        生成的AI提问JSON字符串
    """
    try:
        logger.info(f"开始执行：generate_ai_questions:")
        # 使用提示词生成函数
        messages = generate_ai_questions_prompt(content)
        if not messages:
            logger.error("生成AI提问提示词失败")
            return "生成AI提问失败"
            
        logger.info(f"messages: {messages}")
        
        # 定义固定的 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "question": {
                            "type": "string",
                            "description": "生成的问题"
                        },
                        "answer": {
                            "type": "string",
                            "description": "问题的答案"
                        }
                    },
                    "required": ["question", "answer"],
                    "additionalProperties": False
                },
                "strict": True
            }
        }
        
        # 调用通用方法
        content = await call_llm_with_format_json(
            messages=messages,
            api_key=api_key,
            api_url=api_url,
            model=model,
            response_format=response_format,
            timeout=300
        )
        
        if not content:
            logger.error("生成AI提问失败")
            return ""
            
        try:
            # 验证是否为有效的 JSON
            json.loads(content)
            return content
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失败: {str(e)}, content: {content}")
            return ""
                
    except Exception as e:
        logger.error(f"生成AI提问时发生错误: {str(e)}")
        return "生成AI提问时发生错误"

async def generate_tags(name: str, api_key: str = "", api_url: str = "", model: str = "") -> List[str]:
    """
    使用LLM根据名称生成标签
    
    Args:
        name: 灵感卡片名称
        api_key: API密钥
        api_url: API接口地址
        model: 模型名称
        
    Returns:
        生成的标签列表
    """
    try:
        logger.info(f"开始生成标签，名称: {name}")
        
        # 使用提示词生成函数
        messages = generate_tags_prompt(name)
        if not messages:
            logger.error(f"生成标签提示词失败，name: {name}")
            return []
            
        logger.info(f"生成的提示词: {json.dumps(messages, ensure_ascii=False)}")
        
        # 定义固定的 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "type": "object",
                "properties": {
                    "tags": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "生成的标签列表"
                    }
                },
                "required": ["tags"],
                "additionalProperties": False,
                "strict": True
            }
        }
        
        # 调用通用方法
        content = await call_llm_with_format_json(
            messages=messages,
            api_key=api_key,
            api_url=api_url,
            model=model,
            response_format=response_format,
            timeout=300
        )
        
        if not content:
            return []
            
        try:
            # 解析JSON
            data = json.loads(content)
            if isinstance(data, dict) and 'tags' in data:
                tags = data['tags']
                if isinstance(tags, list):
                    # 确保只返回前3个标签
                    result_tags = [tag.strip() for tag in tags[:3] if tag.strip()]
                    logger.info(f"生成的标签: {result_tags}")
                    return result_tags
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}, content: {content}")
            return []
                
        return []
                
    except Exception as e:
        logger.error(f"生成标签时发生错误: {str(e)}")
        return [] 