from typing import List, Dict, Any, Optional
import json
from fastapi import HTTPException, status
from app.core.logging import get_logger
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.models.insight.inspirations import Inspiration
from app.models.insight.inspiration_tag import InspirationTag
from app.models.model_config import ModelConfig
from app.models.user import User
from app.services.insight.insight_prompts import generate_inspiration_prompt
from app.services.llm_service import call_llm, call_llm_with_format_json
from tortoise.expressions import Q
import asyncio
import aiohttp
import uuid
from app.services.insight.knowledge_canvas_service import clean_html_content
from app.api.schemas.insight.inspiration import InspirationDetail, InspirationSource, InspirationTag as InspirationTagSchema

logger = get_logger(__name__)

# 定义 JSON Schema
INSPIRATION_SCHEMA = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "name": {
                "type": "string",
                "description": "研究方向的名称"
            },
            "source": {
                "type": "array",
                "description": "灵感来源的信息",
                "items": {
                    "type": "object",
                    "properties": {
                        "source_id": {
                            "type": "string",
                            "description": "来源 输入 灵感ID"
                        },
                        "source_type": {
                            "type": "string",
                            "description": "来源类型，（NOTE、KNOWLEDGE、LITERATURE、INSPIRATION） 泛知识、知识库、文献库、灵感， 必须选择其中一个",
                            "enum": [ "NOTE", "KNOWLEDGE", "LITERATURE", "INSPIRATION"]
                        },
                        "source_name": {
                            "type": "string",
                            "description": "来源 输入：标题"
                        }
                    },
                    "required": ["source_id", "source_type", "source_name"]
                }
            },
            "tags": {
                "type": "array",
                "description": "研究方向的标签",
                "items": {
                    "type": "string"
                }
            },
            "inspiration": {
                "type": "string",
                "description": "研究方向的详细描述"
            }
        },
        "required": ["name", "source", "tags", "inspiration"],
        "additionalProperties": False
    }
}

async def generate_inspiration_direct(content: str, api_key: str = "", api_url: str = "", model: str = "") -> str:
    """
    直接调用LLM生成灵感
    
    Args:
        content: 需要生成灵感的内容
        api_key: API密钥
        api_url: API接口地址
        model: 模型名称
        
    Returns:
        生成的灵感文本
    """
    try:
        logger.info("开始执行：generate_inspiration_direct")
        # 使用提示词生成函数
        messages = generate_inspiration_prompt(content=content)
        if not messages:
            logger.error("生成灵感提示词失败")
            return "生成灵感失败"
            
        logger.info(f"messages: {messages}")
        
        # 定义 response_format
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "inspiration_result",
                "strict": True,
                "schema": INSPIRATION_SCHEMA
            }
        }
        
        # 调用通用方法
        content = await call_llm_with_format_json(
            messages=messages,
            api_key=api_key,
            api_url=api_url,
            model=model,
            response_format=response_format,
            timeout=3600  # 60分钟超时
        )
        
        if not content:
            logger.error("生成灵感失败")
            return ""
            
        try:
            # 验证是否为有效的 JSON
            json.loads(content)
            return content
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失败: {str(e)}, content: {content}")
            return content
    except Exception as e:
        logger.error(f"生成灵感时发生错误: {str(e)}")
        return "生成灵感时发生错误"

async def generate_inspiration(model_config: ModelConfig,user_id:uuid.UUID) -> Dict[str, Any]:
    """
    生成灵感
    
    Args:
        model_config: 模型配置
        
    Returns:
        Dict[str, Any]: 生成的灵感结果
    """
    try:
        logger.info("开始生成灵感")
        
        # 查询知识卡片数据
        knowledge_list = []
        try:
            # 先查询所有未删除的知识卡片
            knowledge_canvases = await KnowledgeCanvas.filter(
                user_id=user_id,
                is_deleted=False
            ).order_by("-updated_at").limit(10).prefetch_related("tags")
            
            # 处理查询到的知识卡片数据
            for knowledge in knowledge_canvases:
                # 获取标签
                tags = []
                for tag in knowledge.tags:
                    if not tag.is_deleted:
                        tags.append(tag.name)
                
                # 构建知识卡片数据
                original_article = knowledge.original_article
                # 如果是NOTE类型，清理HTML内容
                if knowledge.source_type == "NOTE":
                    original_article = clean_html_content(original_article) if original_article else ""
                
                knowledge_data = {
                    "name": knowledge.name,
                    "summary": knowledge.summary,
                    "tags": tags,
                    "source": json.dumps({
                        "source_type": knowledge.source_type,
                        "source_name": knowledge.name,
                        "source_id": str(knowledge.id)
                    }),
                    "original_article": original_article
                }
                
                knowledge_list.append(knowledge_data)
                
        except Exception as e:
            logger.error(f"查询知识卡片失败，错误: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"查询知识卡片失败: {str(e)}"
            )
        
        if not knowledge_list:
            logger.warning("未找到有效的知识卡片")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到有效的知识卡片"
            )
        
        logger.info(f"成功查询到{len(knowledge_list)}个知识卡片")
        
        # 构建内容
        content = ""
        for index, knowledge in enumerate(knowledge_list):
            tags_str = ", ".join(knowledge.get("tags", []))
            content += f"标题：{knowledge.get('name', '')}\n"
            content += f"来源：{knowledge.get('source', '')}\n"
            content += f"标签：{tags_str}\n"
            content += f"原文：{knowledge.get('original_article', '')}\n\n"
            if index < len(knowledge_list) - 1:
                content += "---\n"
        
        # 直接调用API生成灵感
        inspiration = await generate_inspiration_direct(
            content=content,
            api_key=model_config.api_key,
            api_url=model_config.api_url,
            model=model_config.model_name
        )
        
        if not inspiration:
            logger.error("生成灵感失败")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="生成灵感失败"
            )
        
        # 返回结果
        return {
            "inspiration": inspiration
        }
        
    except HTTPException as e:
        logger.error(f"生成灵感失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"生成灵感时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成灵感时发生错误: {str(e)}"
        )

async def save_inspiration_to_library(
    name: str,
    content: str,
    user_id: uuid.UUID,
    source: Optional[List[InspirationSource]] = None,
    tags: Optional[List[str]] = None,
    summary: Optional[str] = None
) -> bool:
    """
    保存灵感到灵感库
    
    Args:
        name: 灵感名称
        content: 灵感内容
        user_id: 用户ID
        source: 灵感来源（可选）
        tags: 灵感标签（可选）
        summary: 灵感综述（可选）
        
    Returns:
        bool: 保存是否成功
    """
    try:
        # 参数验证
        if not name or not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="灵感名称和内容不能为空"
            )
            
        # 获取用户
        user = await User.get(id=user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 处理来源数据 - 直接使用 source 列表，不需要手动序列化
        source_data = source if source else []
        
        # 创建灵感
        new_inspiration = await Inspiration.create(
            id=uuid.uuid4(),
            user=user,
            name=name,
            source=source_data,  # Tortoise ORM 会自动处理 JSON 序列化
            content=content,
            summary=summary
        )
        
        # 处理标签
        if tags:
            tag_objs = []
            for tag_name in tags:
                # 查找或创建标签
                tag, created = await InspirationTag.get_or_create(
                    name=tag_name,
                    user=user,
                    defaults={"user": user}
                )
                tag_objs.append(tag)
            
            # 添加标签关联
            await new_inspiration.tags.add(*tag_objs)
        
        return True
    except HTTPException as e:
        logger.error(f"保存灵感失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"保存灵感时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存灵感时发生错误: {str(e)}"
        ) 