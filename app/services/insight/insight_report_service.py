"""
报告服务模块
处理报告相关的业务逻辑，包括报告流式生成功能
"""
import asyncio
from typing import Callable, Optional
from uuid import UUID


from app.api.repository.user_default_model import get_user_model
from app.api.schemas.user import UserResponse
from app.models.insight.hi_insight_report import HiInsightReport, InsightReportStatus, InsightReportType
from app.models.insight.inspirations import Inspiration
from app.models.insight.knowledge_canvas import KnowledgeCanvas
from app.models.insight.inspirations_canvas_report_relation import InspirationsCanvasReportRelation, InspirationSourceType
from app.models.organization_model_use import UseCase
from app.services.llm_token_service import get_generation_result
from app.utils.constants import ProductType
from app.utils.llm_service import stream_llm_and_save
from app.core.logging import get_logger
from app.utils.utils import send_data, ContentStatus, save_text_to_file, sanitize_filename, handle_before_save, remove_markdown_h1_and_text
from app.utils.content_manager import ContentManager
from app.utils.enum import CallLLMFlag
from app.services.insight.insight_report_prompts import insight_generate_report_prompt
# 准备文件路径
from datetime import datetime
logger = get_logger(__name__)


async def get_content_by_id_and_type(content_id: UUID, content_type: str):
    """
    根据内容ID和类型获取内容对象
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (CANVAS 或 INSPIRATION)
    
    Returns:
        内容对象或None
    """
    try:
        if content_type == "CANVAS":
            return await KnowledgeCanvas.filter(
                id=content_id,
                is_deleted=False
            ).first()
        elif content_type == "INSPIRATION":
            return await Inspiration.filter(
                id=content_id,
                is_deleted=False
            ).first()
        else:
            logger.error(f"不支持的内容类型: {content_type}")
            return None
    except Exception as e:
        logger.error(f"获取内容失败: {str(e)}")
        return None


def get_content_for_report(content_obj, content_type: str) -> tuple[str, str]:
    """
    获取用于生成报告的内容
    
    Args:
        content_obj: 内容对象
        content_type: 内容类型
    
    Returns:
        (名称, 内容) 元组
    """
    if content_type == "INSPIRATION":
        name = content_obj.name or "未命名灵感库"
        # 对于灵感库，使用综述(summary)或内容(content)
        content = content_obj.summary or content_obj.content or ""
    elif content_type == "CANVAS":
        name = content_obj.name or "未命名知识卡片"
        # 对于知识卡片，使用原始文章或截取内容
        content = content_obj.original_article or content_obj.original_article_truncated or ""
    else:
        name = "未知内容"
        content = ""
    
    return name, content


async def create_report_relation(content_id: UUID, content_type: str, hi_insight_report: HiInsightReport):
    """
    创建灵感库/知识卡片与insight报告的多对多关联关系
    
    Args:
        content_id: 内容ID（灵感库ID或知识卡片ID）
        content_type: 内容类型（"CANVAS" 或 "INSPIRATION"）
        hi_insight_report: insight报告对象
    
    Returns:
        bool: 创建是否成功
    """
    try:
       
        
        # 检查是否已存在关联关系（避免重复创建）
        existing_relation = await InspirationsCanvasReportRelation.filter(
            inspiration_id=content_id,
            inspiration_type=content_type,
            report=hi_insight_report,
            is_deleted=False
        ).first()
        
        if not existing_relation:
            # 创建新的多对多关联关系记录
            relation = await InspirationsCanvasReportRelation.create(
                inspiration_id=content_id,
                inspiration_type=content_type,
                report=hi_insight_report
            )
            logger.info(f"成功创建多对多关联关系: {content_type.value}({content_id}) -> Report({hi_insight_report.id})")
            return True
        else:
            logger.info(f"关联关系已存在，跳过创建: {content_type.value}({content_id}) -> Report({hi_insight_report.id})")
            return True
            
    except Exception as e:
        logger.error(f"创建多对多关联关系失败: {str(e)}")
        return False


async def insight_stream_research_report_content(
    current_user: UserResponse,
    hi_insight_report: HiInsightReport,
    content: str,
    model_config,
    callback: Optional[Callable[[str], None]] = None,
    complete_callback: Optional[Callable[[str], None]] = None,
    error_callback: Optional[Callable[[str], None]] = None
):
    """
    流式生成报告内容
    
    Args:
        current_user: 当前用户
        hi_insight_report: 报告对象
        messages: 生成的提示词消息
        model_config: 模型配置
        callback: 流式内容回调
        complete_callback: 完成回调
        error_callback: 错误回调
    """
    
    try:
        # 导入必要的模块
        from app.models.research import Research, ResearchStatus
        from app.api.repository.project_report import search_to_carry_info
        
        
        # 为报告创建一个临时的研究对象，用于信息收集
        research = await Research.create(
            query=hi_insight_report.title,
            search_queries=[],
            contexts=[],
            status=ResearchStatus.PENDING
        )
        
        # 如果报告对象还没有关联research，就设置关联
        if not hi_insight_report.research:
            hi_insight_report.research = research
            await hi_insight_report.save()
        
        logger.info(f"为报告 {hi_insight_report.id} 创建研究对象 {research.id}")
        

        # 构造一个简化的配置对象，模拟ProjectConfigResponse2
        class SimpleConfig:
            def __init__(self):
                self.id = hi_insight_report.id
                self.name = hi_insight_report.title
                self.model = model_config
                self.literature_library = "google_scholar"  # 默认使用谷歌学术搜索
        
        config_response = SimpleConfig()
        
        # 在生成报告前，先收集相关的背景信息
        logger.info(f"开始为报告收集背景信息: {hi_insight_report.title}")
        
        # 调用 search_to_carry_info 收集背景信息
        try:
            await search_to_carry_info(
                research=research,
                config_response=config_response,
                open_literature_summary=False  # 报告暂不开启文献总结
            )
            logger.info(f"背景信息收集完成，共收集到 {len(research.contexts)} 条上下文")
        except Exception as e:
            logger.warning(f"背景信息收集失败: {str(e)}，将继续生成报告")
        
         # 生成报告提示词
        messages = insight_generate_report_prompt(
            name=hi_insight_report.title, 
            report_type=hi_insight_report.report_type,
            content=content, 
            research_context=research.contexts
        )
        logger.info(f"生成报告提示词: {messages}")
        if not messages:
            hi_insight_report.status = InsightReportStatus.FAILED
            await hi_insight_report.save()
            return send_data(False, None, "生成报告提示词失败")
        
        
        
    
    except Exception as e:
        logger.error(f"收集背景信息时出错: {str(e)}，将使用原始提示词生成报告")
    
    # 使用增强后的提示词调用LLM生成报告
    await stream_llm_and_save(
        messages=messages,
        model=model_config.model_name,
        apiKey=model_config.api_key,
        apiUrl=model_config.api_url,
        user=current_user,
        flag=CallLLMFlag.GENERATE_INSIGHT_REPORT.value,  # 使用报告专用标识
        callback=callback,
        complete_callback=complete_callback,
        error_callback=error_callback,
        related_id=str(hi_insight_report.id),
        product_type=ProductType.INSIGHTPLUS.value
    )

async def check_existing_report(
    content_id: UUID,
    content_type: str,
    report_type: str,
    current_user: UserResponse
):
    """
    检查是否已存在相同条件的报告
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (CANVAS 或 INSPIRATION)  
        report_type: 报告类型
        current_user: 当前用户
    
    Returns:
        如果存在已完成的报告，返回报告数据；否则返回None
    """
    try:
        logger.info(f"检查是否存在重复报告: content_id={content_id}, content_type={content_type}, report_type={report_type}")
        
        # 验证报告类型
        try:
            report_type_enum = InsightReportType(report_type)
        except ValueError:
            logger.warning(f"无效的报告类型: {report_type}")
            return None
        
        # 通过关联关系表查询是否已存在相同条件的报告
        relations = await InspirationsCanvasReportRelation.filter(
            inspiration_id=content_id,
            inspiration_type=content_type,
            is_deleted=False
        ).prefetch_related('report').all()
        
        # 在关联的报告中寻找匹配的报告类型和状态
        for relation in relations:
            report = relation.report
            if (report and 
                report.user_id == current_user.id and
                report.report_type == report_type_enum and
                not report.is_deleted):
                
                logger.info(f"找到已存在的报告: report_id={report.id}, 状态={report.status}")
                
                # 读取报告内容
                report_content = ""
                if report.ai_generated_report:
                    try:
                        import os
                        if os.path.exists(report.ai_generated_report):
                            with open(report.ai_generated_report, 'r', encoding='utf-8') as f:
                                report_content = f.read()
                            logger.info(f"成功读取已存在报告文件: {report.ai_generated_report}")
                        else:
                            logger.warning(f"已存在报告文件不存在: {report.ai_generated_report}")
                            report_content = "报告文件不存在"
                    except Exception as e:
                        logger.error(f"读取已存在报告文件失败: {str(e)}")
                        report_content = f"读取报告文件失败: {str(e)}"
                
                # 构建返回数据
                response_data = {
                    "report_id": report.id,
                    "title": report.title,
                    "content": report_content,
                    "content_id": content_id,
                    "content_type": content_type,
                    "report_type": report_type,
                    "status": "completed",
                    "generation_info": {
                        "generation_time": report.generation_time or 0,
                        "model_name": report.model_name or "未知",
                        "word_count": len(report_content),
                        "input_tokens": report.input_tokens or 0,
                        "output_tokens": report.output_tokens or 0,
                        "total_tokens": report.total_tokens or 0
                    },
                    "created_at": report.created_at.isoformat(),
                    "file_path": report.ai_generated_report,
                    "is_existing": True  # 标识这是已存在的报告
                }
                
                return response_data
        
        logger.info(f"未找到匹配的已完成报告，将生成新报告")
        return None
        
    except Exception as e:
        logger.error(f"检查已存在报告时发生错误: {str(e)}")
        return None


async def generate_content_report_stream(
    content_id: UUID,
    content_type: str,
    report_type: str,
    current_user: UserResponse,
    content_manager: ContentManager
):
    """
    为指定内容生成完整报告（流式）
    
    参考 project_report.py 的实现模式，启动异步生成任务并立即返回任务状态。
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (CANVAS 或 INSPIRATION)
        report_type: 报告类型
        current_user: 当前用户
        content_manager: 内容管理器（用于流式处理）
    
    Returns:
        包含报告ID和任务状态的字典
    """
    try:
        logger.info(f"用户 {current_user.username} 开始流式生成报告，内容ID: {content_id}, 类型: {content_type}, 报告类型: {report_type}")
        
        # 首先检查是否已存在相同条件的报告
        existing_report = await check_existing_report(content_id, content_type, report_type, current_user)
        if existing_report:
            logger.info(f"发现已存在的报告，直接返回: report_id={existing_report['report_id']}")
            return send_data(True, existing_report, "找到已存在的报告，直接返回")
        
        # 验证报告类型
        try:
            report_type_enum = InsightReportType(report_type)
        except ValueError:
            return send_data(False, None, f"不支持的报告类型: {report_type}")
        
        # 获取内容对象
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            content_name = "知识卡片" if content_type == "CANVAS" else "灵感库"
            return send_data(False, None, f"未找到对应的{content_name}")
        
        # 权限检查 - 确保用户只能访问自己的内容
        if content_obj.user_id != current_user.id:
            return send_data(False, None, "无权限访问此内容")
        
        # 获取用于生成报告的内容
        name, content = get_content_for_report(content_obj, content_type)
        
        # 获取用户的模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 记录开始时间（用于计算生成耗时）
        start_time = datetime.now()
        
        # 创建报告记录
        hi_insight_report = await HiInsightReport.create(
            title=name,
            report_type=report_type_enum,
            user_id=current_user.id,
            model_name=model_config.model_name,
            model_config_id=model_config.id,
            organization_id=current_user.organization.id if current_user.organization else None,
            status=InsightReportStatus.PROCESSING  # 设置为处理中状态
        )
        
        logger.info(f"创建报告记录: {hi_insight_report.id}，开始时间: {start_time}")
        
        # 立即更新源内容的报告ID，建立双向关联
        try:
            if content_type == "CANVAS":
                content_obj.insight_reports_id = hi_insight_report.id
                await content_obj.save()
                logger.info(f"已更新知识卡片 {content_obj.id} 的报告关联ID: {hi_insight_report.id}")
            elif content_type == "INSPIRATION":
                content_obj.insight_reports_id = hi_insight_report.id
                await content_obj.save()
                logger.info(f"已更新灵感库 {content_obj.id} 的报告关联ID: {hi_insight_report.id}")
        except Exception as e:
            logger.error(f"更新源内容的报告关联ID失败: {str(e)}")
            # 注意：这里不返回错误，因为关联失败不应该阻止报告生成
      
        
        report_folder = f"llm_file/insight_reports/{hi_insight_report.id}"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"insight_report_{timestamp}.txt"
        if name:
            file_name = sanitize_filename(f"insight_report_{name[:10].replace(' ', '_')}_{timestamp}.txt")
        relative_path = f"{report_folder}/{file_name}"
        
        # 创建空文件
        save_text_to_file(content="", file_path=relative_path)
        logger.info(f"创建空文件: {relative_path}")
        # 定义回调函数
        async def complete_callback(open_router_id: str, all_content: str):
            """生成完成回调"""
            try:
                # 计算生成耗时
                end_time = datetime.now()
                generation_time_s = int((end_time - start_time).total_seconds() )
                
                # 保存内容到文件，参考 project_report.py 的方式
                save_text_to_file(handle_before_save(remove_markdown_h1_and_text(all_content, name)), relative_path)
                logger.info(f"报告内容已保存到文件: {relative_path}")
                
                # 获取token统计信息
                try:
                    temp = await get_generation_result(
                        generation_id = open_router_id,
                        api_key=model_config.api_key
                    )
                    hi_insight_report.total_tokens = temp.data.tokens_completion + temp.data.tokens_prompt
                    hi_insight_report.input_tokens = temp.data.tokens_prompt
                    hi_insight_report.output_tokens = temp.data.tokens_completion
                    logger.info(f"Token统计 - 输入: {temp.data.tokens_prompt}, 输出: {temp.data.tokens_completion}, 总计: {hi_insight_report.total_tokens}")
                except Exception as e:
                    logger.error(f"获取token统计失败: {e}")
                    hi_insight_report.total_tokens = 0
                    hi_insight_report.input_tokens = 0
                    hi_insight_report.output_tokens = 0
                
                # 更新报告的生成统计信息
                hi_insight_report.model_name = model_config.model_name
                hi_insight_report.ai_generated_report = relative_path  # 存储文件路径而不是内容
                hi_insight_report.report_generation_time = end_time
                hi_insight_report.generation_time = generation_time_s
                hi_insight_report.status = InsightReportStatus.COMPLETED
                await hi_insight_report.save()
                
                logger.info(f"成功生成报告，报告ID: {hi_insight_report.id}, 文件路径: {relative_path}, 生成耗时: {generation_time_s}s")
                
                # 在报告生成成功后，创建多对多关联关系
                relation_success = await create_report_relation(content_id, content_type, hi_insight_report)
                if not relation_success:
                    logger.warning(f"多对多关联关系创建失败，但不影响报告生成成功状态")
                # 注意：关联关系创建失败不应该影响报告生成的成功状态
                
                # 清理内容管理器
                content_manager.clear_project(str(hi_insight_report.id))
                
            except Exception as e:
                logger.error(f"完成回调处理失败: {str(e)}")
                hi_insight_report.status = InsightReportStatus.FAILED
                await hi_insight_report.save()
        
        async def error_callback(error: str):
            """错误回调"""
            try:
                # 即使生成失败，也记录耗时
                end_time = datetime.now()
                generation_time_s = int((end_time - start_time).total_seconds() )
                
                logger.error(f"生成报告时发生错误: {error}，耗时: {generation_time_s}s")
                content_manager.add_content(str(hi_insight_report.id), f"生成失败: {error}", ContentStatus.ERROR)
                
                hi_insight_report.status = InsightReportStatus.FAILED
                hi_insight_report.generation_time = generation_time_s  # 记录失败时的耗时
                hi_insight_report.report_generation_time = end_time
                await hi_insight_report.save()
            except Exception as e:
                logger.error(f"错误回调处理失败: {str(e)}")
                hi_insight_report.status = InsightReportStatus.FAILED
                await hi_insight_report.save()
        
        async def stream_callback(content_chunk: str):
            """流式内容回调"""
            content_manager.add_content(str(hi_insight_report.id), content_chunk, ContentStatus.NORMAL)
        
       
        
        # 添加初始内容到管理器
        content_manager.add_content(str(hi_insight_report.id), "", ContentStatus.NORMAL)
        
        # 创建异步任务启动流式生成
        task = asyncio.create_task(
            insight_stream_research_report_content(
                current_user=current_user,
                hi_insight_report=hi_insight_report,
                content=content,
                model_config=model_config,
                callback=stream_callback,
                complete_callback=complete_callback,
                error_callback=error_callback
            )
           )
        
        # 将任务实例保存到内容管理器
        content_manager.add_asyncio(str(hi_insight_report.id), task)
        logger.info(f"将任务实例保存到内容管理器: {str(hi_insight_report.id)}")
        # 立即返回任务状态
        response_data = {
            "report_id": hi_insight_report.id,
            "status": "generating",
            "message": "报告生成任务已启动，请通过流式接口获取实时内容"
        }
        
        return send_data(True, response_data, "报告生成任务启动成功")
        
    except Exception as e:
        logger.error(f"启动流式报告生成失败: {str(e)}")
        return send_data(False, None, f"启动报告生成失败: {str(e)}") 

async def generate_content_report_sync(
    content_id: UUID,
    content_type: str,
    report_type: str,
    current_user: UserResponse
):
    """
    为指定内容生成完整报告（同步版本）
    
    与流式版本不同，该方法会等待生成完成后直接返回完整的报告内容。
    适用于需要一次性获取完整报告的场景。
    
    Args:
        content_id: 内容ID
        content_type: 内容类型 (CANVAS 或 INSPIRATION)
        report_type: 报告类型
        current_user: 当前用户
    
    Returns:
        包含完整报告内容的字典
    """
    try:
        logger.info(f"用户 {current_user.username} 开始同步生成报告，内容ID: {content_id}, 类型: {content_type}, 报告类型: {report_type}")
        
        # 首先检查是否已存在相同条件的报告
        existing_report = await check_existing_report(content_id, content_type, report_type, current_user)
        if existing_report:
            logger.info(f"发现已存在的报告，直接返回: report_id={existing_report['report_id']}")
            return send_data(True, existing_report, "找到已存在的报告，直接返回")
        
        # 验证报告类型
        try:
            report_type_enum = InsightReportType(report_type)
        except ValueError:
            return send_data(False, None, f"不支持的报告类型: {report_type}")
        
        # 获取内容对象
        content_obj = await get_content_by_id_and_type(content_id, content_type)
        if not content_obj:
            content_name = "知识卡片" if content_type == "CANVAS" else "灵感库"
            return send_data(False, None, f"未找到对应的{content_name}")
        
        # 权限检查 - 确保用户只能访问自己的内容
        if content_obj.user_id != current_user.id:
            return send_data(False, None, "无权限访问此内容")
        
        # 获取用于生成报告的内容
        name, content = get_content_for_report(content_obj, content_type)
        
        # 获取用户的模型配置
        model_config = await get_user_model(current_user, UseCase.INSIGHT_MIND_MAP.value)
        if not model_config:
            logger.warning(f"用户ID {current_user.id} 没有配置LLM模型")
            return send_data(False, None, "用户没有配置LLM模型")
        
        # 记录开始时间（用于计算生成耗时）
        start_time = datetime.now()
        
        # 创建报告记录
        hi_insight_report = await HiInsightReport.create(
            title=name,
            report_type=report_type_enum,
            user_id=current_user.id,
            model_name=model_config.model_name,
            model_config_id=model_config.id,
            organization_id=current_user.organization.id if current_user.organization else None,
            status=InsightReportStatus.PROCESSING  # 设置为处理中状态
        )
        
        logger.info(f"创建报告记录: {hi_insight_report.id}，开始时间: {start_time}")
        
        try:
            # 导入必要的模块
            from app.models.research import Research, ResearchStatus
            from app.api.repository.project_report import search_to_carry_info
            
            # # 为报告创建一个临时的研究对象，用于信息收集
            # research = await Research.create(
            #     query=hi_insight_report.title,
            #     search_queries=[],
            #     contexts=[],
            #     status=ResearchStatus.PENDING
            # )
            
            # 设置报告与研究对象的关联
            # hi_insight_report.research = research
            # await hi_insight_report.save()
            
            # logger.info(f"为报告 {hi_insight_report.id} 创建研究对象 {research.id}")
            
            # 构造一个简化的配置对象，模拟ProjectConfigResponse2
            # class SimpleConfig:
            #     def __init__(self):
            #         self.id = hi_insight_report.id
            #         self.name = hi_insight_report.title
            #         self.model = model_config
            #         self.literature_library = "google_scholar"  # 默认使用谷歌学术搜索
            
            # config_response = SimpleConfig()
            
            # 在生成报告前，先收集相关的背景信息
            #logger.info(f"开始为报告收集背景信息: {hi_insight_report.title}")
            
            # 调用 search_to_carry_info 收集背景信息
            # try:
            #     await search_to_carry_info(
            #         research=research,
            #         config_response=config_response,
            #         search_method_flag="google",  # 使用谷歌学术搜索
            #         open_literature_summary=False  # 报告暂不开启文献总结
            #     )
            #     logger.info(f"背景信息收集完成，共收集到 {len(research.contexts)} 条上下文")
            # except Exception as e:
            #     logger.warning(f"背景信息收集失败: {str(e)}，将继续生成报告")
            
            # 生成报告提示词
            messages = insight_generate_report_prompt(
                name=hi_insight_report.title, 
                report_type=hi_insight_report.report_type,
                content=content, 
                research_context=None
            )
            logger.info(f"生成报告提示词: {messages}")
            if not messages:
                hi_insight_report.status = InsightReportStatus.FAILED
                await hi_insight_report.save()
                return send_data(False, None, "生成报告提示词失败")
            
            # 使用非流式方式调用LLM生成报告
            from app.services.llm_service import call_llm
            
            logger.info("开始调用LLM生成报告（同步方式）")
            generated_content = await call_llm(
                messages=messages,
                flag=CallLLMFlag.GENERATE_INSIGHT_REPORT.value,
                model=model_config.model_name,
                stream=False,  # 非流式
                apiKey=model_config.api_key,
                apiUrl=model_config.api_url,
                user=current_user
            )
            
            if not generated_content:
                # 生成失败
                end_time = datetime.now()
                generation_time_s = int((end_time - start_time).total_seconds())
                
                hi_insight_report.status = InsightReportStatus.FAILED
                hi_insight_report.generation_time = generation_time_s
                hi_insight_report.report_generation_time = end_time
                await hi_insight_report.save()
                
                logger.error(f"LLM生成报告失败，报告ID: {hi_insight_report.id}")
                return send_data(False, None, "LLM生成报告内容为空")
            
            # 计算生成耗时
            end_time = datetime.now()
            generation_time_s = int((end_time - start_time).total_seconds())
            
            # 处理生成的内容（清理格式）
            processed_content = handle_before_save(remove_markdown_h1_and_text(generated_content, name))
            
            # 准备文件路径并保存到文件
            report_folder = f"llm_file/insight_reports/{hi_insight_report.id}"
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"insight_report_{timestamp}.txt"
            if name:
                file_name = sanitize_filename(f"insight_report_{name[:10].replace(' ', '_')}_{timestamp}.txt")
            relative_path = f"{report_folder}/{file_name}"
            
            # 保存内容到文件
            save_text_to_file(processed_content, relative_path)
            logger.info(f"报告内容已保存到文件: {relative_path}")
            
            # 更新报告的生成统计信息
            hi_insight_report.model_name = model_config.model_name
            hi_insight_report.ai_generated_report = relative_path  # 存储文件路径
            hi_insight_report.report_generation_time = end_time
            hi_insight_report.generation_time = generation_time_s  # 保存生成耗时（秒）
            hi_insight_report.status = InsightReportStatus.COMPLETED
            
            # TODO: 获取token统计信息（同步版本暂时跳过，因为非流式调用可能无法获取详细统计）
            hi_insight_report.total_tokens = 0
            hi_insight_report.input_tokens = 0
            hi_insight_report.output_tokens = 0
            
            await hi_insight_report.save()
            
            logger.info(f"成功生成报告，报告ID: {hi_insight_report.id}, 文件路径: {relative_path}, 生成耗时: {generation_time_s}s")
            
            # 在报告生成成功后，创建多对多关联关系
            relation_success = await create_report_relation(content_id, content_type, hi_insight_report)
            if not relation_success:
                logger.warning(f"多对多关联关系创建失败，但不影响报告生成成功状态")
            # 注意：关联关系创建失败不应该影响报告生成的成功状态
            
            # 返回完整的报告数据
            response_data = {
                "report_id": hi_insight_report.id,
                "title": hi_insight_report.title,
                "content": processed_content,  # 直接返回生成的内容
                "content_name": name,
                "content_id": content_id,
                "content_type": content_type,
                "report_type": report_type,
                "status": "completed",
                "generation_info": {
                    "generation_time": generation_time_s,
                    "model_name": model_config.model_name,
                    "word_count": len(processed_content),
                    "input_tokens": hi_insight_report.input_tokens,
                    "output_tokens": hi_insight_report.output_tokens,
                    "total_tokens": hi_insight_report.total_tokens
                },
                "created_at": hi_insight_report.created_at.isoformat(),
                "file_path": relative_path
            }
            
            return send_data(True, response_data, "报告生成成功")
            
        except Exception as e:
            # 生成过程中发生错误
            end_time = datetime.now()
            generation_time_s = int((end_time - start_time).total_seconds())
            
            logger.error(f"同步生成报告时发生错误: {str(e)}")
            
            hi_insight_report.status = InsightReportStatus.FAILED
            hi_insight_report.generation_time = generation_time_s
            hi_insight_report.report_generation_time = end_time
            await hi_insight_report.save()
            
            return send_data(False, None, f"生成报告时发生错误: {str(e)}")
        
    except Exception as e:
        logger.error(f"同步报告生成失败: {str(e)}")
        return send_data(False, None, f"同步报告生成失败: {str(e)}") 


async def get_insight_report_detail(
    report_id: UUID,
    current_user: UserResponse
):
    """
    获取报告详情，包括从文件中读取完整内容
    
    Args:
        report_id: 报告ID
        current_user: 当前用户
    
    Returns:
        包含报告详情和完整内容的响应数据
    """
    try:
        logger.info(f"用户 {current_user.username} 查询报告详情: report_id={report_id}")
        
        # 查询报告记录
        report = await HiInsightReport.filter(
            id=report_id, 
            user_id=current_user.id, 
            is_deleted=False
        ).first()
        
        if not report:
            logger.warning(f"报告不存在或无权限访问: report_id={report_id}, user_id={current_user.id}")
            return send_data(False, None, "报告不存在或无权限访问")
        
        # 从文件中读取报告内容
        report_content = ""
        if report.ai_generated_report and report.status == InsightReportStatus.COMPLETED:
            try:
                # 读取文件内容
                import os
                if os.path.exists(report.ai_generated_report):
                    with open(report.ai_generated_report, 'r', encoding='utf-8') as f:
                        report_content = f.read()
                    logger.info(f"成功读取报告文件: {report.ai_generated_report}, 内容长度: {len(report_content)}")
                else:
                    logger.warning(f"报告文件不存在: {report.ai_generated_report}")
                    report_content = "报告文件不存在"
            except Exception as e:
                logger.error(f"读取报告文件失败: {str(e)}")
                report_content = f"读取报告文件失败: {str(e)}"
        else:
            # 根据不同状态返回不同的提示信息
            if report.status == InsightReportStatus.PROCESSING:
                report_content = "报告正在生成中，请稍后查看"
            elif report.status == InsightReportStatus.FAILED:
                report_content = "报告生成失败"
            elif report.status == InsightReportStatus.CANCELED:
                report_content = "报告生成已取消"
            else:
                report_content = "报告内容暂不可用"
        
        # 计算字数统计
        word_count = len(report_content) if report_content else 0
        
        # 构建响应数据
        response_data = {
            "id": report.id,
            "title": report.title,
            "report_type": report.report_type,
            "status": report.status,
            "content": report_content,
            "word_count": word_count,
            "generation_info": {
                "generation_time": report.generation_time or 0,
                "model_name": report.model_name or "未知",
                "input_tokens": report.input_tokens or 0,
                "output_tokens": report.output_tokens or 0,
                "total_tokens": report.total_tokens or 0,
                "report_generation_time": report.report_generation_time.isoformat() if report.report_generation_time else None
            },
            "created_at": report.created_at.isoformat(),
            "updated_at": report.updated_at.isoformat(),
            "file_path": report.ai_generated_report
        }
        
        logger.info(f"成功获取报告详情: report_id={report_id}, 内容长度={word_count}, 状态={report.status}")
        return send_data(True, response_data, "获取报告详情成功")
        
    except Exception as e:
        logger.error(f"获取报告详情失败: {str(e)}")
        return send_data(False, None, f"获取报告详情失败: {str(e)}")


async def get_report_related_content(report_id: UUID, current_user: UserResponse):
    """
    获取与指定报告相关的灵感库和知识卡片
    
    Args:
        report_id: 报告ID
        current_user: 当前用户
    
    Returns:
        包含相关内容列表的响应数据
    """
    try:
        logger.info(f"用户 {current_user.username} 查询报告关联内容: report_id={report_id}")
        
        # 验证报告权限
        report = await HiInsightReport.filter(
            id=report_id, 
            user_id=current_user.id, 
            is_deleted=False
        ).first()
        
        if not report:
            return send_data(False, None, "报告不存在或无权限访问")
        
        # 查询所有与该报告相关的关联关系
        relations = await InspirationsCanvasReportRelation.filter(
            report=report,
            is_deleted=False
        ).all()
        
        related_content = []
        for relation in relations:
            # 获取具体的灵感对象
            content_obj = await relation.get_inspiration_object()
            if content_obj:
                related_content.append({
                    "relation_id": relation.id,
                    "content_id": relation.inspiration_id,
                    "content_type": relation.inspiration_type.value,
                    "content_name": content_obj.name if hasattr(content_obj, 'name') else "未知",
                    "created_at": relation.created_at.isoformat()
                })
        
        response_data = {
            "report_id": report_id,
            "related_content_count": len(related_content),
            "related_content": related_content
        }
        
        logger.info(f"成功获取报告关联内容: report_id={report_id}, 关联数量={len(related_content)}")
        return send_data(True, response_data, "获取报告关联内容成功")
        
    except Exception as e:
        logger.error(f"获取报告关联内容失败: {str(e)}")
        return send_data(False, None, f"获取报告关联内容失败: {str(e)}")

