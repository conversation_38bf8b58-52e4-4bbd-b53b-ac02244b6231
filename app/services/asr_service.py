import asyncio
import dashscope
from dashscope.audio.asr import *
from fastapi import WebSocket, Query, WebSocketException, status
from app.core.logging import get_logger
from typing import Annotated
from app.api.deps import auth_token_data
from app.api.repository.voice_text import update_voice_text

logger = get_logger(__name__)


# 定义 DashScope 回调
class Callback(TranslationRecognizerCallback):
    def __init__(self, websocket: WebSocket):
        self.websocket = websocket

    async def send_json(self, data):
        try:
            await self.websocket.send_json(data)
        except Exception as e:
            logger.error(f"WebSocket send error: {e}")

    def on_open(self) -> None:
        logger.info("asr服务已经连接")

    def on_close(self) -> None:
        logger.info("asr服务已经关闭")

    def on_event(
        self,
        result: RecognitionResult
    ) -> None:
        request_id = result.get_request_id()
        sentence = result.get_sentence()
        is_sentence_end = RecognitionResult.is_sentence_end(sentence)
        data = {
            "request_id": request_id,
            "is_end": is_sentence_end,
            "text": sentence.get("text")
        }

        # 转发结果给前端
        asyncio.create_task(self.send_json(data))
async def ws_auth_token(
    token: Annotated[str, Query()],
    voice_id: Annotated[str, Query()]
):
    try:
        await auth_token_data(token=token)
    except Exception as e:
        logger.error(f"ws验证token错误：{str(e)}")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="缺少有效的令牌")
    if not voice_id:
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="缺少录音记录ID")
    return token, voice_id
async def websocket_endpoint(
    websocket: WebSocket
):
    await websocket.accept()
    # 建立 DashScope 实时翻译器
    callback = Callback(websocket)
    translator = TranslationRecognizerRealtime(
        model="paraformer-realtime-8k-v2",
        format="pcm",
        sample_rate=16000,
        semantic_punctuation_enabled=True,
        callback=callback,
        punctuation_prediction_enabled=True
    )
    translator.start()

    try:
        while True:
            # 前端推送的二进制 PCM 数据
            data = await websocket.receive_bytes()
            translator.send_audio_frame(data)
    except Exception as e:
        logger.error(f"WebSocket closed:{str(e)}")
    finally:
        translator.stop()
        await websocket.close()
