# import aioredis
import asyncio
from redis.asyncio import Redis
from app.core.config import settings

# 单例 Redis 客户端
redis_client = None

def get_redis_client():
    """
    获取全局 Redis 客户端（异步连接池）
    """
    global redis_client
    if redis_client is None:
        kwargs = {"decode_responses": True}
        if settings.REDIS_PASSWORD:  # 只有非空才传
            kwargs["password"] = settings.REDIS_PASSWORD
        redis_client = Redis.from_url(
            url=settings.REDIS_URL,
            **kwargs
        )
    return redis_client

# 用于 FastAPI 生命周期事件的关闭钩子
async def close_redis_client():
    global redis_client
    if redis_client is not None:
        await redis_client.close()
        redis_client = None 