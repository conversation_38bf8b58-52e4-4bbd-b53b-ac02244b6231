import uuid
from tortoise import fields
from tortoise.models import Model
from typing import List, Dict, Any
from datetime import datetime
import json
from app.models.insight.inspiration_tag import InspirationTag


class Inspiration(Model):
    """
    灵感库模型
    """
    id = fields.UUIDField(pk=True, default=uuid.uuid4, description="灵感ID")
    name = fields.CharField(max_length=255, description="灵感名称")
    source = fields.JSONField(null=True, description="灵感来源数据")
    summary = fields.TextField(null=True, description="灵感库简介")
    original_article_truncated = fields.TextField(null=True, description="灵感库综述")
    content = fields.TextField(null=True, description="灵感内容")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    ai_expanded = fields.TextField(null=True, description="AI扩写")
    ai_analysis = fields.TextField(null=True, description="AI分析")
    ai_keynotes = fields.TextField(null=True, description="AI重点")
    ai_outline = fields.TextField(null=True, description="AI结构大纲")
    ai_probe = fields.TextField(null=True, description="AI追问")
    ai_thought_process = fields.TextField(null=True, description="AI思考过程")

    # 新增字段：关联报告ID
    insight_reports = fields.ForeignKeyField(
        "models.HiInsightReport",
        related_name="inspirations",
        null=True,
        description="关联报告ID"
    )

    tags = fields.ManyToManyField(
        "models.InspirationTag",
        related_name="inspirations",
        through="inspiration_tag_relation",
        description="标签"
    )
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="inspirations", 
        null=True, 
        description="创建用户"
    )

    class Meta:
        table = "inspirations"
        table_description = "灵感库表"

    def __str__(self):
        return self.name
        

