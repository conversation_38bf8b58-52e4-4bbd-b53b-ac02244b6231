import uuid
from tortoise import fields
from tortoise.models import Model


class KnowledgeCanvasTag(Model):
    """灵感卡片标签模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=50, description="标签名称")
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="knowledge_canvas_tags", 
        null=True, 
        description="创建用户"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    
    class Meta:
        table = "knowledge_canvas_tags"
        description = "灵感卡片标签" 