import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime
from app.models.insight.knowledge_canvas_tag import KnowledgeCanvasTag


class KnowledgeCanvas(Model):
    """灵感卡片模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=255, null=True, description="名称")
    source_type = fields.CharField(max_length=100, null=True, description="来源类型")
    type = fields.CharField(max_length=100, null=True, description="类型")
    summary = fields.TextField(null=True, description="简介")
    key_notes = fields.TextField(null=True, description="重点注释")
    related_notes = fields.TextField(null=True, description="关联笔记")
    image_url = fields.CharField(max_length=500, null=True, description="图片URL")
    original_article = fields.TextField(null=True, description="原始文章")
    original_article_truncated = fields.TextField(null=True, description="原始文章截取or灵感库综述")
    user_notes = fields.TextField(null=True, description="用户注释")
    note_update_at = fields.DatetimeField(null=True, description="笔记修改时间")
    author = fields.CharField(max_length=500, null=True, description="作者")
    reference = fields.TextField(null=True, description="引用")
    source = fields.TextField(null=True, description="来源")
    inspiration_source = fields.TextField(null=True, description="灵感来源")
    detailed_description = fields.TextField(null=True, description="详细描述")
    ai_questions = fields.TextField(null=True, description="AI提问")
    ai_expanded = fields.TextField(null=True, description="AI扩写")
    ai_analysis = fields.TextField(null=True, description="AI分析")
    ai_keynotes = fields.TextField(null=True, description="AI重点")
    ai_outline = fields.TextField(null=True, description="AI结构大纲")
    ai_probe = fields.TextField(null=True, description="AI追问")
    note_type = fields.IntField(null=True, description="笔记类型(1 富文本 2 md)")
    
    # 新增字段：关联insight报告ID
    insight_reports = fields.ForeignKeyField(
        "models.HiInsightReport",
        related_name="knowledge_canvases",
        null=True,
        description="关联insight报告ID"
    )

    user = fields.ForeignKeyField(
        "models.User", 
        related_name="knowledge_canvases", 
        null=True, 
        description="创建用户"
    )
    tags = fields.ManyToManyField(
        "models.KnowledgeCanvasTag",
        related_name="knowledge_canvases",
        through="knowledge_canvas_tag_relation",
        description="标签"
    )
    
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    
    class Meta:
        table = "knowledge_canvas"
        description = "灵感卡片" 