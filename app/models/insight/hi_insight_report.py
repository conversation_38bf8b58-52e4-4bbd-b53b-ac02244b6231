from enum import Enum
import uuid
from tortoise import fields
from tortoise.models import Model

class InsightReportType(str, Enum):
    """AI文档类型枚举"""
    OPENING_REPORT = "OPENING_REPORT"       # 开题报告
    INDUSTRY_ANALYSIS = "INDUSTRY_ANALYSIS" # 行业分析
    SUBJECT_REPORT = "SUBJECT_REPORT"       # 课题报告
    PATENT_ANALYSIS = "PATENT_ANALYSIS"     # 专利分析
    TECH_REVIEW = "TECH_REVIEW"             # 技术综述
    RESEARCH_REPORT = "RESEARCH_REPORT"     # 研究报告


class InsightReportStatus(str, Enum):
    """AI文档状态枚举"""
    PROCESSING = "PROCESSING" # 处理中
    COMPLETED = "COMPLETED"   # 已完成
    FAILED = "FAILED"         # 失败
    CANCELED = "CANCELED"     # 已取消

class HiInsightReport(Model):
    """
    HiInsightReport报告模型 - 用于存储和管理各类AI文档报告及生成数据
    """
    id = fields.UUIDField(pk=True, default=uuid.uuid4, description="报告唯一标识")
    title = fields.CharField(max_length=500, description="报告标题")
    report_type = fields.CharEnumField(InsightReportType, description="报告类型")
    status = fields.CharEnumField(InsightReportStatus, default=InsightReportStatus.PROCESSING, description="报告状态")
    
    # 关联用户和机构
    user = fields.ForeignKeyField(
        "models.User",
        related_name="insight_reports",
        description="创建用户"
    )
    organization = fields.ForeignKeyField(
        "models.Organizations",
        related_name="insight_reports",
        null=True,
        description="所属机构"
    )
    
    # 关联模型配置
    model_config = fields.ForeignKeyField(
        "models.ModelConfig",
        related_name="insight_reports",
        null=True,
        description="关联的模型配置ID"
    )
    
    # 关联研究对象
    research = fields.ForeignKeyField(
        "models.Research",
        related_name="insight_reports", 
        null=True,
        description="关联的研究对象ID"
    )
    
    # 报告内容和生成信息
    model_name = fields.CharField(max_length=200, null=True, description="使用的模型名称")
    
    # 生成统计信息
    generation_time = fields.IntField(null=True, description="生成耗时(秒)")
    input_tokens = fields.IntField(null=True, description="输入token数量")
    output_tokens = fields.IntField(null=True, description="输出token数量")
    total_tokens = fields.IntField(null=True, description="总token数量")
    report_generation_time = fields.DatetimeField(null=True, description="报告生成完成时间")
    ai_generated_report = fields.TextField(null=True, description="AI生成的报告文件路径")
    # 系统字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_historical = fields.BooleanField(default=False, description="是否为历史记录")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "hi_insight_reports"
        table_description = "insight报告表 - 存储各类分析报告及其生成数据" 