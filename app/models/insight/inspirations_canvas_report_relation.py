import uuid
from enum import Enum
from tortoise import fields
from tortoise.models import Model


class InspirationSourceType(str, Enum):
    """灵感来源类型枚举"""
    INSPIRATION = "INSPIRATION"           # 灵感库
    CANVAS = "CANVAS" # 知识卡片


class InspirationsCanvasReportRelation(Model):
    """
    灵感库、知识卡片与AI文档报告的多对多关系表
    用于建立灵感内容与报告之间的关联关系
    """
    id = fields.UUIDField(pk=True, default=uuid.uuid4, description="关系ID")
    
    # 灵感来源ID（可能是inspiration_id或knowledge_canvas_id）
    inspiration_id = fields.UUIDField(description="灵感来源ID")
    
    # 灵感来源类型（区分是灵感库还是知识卡片）
    inspiration_type = fields.CharEnumField(
        InspirationSourceType, 
        description="灵感来源类型"
    )
    
    # 关联的insight报告
    report = fields.ForeignKeyField(
        "models.HiInsightReport",
        related_name="inspiration_relations",
        description="关联的insight报告"
    )
    
    # 系统字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")

    class Meta:
        table = "inspirations_canvas_report_relation"
        table_description = "灵感库、知识卡片与AI文档报告的多对多关系表"
        unique_together = (("inspiration_id", "inspiration_type", "report"),)
        
    def __str__(self):
        return f"{self.inspiration_type}:{self.inspiration_id} -> Report:{self.report.id if self.report else 'None'}"
        
    async def get_inspiration_object(self):
        """
        根据inspiration_type获取对应的灵感对象
        返回: Inspiration或KnowledgeCanvas对象
        """
        if self.inspiration_type == InspirationSourceType.INSPIRATION:
            from app.models.insight.inspirations import Inspiration
            return await Inspiration.get_or_none(id=self.inspiration_id)
        elif self.inspiration_type == InspirationSourceType.CANVAS:
            from app.models.insight.knowledge_canvas import KnowledgeCanvas
            return await KnowledgeCanvas.get_or_none(id=self.inspiration_id)
        return None 