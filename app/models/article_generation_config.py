import uuid
from decimal import Decimal
from tortoise import fields
from tortoise.models import Model
from enum import Enum
from datetime import datetime, timezone


class ArticleGenerationConfig(Model):
    """文章生成配置模型 - 独立表，用于配置各种类型文章的生成参数"""
    
    # 主键
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    
    # 关联字段 - 添加外键关联
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="article_generation_configs", 
        description="创建用户"
    )
    organization = fields.ForeignKeyField(
        "models.Organizations", 
        related_name="article_generation_configs", 
        null=True, 
        description="所属机构"
    )
    model = fields.ForeignKeyField(
        "models.ModelConfig", 
        related_name="article_generation_configs", 
        description="选择的模型"
    )
   
        
    # 信息配置字段
    name = fields.CharField(max_length=500, description="主题（必填）")
    meeting_minutes_audio = fields.CharField(null=True, max_length=500, description="会议纪要-录音文件路径")
    target_audience = fields.CharField(null=True, max_length=50, description="智库报告-目标受众")
    analysis_method = fields.CharField(null=True, max_length=200, description="智库报告-分析方法")
    industry_type = fields.CharField(null=True, max_length=50, description="市场行业研习报告-行业类型")
    total_investment_amount = fields.DecimalField(null=True, max_digits=15, decimal_places=2, description="可研报告-总投资额")
    financial_analysis = fields.TextField(null=True, description="可研报告-财务分析")
    risk_assessment = fields.TextField(null=True, description="可研报告-风险评估")
    research_field = fields.CharField(null=True, max_length=200, description="文献综述-研究领域")
    material_subject = fields.CharField(null=True, max_length=500, description="材料主体")
    team_members = fields.TextField(null=True, description="团队成员（JSON格式存储）")
    team_introduction = fields.TextField(null=True, description="团队介绍")
    reference_library = fields.TextField(null=True, description="参考文献库")
    reference_library_urls = fields.TextField(null=True, description="参考文献库URL列表（JSON格式存储）")
    user_add_prompt = fields.TextField(null=True, description="用户添加的提示词")
    # 用户增加的大纲模板
    user_add_demo = fields.ForeignKeyField(
        'models.UploadFile',
        related_name='article_upload_file',
        description="关联文件上传表",
        null=True
    )
    word_count_requirement = fields.IntField(null=True, description="字数要求")
    language_style = fields.CharField(null=True, max_length=100, description="语言风格")
    application_category = fields.CharField(null=True, max_length=100, description="申报口径")
    requirements_attachments = fields.TextField(null=True, description="申报要求附件（JSON格式存储）")
    
    # 新增字段
    leader_id = fields.CharField(null=True, max_length=100, description="申报主体ID")
    leader_text = fields.TextField(null=True, max_length=100, description="申报主体")
    ai_leader_introduction = fields.TextField(null=True, description="项目主体的AI介绍")
    requirements_attachments_id = fields.CharField(null=True, max_length=500, description="申报要求附件ID（字符串类型）")
    team_members_id = fields.CharField(null=True, max_length=500, description="团队成员ID（字符串类型）")
    
    # 搜索引擎配置字段
    search_engine = fields.CharField(max_length=20, default="google", description="搜索引擎选择")
    search_list_count = fields.IntField(default=10, description="搜索列表数")
    search_iterations = fields.IntField(default=3, description="搜索迭代次数")
    retrieval_content_count = fields.IntField(default=20, description="检索内容数")
    
    # 文献总结配置字段
    literature_summary_enabled = fields.BooleanField(default=True, description="文献总结开关")
    max_content_collection_count = fields.IntField(null=True, default=20, description="最大内容收集数量")
    max_reference_count = fields.IntField(null=True, default=20, description="最大参考文献数")
    
    # 模型参数配置字段
    temperature = fields.DecimalField(null=True, max_digits=3, decimal_places=2, default=Decimal('0.7'), description="温度参数")
    top_p = fields.DecimalField(null=True, max_digits=3, decimal_places=2, default=Decimal('0.9'), description="Top-p参数")
    top_k = fields.IntField(null=True, default=50, description="Top-k参数")
    
    # 提示词管理字段
    outline_system_prompt = fields.TextField(null=True, description="大纲的系统提示词")
    content_system_prompt = fields.TextField(null=True, description="正文的系统提示词")
    outline_user_prompt = fields.TextField(null=True, description="大纲的用户提示词")
    content_user_prompt = fields.TextField(null=True, description="正文的用户提示词")
    
    # 生成结果文件地址字段
    generated_outline_file_path = fields.CharField(null=True, max_length=500, description="生成的大纲文件地址")
    generated_content_file_path = fields.CharField(null=True, max_length=500, description="生成的正文文件地址")
    outline_generation_time = fields.DatetimeField(null=True, description="大纲生成时间", use_tz=True)
    content_generation_time = fields.DatetimeField(null=True, description="正文生成时间", use_tz=True)
    outline_generation_status = fields.CharField(null=True, max_length=20, default="pending", description="大纲生成状态")
    content_generation_status = fields.CharField(null=True, max_length=20, default="pending", description="正文生成状态")
    
    # Token消耗统计字段
    outline_tokens_consumed = fields.IntField(null=True, default=0, description="大纲生成消耗的Token数量")
    content_tokens_consumed = fields.IntField(null=True, default=0, description="正文生成消耗的Token数量")
    
    # 系统字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    # 添加 research 字段，与 ProjectConfig 保持一致
    research = fields.ForeignKeyField(
        'models.Research',
        related_name='article_generation_configs',
        description="关联研究表",
        null=True
    )
    
    class Meta:
        table = "article_generation_configs"
        description = "文章生成配置模型 - 用于配置各种类型文章的生成参数"
    
    def __str__(self):
        return f"ArticleGenerationConfig(id={self.id}, name={self.name})" 