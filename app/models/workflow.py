import uuid
from enum import Enum
from tortoise import fields
from tortoise.models import Model
from enum import Enum


class WorkflowCategory(str, Enum):
    OUTLINE = "OUTLINE"
    REPORT = "REPORT"
    OTHER = "OTHER"


class Name(str, Enum):
    """流程名称"""
    GENERATED_OUTLINE_FIRST_TIME = "首次生成大纲"
    REVISED_OUTLINE = "重新生成大纲"
    OUTLINE_POLISHING = "大纲指定调整"
    GENERATED_CONTENT_FIRST_TIME = "首次生成正文"
    REVISED_CONTENT = "重新生成正文"
    CONTENT_POLISHING = "正文指定调整"
    ILLUSION_REVIEW = "幻觉审查"
    AI_TRACE_REMOVAL = "去AI痕迹"
class Order(int, Enum):
    """流程顺序"""
    GENERATED_OUTLINE_FIRST_TIME = 10
    REVISED_OUTLINE = 20
    OUTLINE_POLISHING = 30
    GENERATED_CONTENT_FIRST_TIME = 40
    REVISED_CONTENT = 50
    CONTENT_POLISHING = 60
    ILLUSION_REVIEW = 70
    AI_TRACE_REMOVAL = 80

class Workflow(Model):
    """工作流程模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    project_config = fields.ForeignKeyField(
        'models.ProjectConfig', related_name='workflows', description="关联项目配置ID"
    )
    name = fields.CharField(max_length=100, description="流程名称")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.IntField(default=0, description="是否删除 (0: 正常, 1: 删除)")
    # 可以是大纲或者报告的文件地址
    content = fields.CharField(max_length=100, null=True, default=None, description="流程内容")
    # 可以是幻觉审查或者去AI痕迹的内容
    content_remark = fields.CharField(max_length=100, null=True, default=None, description="流程内容的补充")
    # 可以是大纲或者报告的文件地址
    category = fields.CharEnumField(WorkflowCategory, description="流程分类：OUTLINE, REPORT, OTHER", default=WorkflowCategory.OTHER)
    # 操作人
    operator = fields.ForeignKeyField(
        'models.User', related_name='workflows', description="操作人"
    )
    # 删除时间
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    # 流程的顺序
    # 10 首次生成大纲
    # 20 重新修改大纲
    # 30 大纲润色
    # 40 首次生成正文
    # 50 重新生成正文
    # 60 正文润色
    # 70 幻觉审查
    # 80 去AI痕迹
    order = fields.IntField(default=0, description="流程的顺序")
    class Meta:
        table = "workflows"
        description = "项目工作流程"
    
    def __str__(self):
        return f"工作流程: {self.name} - 项目配置ID: {self.project_config_id}" 