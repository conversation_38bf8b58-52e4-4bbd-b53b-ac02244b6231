import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime


class ProjectModelConfig(Model):
    """项目模型配置关联表，用于关联项目配置和模型配置"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    project_config = fields.ForeignKeyField(
        "models.ProjectConfig", 
        related_name="model_configs", 
        description="关联的项目配置"
    )
    model_config = fields.ForeignKeyField(
        "models.ModelConfig", 
        related_name="project_configs", 
        description="关联的模型配置"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "project_model_configs"
        description = "项目模型配置关联表"
        unique_together = (("project_config", "model_config"),)
    
    def __str__(self):
        return f"项目模型配置: {self.project_config_id} - {self.model_config_id}" 