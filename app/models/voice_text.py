import uuid
from tortoise import fields
from tortoise.models import Model


class VoiceText(Model):
    """录音文件的文字内容转译"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    text_content = fields.TextField(null=True, description="录音文件的文字内容")
    upload_files = fields.ForeignKeyField(
        "models.UploadFile", 
        related_name="voice_file", 
        null=True, 
        description="上传的语音文件的地址"
    )
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="voice_user", 
        null=True, 
        description="创建该记录的用户"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "docgen_voice_text"
        description = "录音文件转换的文字内容"