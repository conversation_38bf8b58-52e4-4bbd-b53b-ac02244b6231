import uuid
from tortoise import fields
from tortoise.models import Model


class RequirementsAttachmentFiles(Model):
    """申报要求附件文件模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    file_path = fields.CharField(max_length=255, description="文件路径")
    file_name = fields.CharField(max_length=255, description="文件名称")
    file_content = fields.TextField(null=True, description="文件内容")
    word_count = fields.IntField(default=0, description="文件字数")
    analysis_result = fields.TextField(null=True, description="LLM分析结果")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    project_configs = fields.ForeignKeyField(
        "models.ProjectConfig",
        related_name="requirements_attachments_files",
        null=True,
        description="关联的项目配置"
    )
    
    class Meta:
        table = "requirements_attachments_files"
        description = "申报要求附件文件"
    
    def __str__(self):
        return f"{self.file_name}" 