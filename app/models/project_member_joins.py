from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class ProjectMemberJoin(Model):
    """项目成员关联模型"""
    join_id = fields.CharField(max_length=100, description="成员关联ID")
    member = fields.ForeignKeyField(
        "models.ProjectMember", 
        related_name="joins", 
        description="关联的项目成员"
    )
    user = fields.ForeignKeyField(
        "models.User",
        null=True,
        related_name="join_create_user",
        description="创建用户"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    # 软删除字段
    is_deleted = fields.IntField(default=0, description="是否删除 (0: 正常, 1: 删除)")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "project_member_joins"
        description = "项目成员关联"
    
    def __str__(self):
        return f"Join {self.join_id} - Member: {self.member_id}" 