import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class APIKey(Model):
    """API密钥模型，用于用户访问权限控制"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    key = fields.CharField(max_length=100, unique=True, description="实际的API Key (形如 sk-XXXX)")
    name = fields.CharField(max_length=100, description="Key的描述说明")
    quota = fields.IntField(default=0, description="调用次数上限，0表示无限制")
    used = fields.IntField(default=0, description="当前已使用的次数")
    is_active = fields.BooleanField(default=True, description="Key是否有效")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "api_keys"
        description = "API密钥"
    
    def __str__(self):
        return f"{self.name} ({self.key})"
    
    @property
    def is_valid(self):
        """检查API Key是否有效（激活状态且未超出配额）"""
        if not self.is_active:
            return False
        
        # 如果quota为0，表示无限制
        if self.quota == 0:
            return True
        
        return self.used < self.quota 