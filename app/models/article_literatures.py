from tortoise import fields
from tortoise.models import Model
from datetime import datetime


class ArticleLiterature(Model):
    """文章文献模型"""
    id = fields.UUIDField(pk=True, description="文献ID")
    
    # 外键关联研究表
    research = fields.ForeignKeyField(
        "models.Research",
        related_name="article_literatures",
        description="关联的研究项目"
    )

    # 文献基本信息
    title = fields.CharField(max_length=500, description="文献标题")
    authors = fields.CharField(max_length=500, description="作者，多个作者用逗号分隔")
    journal = fields.CharField(max_length=200, description="期刊名称")
    year = fields.IntField(description="发表年份")
    issue = fields.CharField(max_length=20, null=True, description="期号")
    volume = fields.CharField(max_length=20, null=True, description="卷号")
    pages = fields.CharField(max_length=50, null=True, description="页码范围，如：123-145")
    doi = fields.CharField(max_length=200, null=True, description="DOI索引")
    url = fields.CharField(max_length=200, null=True, description="文献网页链接")
    summary = fields.TextField(null=False, description="文献总结")
    
    # 时间戳字段
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否删除 (0: 正常, 1: 删除)")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "article_literatures"
        description = "文章文献"
    
    def soft_delete(self):
        """软删除方法"""
        self.is_deleted = True
        self.deleted_at = datetime.now()
        return self.save()
    
    @property
    def citation_format(self) -> str:
        """
        生成标准引用格式
        
        Returns:
            str: 标准引用格式字符串
        """
        citation_parts = []
        
        # 作者
        if self.authors:
            citation_parts.append(self.authors)
        
        # 标题
        if self.title:
            citation_parts.append(self.title)
        
        # 期刊、卷号、期号、页码、年份
        journal_info = []
        if self.journal:
            journal_info.append(self.journal)

        if self.year:
            journal_info.append(str(self.year))
        
        if self.volume and self.issue and self.pages:
            journal_info.append(f"{self.volume}({self.issue}): {self.pages}")
        elif self.volume and self.issue:
            journal_info.append(f"{self.volume}({self.issue})")
        elif self.volume and self.pages:
            journal_info.append(f"{self.volume}: {self.pages}")
        elif self.issue and self.pages:
            journal_info.append(f"({self.issue}): {self.pages}")
        
        if journal_info:
            citation_parts.append(", ".join(journal_info))
        
        return ". ".join(citation_parts) + "." 