import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class ProjectLeader(Model):
    """项目主体模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=255, description="主体名称")
    credit_code = fields.CharField(max_length=25, description="统一社会信用代码")
    institution_type = fields.CharField(max_length=20, description="机构性质")
    # province = fields.IntField(null=True, description="省ID")
    province = fields.ForeignKeyField(
        'models.Area', null=True, related_name='area_province', description="关联省市区"
    )
    city = fields.ForeignKeyField(
        'models.Area', null=True, related_name='area_city', description="关联省市区"
    )
    district = fields.ForeignKeyField(
        'models.Area', null=True, related_name='area_district', description="关联省市区"
    )
    user = fields.ForeignKeyField(
        "models.User",
        null=True,
        related_name="leader_create_user",
        description="创建用户"
    )
    # province = fields.ForeignKeyField(
    #     'models.Area', related_name='area_province', description="关联用户ID"
    # )
    # city = fields.IntField(null=True, description="市ID")
    # district = fields.IntField(null=True, description="区ID")
    address = fields.CharField(max_length=200, null=True, description="详细地址")
    website = fields.CharField(max_length=255, null=True, description="机构网站")
    ai_introduction = fields.CharField(max_length=500, null=True, description="公司的AI详细介绍")
    founded_date = fields.DateField(description="成立时间")
    # patent_count = fields.IntField(null=True, description="专利数量")
    related_projects = fields.CharField(max_length=500, description="主要业务及业绩")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    # 软删除字段
    is_deleted = fields.IntField(default=0, description="是否删除 (0: 正常, 1: 删除)")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "project_leaders"
        description = "项目主体"
    
    def __str__(self):
        return f"{self.name}" 