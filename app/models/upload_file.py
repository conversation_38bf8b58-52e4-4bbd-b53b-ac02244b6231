import uuid
from tortoise import fields
from tortoise.models import Model


class UploadFile(Model):
    """上传的文件"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    file_path = fields.CharField(max_length=255, description="文件路径")
    file_name = fields.CharField(max_length=255, description="文件名称")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    # 文件类型标识
    mime_type = fields.CharField(null=True, max_length=200, description="文件的mimetype")
    # 文件大小（Byte）
    file_size = fields.IntField(null=True, description="文件的大小(byte)")
    # 媒体文件的持续时长（毫秒）
    file_duration = fields.IntField(null=True, description="媒体文件的持续时长(毫秒)")
    
    class Meta:
        table = "upload_files"
        description = "用户上传的文件"
    
    def __str__(self):
        return f"{self.file_name}" 