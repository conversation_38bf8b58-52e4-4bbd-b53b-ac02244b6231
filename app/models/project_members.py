import uuid
from tortoise import fields
from tortoise.models import Model
from datetime import datetime, timezone


class ProjectMember(Model):
    """项目成员模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=100, description="姓名")
    title = fields.CharField(max_length=100, description="职称")
    organization = fields.CharField(max_length=255, null=True, description="所属结构")
    introduction = fields.TextField(null=True, description="简介")
    education = fields.CharField(max_length=255, null=True, description="学历")
    representative_works = fields.TextField(null=True, description="代表作")
    user = fields.ForeignKeyField(
        "models.User",
        null=True,
        related_name="project_member_create_user",
        description="创建用户"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    # 软删除字段
    is_deleted = fields.IntField(default=0, description="是否删除 (0: 正常, 1: 删除)")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "project_members"
        description = "项目成员"
    
    def __str__(self):
        return f"{self.name} ({self.title})" 