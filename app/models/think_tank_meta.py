import uuid
from tortoise import fields
from tortoise.models import Model


class ThinkTankMeta(Model):
    """智库报告的配置项"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    target = fields.CharField(null=True, max_length=50, description="目标受众")
    analysis_method = fields.CharField(null=True, max_length=50, description="分析方法（选填）")
    
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 软删除字段
    is_deleted = fields.BooleanField(default=False, description="是否已删除")
    deleted_at = fields.DatetimeField(null=True, description="删除时间")
    
    class Meta:
        table = "docgen_think_tank_meta"
        description = "智库报告的个性化配置内容"