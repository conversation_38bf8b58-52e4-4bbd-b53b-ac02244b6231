# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Running the Application
```bash
# Development server with auto-reload
python run.py

# Direct uvicorn (alternative)
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Database Management
```bash
# Initialize database migrations (first time)
aerich init-db

# Generate new migration after model changes
aerich migrate

# Apply migrations to database
aerich upgrade

# Database via Docker
docker-compose up -d  # Full stack
docker-compose -f docker-compose-localdb.yml up -d  # Database only
```

### Testing and Linting
- No specific test runner configured - check for test files before adding pytest commands
- No linting configuration found - verify tools before suggesting ruff/flake8 commands

## High-Level Architecture

### Core Application Stack
- **FastAPI**: Async web framework with automatic OpenAPI/Swagger docs
- **Tortoise ORM**: Async database ORM with PostgreSQL backend
- **OpenRouter API**: Unified interface for multiple LLM providers
- **Server-Sent Events**: Streaming responses for real-time content generation

### Multi-Tenant Permission System
Three-tier role hierarchy:
- **SUPER_ADMIN**: System-wide access to all data
- **ADMIN**: Organization-scoped access (machine admin level)
- **User**: Personal data access only

Key pattern: All data queries must implement role-based filtering using `current_user.role.identifier` checks.

### Content Generation Pipeline
The system follows a **Search → Summarize → Iterate → Search** workflow:

1. **Project Configuration** (`project_configs.py`): Define research parameters, team, models
2. **Outline Generation** (`project_report.py`): LLM generates structured research outline
3. **Content Generation** (`research_service.py`): Iterative research using web search + LLM synthesis
4. **Streaming Delivery**: Real-time content streaming via SSE to frontend

### Data Models Architecture

**User Management**:
- `User` → `Role` → `Organization` (hierarchical permissions)
- `UserReportUsage`: Quota management per user
- `ApiKey`: API authentication tokens

**Project System**:
- `ProjectConfig`: Main project definition with team, model, requirements
- `ProjectLeader`/`ProjectMember`: Team composition with user isolation
- `ProjectMemberJoin`: Many-to-many relationship management

**Content Generation**:
- `Research`: Represents a research session with status tracking
- `ModelConfig`: LLM provider configurations (OpenRouter, custom APIs)
- File storage in `llm_file/{project_id}/` directory structure

### Key Service Layers

**Research Service** (`research_service.py`):
- Orchestrates the search-summarize-iterate pipeline
- Integrates with SerpAPI for web search
- Manages literature library queries (PubMed integration)

**LLM Service** (`llm_service.py`):
- Handles streaming communication with LLM providers
- Token usage tracking and billing management
- Error handling and retry logic for API calls

**Search Service** (`search_service.py`):
- Web search abstraction (Google, Google Scholar via SerpAPI)
- Content extraction and cleaning
- Rate limiting and result filtering

### Permission System Implementation
Critical pattern for data isolation - always check user permissions:

```python
if current_user.role.identifier == InsetRole.SUPER_ADMIN:
    # Access all data
elif current_user.role.identifier == InsetRole.ADMIN:
    # Filter by organization
    if current_user.organization and current_user.organization.id:
        query = query.filter(user__organization_id=current_user.organization.id)
else:
    # Filter by user
    query = query.filter(user_id=current_user.id)
```

### Streaming Content Architecture
Uses `ContentManager` class for real-time content streaming:
- In-memory chunk management for active generation sessions
- SSE (Server-Sent Events) for frontend delivery
- Background task cleanup when sessions complete

### Database Migration Strategy
- **Aerich** for schema migrations (`migrations/` directory)
- **SQL scripts** for data fixes and initialization (`scripts/` directory)
- Pre-commit hooks available for automatic migration generation

### Configuration Management
Environment-driven configuration via `.env` file:
- Database connections, JWT secrets, API keys
- LLM provider configurations
- Quota and rate limiting settings
- Logging levels and retention policies

### Error Handling Patterns
- Comprehensive exception logging with request IDs
- User-facing error messages via `send_data()` utility
- Background task error recovery for content generation
- Database transaction rollbacks on failures

## Development Notes

### Adding New Features
1. Define data models in `app/models/`
2. Create Pydantic schemas in `app/api/schemas/`
3. Implement business logic in `app/services/`
4. Add API endpoints in `app/api/routes/`
5. Register routes in `app/api/routes/__init__.py`
6. Generate database migration: `aerich migrate`

### Permission-Aware Development
Always implement the three-tier permission pattern when creating new endpoints that access user-created data. Test with different role types to ensure proper data isolation.

### Streaming Content Development
When adding new content generation features, use the existing `ContentManager` pattern with SSE endpoints for real-time user feedback.

### Key Dependencies
- FastAPI 0.103.1+ (modern async features)
- Tortoise ORM 0.19.3+ (async database operations)
- Loguru for structured logging
- aiohttp for external API calls
- python-docx, PyPDF2 for document processing

## Environment Setup
Copy `.env.example` to `.env` and configure:
- `DATABASE_URL`: PostgreSQL connection string
- `SECRET_KEY`: JWT signing key
- `SERPAPI_API_KEY`: Web search API key
- `DEFAULT_ADMIN_USERNAME/PASSWORD`: Initial admin account