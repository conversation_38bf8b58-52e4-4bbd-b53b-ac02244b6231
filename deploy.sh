#!/bin/sh

# 检查是否提供了版本参数
if [ -z "$1" ]; then
  echo "错误: 请提供版本号"
  echo "用法: ./deploy.sh [版本号] [环境]"
  exit 1
fi

# 设置版本号
VERSION=$1
# 设置环境
ENV=$2
echo "开始启动 Hi-Researcher API 服务, 版本: $VERSION"

# 检查镜像是否存在
if [ "$(docker images -q hi-researcher-api-$ENV:$VERSION 2> /dev/null)" = "" ]; then
  echo "错误: 镜像 hi-researcher-api-$ENV:$VERSION 不存在"
  exit 1
fi

# 目前有三个环境，dev、test和正式
if [ "$ENV" = "dev" ]; then
  ENV_FILE="/home/<USER>/hi-ideagen-dev/api/.env"
  CONTAINER_NAME="hi-researcher-api-dev"
elif [ "$ENV" = "test" ]; then
  ENV_FILE="/home/<USER>/hi-ideagen-test/api/.env"
  CONTAINER_NAME="hi-researcher-api-test"
else
  ENV_FILE="/home/<USER>/hi-ideagen/api/.env"
  CONTAINER_NAME="hi-researcher-api"
fi

# 检查 .env 文件是否存在
if [ ! -f "$ENV_FILE" ]; then
  echo "错误: 环境变量文件 $ENV_FILE 不存在"
  echo "请手动创建 $ENV_FILE"
  exit 1
fi

# 加载 .env 文件中的变量（包括 STATICS_PATH）
set -a
. "$ENV_FILE"
set +a

HOST_UID=$(id -u)
HOST_GID=$(id -g)
export HOST_UID HOST_GID

# 检查并设置 STATICS_PATH 权限
if [ -z "$STATICS_PATH" ]; then
  echo "错误: .env 文件中没有定义 STATICS_PATH"
  exit 1
fi

if [ ! -d "$STATICS_PATH" ]; then
  echo "目录 $STATICS_PATH 不存在，正在创建..."
  mkdir -p "$STATICS_PATH"
fi

chmod 777 "$STATICS_PATH"
echo "已设置 $STATICS_PATH 的读写权限为 777"
# 导出变量供 docker-compose 使用
export VERSION
export CONTAINER_NAME
export PORT
export ENV_FILE
export STATICS_PATH
export ENV
export COMPOSE_PROJECT_NAME=hi-researcher-${ENV}

# 启动服务
echo "部署服务: hi-researcher-api-$ENV:$VERSION，容器名: $CONTAINER_NAME，端口: $PORT..."
docker compose down
docker compose up -d

echo "服务启动完成! 版本: $VERSION"
echo "环境变量文件位置: $ENV_FILE"