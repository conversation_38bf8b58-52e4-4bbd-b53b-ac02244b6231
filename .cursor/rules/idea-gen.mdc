---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
description: 后端项目规则 - FastAPI + PostgreSQL 架构，确保生成内容符合项目结构、部署规范与业务逻辑一致性
files: ["**/*.py", "Dockerfile", "docker-compose.yml", "requirements.txt", ".env", ".env.example", "directories.md", "README.md"]

rules:
  - 在开始工作前，你必须详细阅读 `directories.md`，理解各模块结构及文件职责边界。
  - 生成的重要代码必须**写中文注释**，说明关键逻辑的目的、输入输出、注意事项等。
  - 所有逻辑必须保持**前后自洽**，不得出现未定义变量、未处理异常、重复逻辑等问题。
  - 所有新增代码文件、模块目录，必须同步更新 `directories.md`，保持项目结构文档与实际一致。
  - 若新增模块、功能、启动方式、环境配置等，请同步更新 `README.md`，说明用途与使用方式。

  # 模块职责与限定
  - `app/api/routes/` 下的路由文件仅定义接口入口，不允许写业务逻辑，请将逻辑委托到 `services/`。
  - `app/api/schemas/` 为 Pydantic Schema 层，必须区分 `Request` / `Response`，明确字段含义与类型。
  - `app/models/` 定义 ORM 数据模型，需严格遵守数据库命名规范（见下方），并加上字段/表注释。
  - `app/services/` 是业务逻辑实现层，每个 service 文件应聚焦单一模块逻辑，函数需具备独立可测性。
  - `app/core/` 用于通用配置、安全机制、日志等工具类代码，禁止引入具体业务逻辑。
  - `app/db/` 中为数据库连接和配置，新增模型后应考虑是否需要迁移机制（如 Alembic）。

  # FastAPI 开发建议
  - 所有路由应使用 `@router.get/post/put/delete` 装饰器，写清楚 `summary` 和 `response_model`。
  - 路由参数与请求体结构应通过 schema 明确表示，禁止在路由中直接访问 `request.json()` 等底层方法。
  - 所有 API 接口返回应统一格式（可通过统一响应结构封装函数实现）。

  # Docker / DevOps 集成
  - 若生成逻辑涉及新服务（如模型服务、任务队列等），请同步修改 `Dockerfile` 和 `docker-compose.yml`。
  - 若引入新依赖包，必须同步更新 `requirements.txt`，并按字母顺序排列。
  - 若需引入新环境变量，请同时更新 `.env` 与 `.env.example`，并添加用途说明。

  # 数据库设计规范
  - 表命名应使用**复数形式**（如：`users`、`reports`），只包含小写字母和下划线，避免使用保留字。
  - 字段命名采用 `snake_case` 风格：
    - 主键统一为 `id`
    - 外键为 `{关联表名}_id`
    - 时间字段为 `created_at`、`updated_at`
    - 删除字段为 `is_deleted`（布尔）+ `deleted_at`（时间戳）
  - 所有表和字段必须使用 PostgreSQL 的 `COMMENT ON` 添加注释，说明其作用和含义。
  - 表模型应继承基础 `Base` 类，必要时通过 `__tablename__` 明确表名。

  # 风格与质量保障
  - 避免过于复杂或嵌套过深的实现逻辑，优先选择简洁、易读、可测试方案。
  - 所有函数命名必须表达其动作意图（如 `get_user_by_id`, `create_report`）。
  - 禁止将测试代码、调试代码（如 `print()`）保留在主干逻辑中。

  # 多模块依赖时
  - 若逻辑跨 `api → services → models`，应避免耦合，明确每层的输入输出接口定义。

  # 日志  
  - 所有敏感操作都应该打印详细的日志，你自己决定打印日志的级别