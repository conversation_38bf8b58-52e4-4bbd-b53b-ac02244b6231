---
description: 
globs: 
alwaysApply: true
---
## 🏗️ **角色与权限体系总结**

基于代码分析，我来为你梳理整个角色权限逻辑：
## 🏗️ **Hi-IdeaGen 系统角色与权限体系总结**

这是一个完整的多级权限管理系统：

### 🎭 **1. 角色体系架构**

#### **角色定义**：
```python
class InsetRole(str, Enum):
    ADMIN = "admin"              # 机构管理员
    SUPER_ADMIN = "super_admin"  # 超级管理员

PERMISSION_LEVELS = {
    "super_admin": 3,  # 权限等级最高
    "admin": 2,        # 机构管理员权限等级
    # 其他角色都是0
}
```

#### **机构隔离模型**：
- **超级管理员**：跨机构管理，无机构限制
- **机构管理员**：仅管理本机构内资源
- **普通用户**：使用本机构内资源

---

### 🔐 **2. 核心权限控制逻辑**

#### **A. 用户管理权限**

| 操作 | 超级管理员 (SUPER_ADMIN) | 机构管理员 (ADMIN) |
|------|-------------------------|------------------|
| **查看用户** | ✅ 全平台所有用户 | ⚠️ 仅本机构用户 |
| **创建用户** | ✅ 为指定机构创建（必须指定organization_id） | ⚠️ 仅为本机构创建 |
| **修改用户** | ✅ 修改任何用户 | ⚠️ 仅修改本机构用户 |
| **删除用户** | ✅ 删除任何用户（除自己） | ⚠️ 仅删除本机构用户（除自己） |

#### **B. 机构管理权限**

| 操作 | 超级管理员 | 机构管理员 |
|------|-----------|----------|
| **创建机构** | ✅ 可创建任何机构 | ❌ 无权限 |
| **管理机构** | ✅ 管理所有机构 | ❌ 无权限 |

#### **C. 角色管理权限**

| 操作 | 超级管理员 | 机构管理员 |
|------|-----------|----------|
| **查看角色** | ✅ 所有机构的角色 | ⚠️ 仅本机构角色 |
| **创建角色** | ✅ 为任何机构创建角色 | ⚠️ 仅为本机构创建角色 |
| **创建超级管理员角色** | ❌ 禁止（系统保护） | ❌ 禁止 |
| **修改/删除角色** | ✅ 任何角色 | ⚠️ 仅本机构角色 |

#### **D. 菜单权限管理**

| 操作 | 超级管理员 | 机构管理员 |
|------|-----------|----------|
| **管理系统菜单** | ✅ 创建/编辑/删除系统菜单 | ❌ 无权限 |
| **为机构分配菜单** | ✅ 为任何机构分配菜单 | ❌ 无权限 |
| **为角色分配菜单** | ✅ 为任何机构的角色分配菜单 | ⚠️ 仅为本机构角色分配菜单 |
| **查看角色菜单** | ✅ 查看任何角色菜单 | ⚠️ 仅查看本机构角色菜单 |

---

### 🛡️ **3. 安全机制与保护措施**

#### **A. 自我保护机制**
```python
# 任何管理员都不能删除自己
if user.id == current_user.id:
    logger.info(f"用户 {current_user.username} 试图删除自己，删除失败")
    return send_data(False, None, "不能删除自己")
```

#### **B. 角色等级保护**
```python
# 不能删除同级或更高级别用户
current_role = PERMISSION_LEVELS.get(current_user.role.identifier) or 0
target_role = PERMISSION_LEVELS.get(user.role.identifier) or 0
if target_role >= current_role:
    return send_data(False, None, "权限不足")
```

#### **C. 角色创建限制**
```python
# 机构管理员不能创建超级管理员
if (current_user.role.identifier != InsetRole.SUPER_ADMIN 
    and role.identifier == InsetRole.SUPER_ADMIN):
    return send_data(False, None, "角色权限不够")

# 禁止创建超级管理员角色（系统保护）
if role.identifier == InsetRole.SUPER_ADMIN:
    return send_data(False, None, f"角色标识符{InsetRole.SUPER_ADMIN.value}不允许创建")
```

#### **D. 机构隔离保护**
```python
# 机构管理员权限检查
if current_user.role.identifier != InsetRole.SUPER_ADMIN:
    if not current_user.organization or user.organization.id != current_user.organization.id:
        logger.info(f"用户 {current_user.username} 试图删除 {user.username}，权限不足，删除失败")
        return send_data(False, None, "权限不足，只能删除本机构用户")
```

---

### 🎯 **4. 关键业务规则**

#### **A. 超级管理员特殊规则**
1. **创建用户必须指定机构**：`"请先选择用户所属机构"`
2. **不能调用某些机构专用接口**：如获取角色菜单树
3. **跨机构操作权限**：可以操作任何机构的资源

#### **B. 机构管理员限制**
1. **机构边界严格**：只能在自己机构内操作
2. **角色限制**：不能创建或管理超级管理员角色
3. **菜单权限**：不能管理系统级菜单，只能为本机构角色分配菜单

#### **C. 数据隔离原则**
1. **用户数据隔离**：机构管理员看不到其他机构用户
2. **角色数据隔离**：每个机构的角色独立管理
3. **菜单权限隔离**：角色菜单权限按机构分配

---

### 📊 **5. 权限验证流程**

```mermaid
graph TD
    A[请求到达] --> B{检查用户角色}
    B -->|SUPER_ADMIN| C[超级管理员流程]
    B -->|ADMIN| D[机构管理员流程]
    B -->|其他| E[普通用户流程]
    
    C --> F{操作类型}
    F -->|跨机构操作| G[允许]
    F -->|机构内操作| G
    F -->|系统级操作| G
    
    D --> H{检查机构权限}
    H -->|本机构| I[允许]
    H -->|跨机构| J[拒绝]
    
    E --> K{检查具体权限}
    K -->|有权限| L[允许]
    K -->|无权限| M[拒绝]
```

权限体系实现了**严格的多级权限控制**和**完整的机构数据隔离**，确保了系统的安全性和数据的隔离性！
## 后续设计API权限时要考虑严格的渗透测试情况，比如越权访问问题，敏感信息传输加密问题（数据库里一般不做加密）
