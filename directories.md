# ... existing code ...
- `app/core/config.py`：全局配置，已支持 Redis 相关配置（REDIS_URL）
- `app/core/redis_client.py`：全局 Redis 连接池管理，支持单点登录 token 存储与校验
- `app/models/model_call_log.py`：大模型调用日志表 ORM 定义，记录每次大模型调用的关键信息
- `app/api/schemas/model_call_log.py`：大模型调用日志的 Pydantic schema 定义
- `app/models/project_url_summary.py`：项目网页链接及内容总结表 ORM 定义，记录项目相关网页及其内容摘要
- `app/api/schemas/project_url_summary.py`：项目网页链接及内容总结的 Pydantic schema 定义
- `app/api/repository/voice_text.py`：录音转文字的仓储层，提供创建与更新接口
- `app/api/schemas/voice_text.py`：录音转文字的请求与响应模型
# ... existing code ... 