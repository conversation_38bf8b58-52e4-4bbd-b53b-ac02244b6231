# 文件名: Dockerfile.minimal-base
FROM python:3.11-slim

# 配置阿里巴巴源
RUN ls -la /etc/apt/ && \
    if [ -f /etc/apt/sources.list ]; then \
        sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
        sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list; \
    else \
        echo "deb https://mirrors.aliyun.com/debian bookworm main contrib non-free" > /etc/apt/sources.list.d/aliyun.list && \
        echo "deb https://mirrors.aliyun.com/debian bookworm-updates main contrib non-free" >> /etc/apt/sources.list.d/aliyun.list; \
    fi

# 安装系统级依赖（编译工具和PostgreSQL客户端库）
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    python3-dev \
    && apt-get clean \
    && apt-get install -y pandoc && \
    && rm -rf /var/lib/apt/lists/*

# 预安装pip最新版本并配置pip源为阿里云
RUN pip install --no-cache-dir --upgrade pip && \
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com