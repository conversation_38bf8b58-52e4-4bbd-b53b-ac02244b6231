services:

  postgres:    # 服务名称
    image: postgres:15.6
    container_name: hi-ideagen-postgres    # Docker 容器的名称
    
    ports:
      - "5432:5432"    # 主机端口:容器端口
    
    volumes:    # 数据卷配置
      - ~/data/postgres:/var/lib/postgresql/data    # 主机路径:容器路径 将数据持久化存储在主机的 ./data/postgres 目录
    
    environment:    # 环境变量配置
      POSTGRES_DB: hi_ideagen      # 指定要创建的数据库名称
      POSTGRES_USER: postgres      # 指定数据库用户名
      POSTGRES_PASSWORD: 123456    # 指定数据库密码
      PGDATA: /var/lib/postgresql/data/pgdata    # 指定数据文件存储位置