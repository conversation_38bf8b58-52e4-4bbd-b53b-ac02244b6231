import os
import sys
from dotenv import load_dotenv
# from aerich import Command
# from app.db.config import TORTOISE_ORM
# import asyncio

# 确保在导入settings之前加载.env文件
dotenv_path = os.path.join(os.path.dirname(__file__), ".env")

# 加载.env文件
load_dotenv(dotenv_path, override=True)


# async def upgrade_db():
#     command = Command(
#         tortoise_config=TORTOISE_ORM,
#         app="models",          # ⚡ 注意，写你的app名字（在 TORTOISE_ORM["apps"] 里）
#         location="./migrations" # ⚡ 指定 migration 存放目录
#     )
#     await command.init()    # 必须先 init
#     await command.upgrade() # 再 upgrade

import uvicorn
from app.core.config import settings

if __name__ == "__main__":
    print("Starting API server...")
    print(f"Host: {settings.HOST}")
    print(f"Port: {settings.PORT}")
    
    try:
        # asyncio.run(upgrade_db())
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=True
        )
    except Exception as e:
        print(f"Error starting server: {e}", file=sys.stderr)
        raise 