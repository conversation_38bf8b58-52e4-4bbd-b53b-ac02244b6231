{"version": "2.0.0", "tasks": [{"label": "activate-conda-env", "type": "shell", "windows": {"command": "powershell -ExecutionPolicy ByPass -Command \"conda activate ideagen_env && echo 'Conda环境已激活: ideagen_env'\""}, "linux": {"command": "source activate ideagen_env && echo 'Conda环境已激活: ideagen_env'"}, "osx": {"command": "source activate ideagen_env && echo 'Conda环境已激活: ideagen_env'"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false, "clear": false}, "problemMatcher": []}]}