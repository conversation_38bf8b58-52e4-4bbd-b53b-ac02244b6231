from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "article_generation_configs" DROP COLUMN "custom_template";
        ALTER TABLE "article_generation_configs" DROP COLUMN "additional_info";
        ALTER TABLE "article_generation_configs" DROP COLUMN "reference_materials";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "article_generation_configs" ADD "custom_template" TEXT;
        ALTER TABLE "article_generation_configs" ADD "additional_info" TEXT;
        ALTER TABLE "article_generation_configs" ADD "reference_materials" TEXT;"""
