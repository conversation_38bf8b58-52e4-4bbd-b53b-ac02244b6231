from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "menus" ADD "organization_id" UUID;
        ALTER TABLE "menus" ADD CONSTRAINT "fk_menus_organiza_fab9cac2" FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "menus" DROP CONSTRAINT IF EXISTS "fk_menus_organiza_fab9cac2";
        ALTER TABLE "menus" DROP COLUMN "organization_id";"""
