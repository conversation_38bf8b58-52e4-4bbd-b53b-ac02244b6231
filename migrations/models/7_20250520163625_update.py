from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "menus" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "identifier" VARCHAR(100) NOT NULL,
    "order" INT NOT NULL DEFAULT 0,
    "parent_id" UUID,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "menus"."name" IS '菜单名称';
COMMENT ON COLUMN "menus"."identifier" IS '菜单标识符';
COMMENT ON COLUMN "menus"."order" IS '菜单顺序';
COMMENT ON COLUMN "menus"."parent_id" IS '父菜单ID';
COMMENT ON COLUMN "menus"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "menus"."updated_at" IS '更新时间';
COMMENT ON COLUMN "menus"."deleted_at" IS '删除时间';
COMMENT ON TABLE "menus" IS '菜单表';
        CREATE TABLE IF NOT EXISTS "roles" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "identifier" VARCHAR(100) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "roles"."name" IS '角色名称';
COMMENT ON COLUMN "roles"."identifier" IS '角色标识符';
COMMENT ON COLUMN "roles"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "roles"."created_at" IS '创建时间';
COMMENT ON COLUMN "roles"."updated_at" IS '更新时间';
COMMENT ON COLUMN "roles"."deleted_at" IS '删除时间';
COMMENT ON TABLE "roles" IS '角色表';
        ALTER TABLE "users" ADD "role_id" UUID;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "users" DROP COLUMN "role_id";
        DROP TABLE IF EXISTS "roles";
        DROP TABLE IF EXISTS "menus";"""
