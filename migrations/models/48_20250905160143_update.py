from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_members" ADD "user_id" UUID;
        ALTER TABLE "project_members" ADD CONSTRAINT "fk_project__users_31fc404b" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_members" DROP CONSTRAINT IF EXISTS "fk_project__users_31fc404b";
        ALTER TABLE "project_members" DROP COLUMN "user_id";"""
