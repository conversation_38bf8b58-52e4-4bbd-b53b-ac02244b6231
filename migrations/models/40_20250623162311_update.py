from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "docgen_project_url_summary" (
    "id" UUID NOT NULL PRIMARY KEY,
    "url" VARCHAR(512) NOT NULL,
    "summary" TEXT,
    "is_valid" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "project_id" UUID REFERENCES "project_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "docgen_project_url_summary"."id" IS '主键';
COMMENT ON COLUMN "docgen_project_url_summary"."url" IS '网页地址';
COMMENT ON COLUMN "docgen_project_url_summary"."summary" IS 'url的内容总结';
COMMENT ON COLUMN "docgen_project_url_summary"."is_valid" IS '是否有效';
COMMENT ON COLUMN "docgen_project_url_summary"."created_at" IS '创建时间';
COMMENT ON COLUMN "docgen_project_url_summary"."updated_at" IS '更新时间';
COMMENT ON COLUMN "docgen_project_url_summary"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "docgen_project_url_summary"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "docgen_project_url_summary"."project_id" IS '项目ID';
COMMENT ON COLUMN "docgen_project_url_summary"."user_id" IS '创建用户';
COMMENT ON TABLE "docgen_project_url_summary" IS '项目用户自主输入的网页链接及内容总结表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "docgen_project_url_summary";"""
