from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "hi_insight_reports" (
    "id" UUID NOT NULL PRIMARY KEY,
    "title" VARCHAR(500) NOT NULL,
    "report_type" VARCHAR(20) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'PROCESSING',
    "model_name" VARCHAR(200),
    "generation_time" INT,
    "input_tokens" INT,
    "output_tokens" INT,
    "total_tokens" INT,
    "report_generation_time" TIMESTAMPTZ,
    "ai_generated_report" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_historical" BOOL NOT NULL DEFAULT False,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
    "organization_id" UUID REFERENCES "organizations" ("id") ON DELETE CASCADE,
    "model_config_id" UUID REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "research_id" UUID
);
COMMENT ON COLUMN "hi_insight_reports"."id" IS '报告唯一标识';
COMMENT ON COLUMN "hi_insight_reports"."title" IS '报告标题';
COMMENT ON COLUMN "hi_insight_reports"."report_type" IS '报告类型';
COMMENT ON COLUMN "hi_insight_reports"."status" IS '报告状态';
COMMENT ON COLUMN "hi_insight_reports"."model_name" IS '使用的模型名称';
COMMENT ON COLUMN "hi_insight_reports"."generation_time" IS '生成耗时(秒)';
COMMENT ON COLUMN "hi_insight_reports"."input_tokens" IS '输入token数量';
COMMENT ON COLUMN "hi_insight_reports"."output_tokens" IS '输出token数量';
COMMENT ON COLUMN "hi_insight_reports"."total_tokens" IS '总token数量';
COMMENT ON COLUMN "hi_insight_reports"."report_generation_time" IS '报告生成完成时间';
COMMENT ON COLUMN "hi_insight_reports"."ai_generated_report" IS 'AI生成的报告文件路径';
COMMENT ON COLUMN "hi_insight_reports"."created_at" IS '创建时间';
COMMENT ON COLUMN "hi_insight_reports"."updated_at" IS '更新时间';
COMMENT ON COLUMN "hi_insight_reports"."is_historical" IS '是否为历史记录';
COMMENT ON COLUMN "hi_insight_reports"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "hi_insight_reports"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "hi_insight_reports"."user_id" IS '创建用户';
COMMENT ON COLUMN "hi_insight_reports"."organization_id" IS '所属机构';
COMMENT ON COLUMN "hi_insight_reports"."model_config_id" IS '关联的模型配置ID';
COMMENT ON COLUMN "hi_insight_reports"."research_id" IS '关联的研究对象ID';
COMMENT ON TABLE "hi_insight_reports" IS 'insight报告表 - 存储各类分析报告及其生成数据';
        ALTER TABLE "inspirations" ADD "insight_reports_id" UUID;
        ALTER TABLE "knowledge_canvas" ADD "insight_reports_id" UUID;
        ALTER TABLE "inspirations" ADD CONSTRAINT "fk_inspirat_hi_insig_9937fe5a" FOREIGN KEY ("insight_reports_id") REFERENCES "hi_insight_reports" ("id") ON DELETE CASCADE;
        ALTER TABLE "knowledge_canvas" ADD CONSTRAINT "fk_knowledg_hi_insig_b1d66f33" FOREIGN KEY ("insight_reports_id") REFERENCES "hi_insight_reports" ("id") ON DELETE CASCADE;
COMMENT ON COLUMN "inspirations"."insight_reports_id" IS '关联的insight报告ID';
COMMENT ON COLUMN "knowledge_canvas"."insight_reports_id" IS '关联的insight报告ID';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_canvas" DROP CONSTRAINT IF EXISTS "fk_knowledg_hi_insig_b1d66f33";
        ALTER TABLE "inspirations" DROP CONSTRAINT IF EXISTS "fk_inspirat_hi_insig_9937fe5a";
        ALTER TABLE "knowledge_canvas" DROP COLUMN "insight_reports_id";
        ALTER TABLE "inspirations" DROP COLUMN "insight_reports_id";
        DROP TABLE IF EXISTS "hi_insight_reports";""" 