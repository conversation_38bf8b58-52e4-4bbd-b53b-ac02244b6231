from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "inspirations" ADD "ai_keynotes" TEXT;
        ALTER TABLE "inspirations" ADD "ai_outline" TEXT;
        ALTER TABLE "inspirations" ADD "ai_analysis" TEXT;
        ALTER TABLE "inspirations" ADD "ai_expanded" TEXT;
        ALTER TABLE "inspirations" ADD "ai_probe" TEXT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "inspirations" DROP COLUMN "ai_keynotes";
        ALTER TABLE "inspirations" DROP COLUMN "ai_outline";
        ALTER TABLE "inspirations" DROP COLUMN "ai_analysis";
        ALTER TABLE "inspirations" DROP COLUMN "ai_expanded";
        ALTER TABLE "inspirations" DROP COLUMN "ai_probe";"""
