from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "inspirations" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL,
    "source" JSONB,
    "summary" TEXT,
    "content" TEXT,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "user_id" UUID REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "inspirations"."id" IS '灵感ID';
COMMENT ON COLUMN "inspirations"."name" IS '灵感名称';
COMMENT ON COLUMN "inspirations"."source" IS '灵感来源数据';
COMMENT ON COLUMN "inspirations"."summary" IS '综述';
COMMENT ON COLUMN "inspirations"."content" IS '灵感内容';
COMMENT ON COLUMN "inspirations"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "inspirations"."created_at" IS '创建时间';
COMMENT ON COLUMN "inspirations"."updated_at" IS '更新时间';
COMMENT ON COLUMN "inspirations"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "inspirations"."user_id" IS '创建用户';
COMMENT ON TABLE "inspirations" IS '灵感库表';
        CREATE TABLE IF NOT EXISTS "inspiration_tags" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "user_id" UUID REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "inspiration_tags"."name" IS '标签名称';
COMMENT ON COLUMN "inspiration_tags"."created_at" IS '创建时间';
COMMENT ON COLUMN "inspiration_tags"."updated_at" IS '更新时间';
COMMENT ON COLUMN "inspiration_tags"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "inspiration_tags"."user_id" IS '创建用户';
COMMENT ON TABLE "inspiration_tags" IS '灵感标签模型';
        ALTER TABLE "knowledge_canvas" ADD "inspiration_source" TEXT;
        CREATE TABLE "inspiration_tag_relation" (
    "inspirations_id" UUID NOT NULL REFERENCES "inspirations" ("id") ON DELETE CASCADE,
    "inspirationtag_id" UUID NOT NULL REFERENCES "inspiration_tags" ("id") ON DELETE CASCADE
);
COMMENT ON TABLE "inspiration_tag_relation" IS '标签';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_canvas" DROP COLUMN "inspiration_source";
        DROP TABLE IF EXISTS "inspirations";
        DROP TABLE IF EXISTS "inspiration_tags";"""
