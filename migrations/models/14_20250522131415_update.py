from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organizations" ADD "limit_count" INT;
        ALTER TABLE "organizations" ADD "left_count" INT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organizations" DROP COLUMN "limit_count";
        ALTER TABLE "organizations" DROP COLUMN "left_count";"""
