from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "llm_call_logs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "model_name" VARCHAR(128) NOT NULL,
    "model_api_key" VARCHAR(256) NOT NULL,
    "model_api_url" VARCHAR(256) NOT NULL,
    "prompt" JSONB,
    "product_type" VARCHAR(11) NOT NULL,
    "related_id" UUID NOT NULL,
    "response" TEXT,
    "description" VARCHAR(256),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "llm_call_logs"."id" IS '主键';
COMMENT ON COLUMN "llm_call_logs"."model_name" IS '模型名称';
COMMENT ON COLUMN "llm_call_logs"."model_api_key" IS '模型密钥';
COMMENT ON COLUMN "llm_call_logs"."model_api_url" IS '模型API地址';
COMMENT ON COLUMN "llm_call_logs"."prompt" IS '大模型调用的提示词';
COMMENT ON COLUMN "llm_call_logs"."product_type" IS '调用方的产品类型说明';
COMMENT ON COLUMN "llm_call_logs"."related_id" IS '调用大模型的生成内容的记录主ID';
COMMENT ON COLUMN "llm_call_logs"."response" IS '大模型返回的完整结果';
COMMENT ON COLUMN "llm_call_logs"."description" IS '调用的描述信息';
COMMENT ON COLUMN "llm_call_logs"."created_at" IS '创建时间';
COMMENT ON COLUMN "llm_call_logs"."user_id" IS '调用大模型的用户ID';
COMMENT ON TABLE "llm_call_logs" IS '大模型调用日志表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "llm_call_logs";"""
