from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_canvas" ADD "original_article_truncated" TEXT;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "related_notes" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "type" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "original_article" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "source_type" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "name" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "ai_questions" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "user_notes" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "key_notes" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "ai_outline" DROP NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "summary" DROP NOT NULL;
        COMMENT ON COLUMN "project_configs"."status" IS '项目状态 (
        CONFIGURING,
        OUTLINE_GENERATING,
        OUTLINE_GENERATED,
        OUTLINE_FAILED,
        REPORT_GENERATING,
        REPORT_GENERATED,
        REPORT_FAILED,
        REMOVE_HALLUCINATING,
        REMOVE_HALLUCINATED,
        REMOVE_AI_TRACING,
        REMOVE_AI_TRACED
        )';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "project_configs"."status" IS '项目状态 (
        CONFIGURING,
        OUTLINE_GENERATING,
        OUTLINE_GENERATED,
        OUTLINE_FAILED,
        REPORT_GENERATING,
        REPORT_GENERATED,
        REPORT_FAILED,
        )';
        ALTER TABLE "knowledge_canvas" DROP COLUMN "original_article_truncated";
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "related_notes" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "type" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "original_article" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "source_type" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "name" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "ai_questions" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "user_notes" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "key_notes" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "ai_outline" SET NOT NULL;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "summary" SET NOT NULL;"""
