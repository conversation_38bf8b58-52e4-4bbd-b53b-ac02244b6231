from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organizations" DROP CONSTRAINT IF EXISTS "fk_organiza_users_8badba1c";
        CREATE TABLE IF NOT EXISTS "file_biz_relations" (
    "id" UUID NOT NULL PRIMARY KEY,
    "ai_analysis_summary" TEXT,
    "product_type" VARCHAR(11) NOT NULL,
    "biz_id" VARCHAR(100),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "file_id" UUID NOT NULL REFERENCES "requirements_attachments_files" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "file_biz_relations"."ai_analysis_summary" IS 'AI分析总结信息（来自LLM的详细分析结果）';
COMMENT ON COLUMN "file_biz_relations"."product_type" IS '产品类型（INSIGHTPLUS/DOCGEN/LABPLAN等）';
COMMENT ON COLUMN "file_biz_relations"."biz_id" IS '关联的业务ID（项目ID、实验ID等，不做外键约束）';
COMMENT ON COLUMN "file_biz_relations"."created_at" IS '创建时间';
COMMENT ON COLUMN "file_biz_relations"."updated_at" IS '更新时间';
COMMENT ON COLUMN "file_biz_relations"."file_id" IS '关联的文件';
COMMENT ON TABLE "file_biz_relations" IS '文件和业务关联表 - 用于保存各产品线的文件和业务信息';
        ALTER TABLE "requirements_attachments_files" ADD "is_deleted" BOOL NOT NULL DEFAULT False;
        ALTER TABLE "requirements_attachments_files" ADD "deleted_at" TIMESTAMPTZ;
        ALTER TABLE "requirements_attachments_files" ADD "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organizations" ADD "user_id" UUID;
        ALTER TABLE "requirements_attachments_files" DROP COLUMN "is_deleted";
        ALTER TABLE "requirements_attachments_files" DROP COLUMN "deleted_at";
        ALTER TABLE "requirements_attachments_files" DROP COLUMN "updated_at";
        DROP TABLE IF EXISTS "file_biz_relations";
        ALTER TABLE "organizations" ADD CONSTRAINT "fk_organiza_users_8badba1c" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;"""
