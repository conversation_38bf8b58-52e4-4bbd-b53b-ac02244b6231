from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "inspirations" ADD "original_article_truncated" TEXT;
        ALTER TABLE "inspirations" ADD "ai_thought_process" TEXT;
        COMMENT ON <PERSON>LUMN "inspirations"."summary" IS '灵感库简介';
        COMMENT ON COLUMN "knowledge_canvas"."summary" IS '简介';
        COMMENT ON COLUMN "knowledge_canvas"."original_article_truncated" IS '原始文章截取or灵感库综述';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "inspirations" DROP COLUMN "original_article_truncated";
        ALTER TABLE "inspirations" DROP COLUMN "ai_thought_process";
        COMMENT ON COLUMN "inspirations"."summary" IS '综述';
        COMMENT ON COLUMN "knowledge_canvas"."summary" IS '概要';
        COMMENT ON COLUMN "knowledge_canvas"."original_article_truncated" IS '原始文章截取';"""
