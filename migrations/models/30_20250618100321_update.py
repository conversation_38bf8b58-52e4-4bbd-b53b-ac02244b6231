from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" ALTER COLUMN "ai_leader_introduction" TYPE VARCHAR(2000) USING "ai_leader_introduction"::VARCHAR(2000);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" ALTER COLUMN "ai_leader_introduction" TYPE VARCHAR(500) USING "ai_leader_introduction"::VARCHAR(500);"""
