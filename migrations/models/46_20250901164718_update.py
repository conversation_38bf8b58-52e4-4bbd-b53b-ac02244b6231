from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "docgen_feasibility_meta" (
    "id" UUID NOT NULL PRIMARY KEY,
    "expect_return_rate" DOUBLE PRECISION,
    "payback_period" DOUBLE PRECISION,
    "analysis_method" VARCHAR(50),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "docgen_feasibility_meta"."expect_return_rate" IS '预期内部收益率';
COMMENT ON COLUMN "docgen_feasibility_meta"."payback_period" IS '投资回收期';
COMMENT ON COLUMN "docgen_feasibility_meta"."analysis_method" IS '分析方法';
COMMENT ON COLUMN "docgen_feasibility_meta"."created_at" IS '创建时间';
COMMENT ON COLUMN "docgen_feasibility_meta"."updated_at" IS '更新时间';
COMMENT ON COLUMN "docgen_feasibility_meta"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "docgen_feasibility_meta"."deleted_at" IS '删除时间';
COMMENT ON TABLE "docgen_feasibility_meta" IS '可行性报告的配置项';
        ALTER TABLE "project_configs" ADD "document_type" VARCHAR(50);
        ALTER TABLE "project_configs" ADD "doc_type" VARCHAR(17);
        ALTER TABLE "project_configs" ADD "extend_id" UUID;
        CREATE TABLE IF NOT EXISTS "docgen_think_tank_meta" (
    "id" UUID NOT NULL PRIMARY KEY,
    "target" VARCHAR(50),
    "analysis_method" VARCHAR(50),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "project_configs"."document_type" IS '类型字段';
COMMENT ON COLUMN "project_configs"."doc_type" IS '报告的类型，';
COMMENT ON COLUMN "project_configs"."extend_id" IS '扩展ID';
COMMENT ON COLUMN "docgen_think_tank_meta"."target" IS '目标受众';
COMMENT ON COLUMN "docgen_think_tank_meta"."analysis_method" IS '分析方法（选填）';
COMMENT ON COLUMN "docgen_think_tank_meta"."created_at" IS '创建时间';
COMMENT ON COLUMN "docgen_think_tank_meta"."updated_at" IS '更新时间';
COMMENT ON COLUMN "docgen_think_tank_meta"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "docgen_think_tank_meta"."deleted_at" IS '删除时间';
COMMENT ON TABLE "docgen_think_tank_meta" IS '智库报告的配置项';
        ALTER TABLE "upload_files" ADD "mime_type" VARCHAR(200);
        ALTER TABLE "upload_files" ADD "file_size" INT;
        ALTER TABLE "upload_files" ADD "file_duration" INT;
        CREATE TABLE IF NOT EXISTS "docgen_voice_text" (
    "id" UUID NOT NULL PRIMARY KEY,
    "text_content" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "upload_files_id" UUID REFERENCES "upload_files" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "upload_files"."mime_type" IS '文件的mimetype';
COMMENT ON COLUMN "upload_files"."file_size" IS '文件的大小(byte)';
COMMENT ON COLUMN "upload_files"."file_duration" IS '媒体文件的持续时长(毫秒)';
COMMENT ON COLUMN "docgen_voice_text"."text_content" IS '录音文件的文字内容';
COMMENT ON COLUMN "docgen_voice_text"."created_at" IS '创建时间';
COMMENT ON COLUMN "docgen_voice_text"."updated_at" IS '更新时间';
COMMENT ON COLUMN "docgen_voice_text"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "docgen_voice_text"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "docgen_voice_text"."upload_files_id" IS '上传的语音文件的地址';
COMMENT ON TABLE "docgen_voice_text" IS '录音文件的文字内容转译';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "upload_files" DROP COLUMN "mime_type";
        ALTER TABLE "upload_files" DROP COLUMN "file_size";
        ALTER TABLE "upload_files" DROP COLUMN "file_duration";
        ALTER TABLE "project_configs" DROP COLUMN "document_type";
        ALTER TABLE "project_configs" DROP COLUMN "doc_type";
        ALTER TABLE "project_configs" DROP COLUMN "extend_id";
        DROP TABLE IF EXISTS "docgen_feasibility_meta";
        DROP TABLE IF EXISTS "docgen_think_tank_meta";
        DROP TABLE IF EXISTS "docgen_voice_text";"""
