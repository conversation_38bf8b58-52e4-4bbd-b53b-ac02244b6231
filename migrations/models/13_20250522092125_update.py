from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "menus" DROP CONSTRAINT IF EXISTS "fk_menus_organiza_fab9cac2";
        CREATE TABLE IF NOT EXISTS "organization_role_menus" (
    "id" UUID NOT NULL PRIMARY KEY,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "menu_id" UUID NOT NULL REFERENCES "menus" ("id") ON DELETE CASCADE,
    "role_id" UUID NOT NULL REFERENCES "roles" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_role_menus"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_role_menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_role_menus"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_role_menus"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_role_menus"."menu_id" IS '菜单';
COMMENT ON COLUMN "organization_role_menus"."role_id" IS '角色';
COMMENT ON TABLE "organization_role_menus" IS '机构角色菜单关联表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "organization_role_menus";
        ALTER TABLE "menus" ADD CONSTRAINT "fk_menus_organiza_fab9cac2" FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE CASCADE;"""
