from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organizations" ADD "use_count" INT;
        ALTER TABLE "organizations" DROP COLUMN "left_count";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organizations" ADD "left_count" INT;
        ALTER TABLE "organizations" DROP COLUMN "use_count";"""
