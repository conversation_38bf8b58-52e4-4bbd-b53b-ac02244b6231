from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "docgen_voice_text" ADD "user_id" UUID;
        ALTER TABLE "docgen_voice_text" ADD CONSTRAINT "fk_docgen_v_users_4fc6efbf" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "docgen_voice_text" DROP CONSTRAINT IF EXISTS "fk_docgen_v_users_4fc6efbf";
        ALTER TABLE "docgen_voice_text" DROP COLUMN "user_id";"""
