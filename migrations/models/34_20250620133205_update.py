from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "organization_models" (
    "id" UUID NOT NULL PRIMARY KEY,
    "default_way" VARCHAR(19) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_models"."default_way" IS '模型的默认用途';
COMMENT ON COLUMN "organization_models"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_models"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_models"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_models"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_models"."model_id" IS '模型';
COMMENT ON COLUMN "organization_models"."organization_id" IS '机构';
COMMENT ON TABLE "organization_models" IS '机构模型关联表';
        CREATE TABLE IF NOT EXISTS "user_default_models" (
    "id" UUID NOT NULL PRIMARY KEY,
    "default_way" VARCHAR(19) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "user_default_models"."default_way" IS '模型的默认用途';
COMMENT ON COLUMN "user_default_models"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "user_default_models"."created_at" IS '创建时间';
COMMENT ON COLUMN "user_default_models"."updated_at" IS '更新时间';
COMMENT ON COLUMN "user_default_models"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "user_default_models"."model_id" IS '模型';
COMMENT ON COLUMN "user_default_models"."user_id" IS '用户';
COMMENT ON TABLE "user_default_models" IS '用户的默认模型关联表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "organization_models";
        DROP TABLE IF EXISTS "user_default_models";"""
