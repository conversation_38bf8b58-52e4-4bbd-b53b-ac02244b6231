from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "inspirations_canvas_report_relation" (
    "id" UUID NOT NULL PRIMARY KEY,
    "inspiration_id" UUID NOT NULL,
    "inspiration_type" VARCHAR(16) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "report_id" UUID NOT NULL REFERENCES "hi_insight_reports" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_inspiration_inspira_af3c2a" UNIQUE ("inspiration_id", "inspiration_type", "report_id")
);
COMMENT ON COLUMN "inspirations_canvas_report_relation"."id" IS '关系ID';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."inspiration_id" IS '灵感来源ID';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."inspiration_type" IS '灵感来源类型';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."created_at" IS '创建时间';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."updated_at" IS '更新时间';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "inspirations_canvas_report_relation"."report_id" IS '关联的insight报告';
COMMENT ON TABLE "inspirations_canvas_report_relation" IS '灵感库、知识卡片与insight报告的多对多关系表';
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "inspirations_canvas_report_relation";"""
