from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "article_generation_configs" ADD "user_add_demo_id" UUID;
        ALTER TABLE "article_generation_configs" ADD CONSTRAINT "fk_article__upload_f_843744a4" FOREIGN KEY ("user_add_demo_id") REFERENCES "upload_files" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "article_generation_configs" DROP CONSTRAINT IF EXISTS "fk_article__upload_f_843744a4";
        ALTER TABLE "article_generation_configs" DROP COLUMN "user_add_demo_id";"""
