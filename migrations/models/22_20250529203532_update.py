from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_canvas" ADD "ai_keynotes" TEXT;
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "author" TYPE VARCHAR(500) USING "author"::VARCHAR(500);
        COMMENT ON COLUMN "knowledge_canvas"."ai_outline" IS 'AI结构大纲';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_canvas" DROP COLUMN "ai_keynotes";
        ALTER TABLE "knowledge_canvas" ALTER COLUMN "author" TYPE VARCHAR(255) USING "author"::VA<PERSON><PERSON><PERSON>(255);
        COMMENT ON COLUMN "knowledge_canvas"."ai_outline" IS 'AI大纲';"""
