from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "file_biz_relations" (
            "id" UUID NOT NULL  PRIMARY KEY,
            "ai_analysis_summary" TEXT,
            "product_type" VARCHAR(20) NOT NULL,
            "biz_id" VARCHAR(100),
            "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
            "file_id" UUID NOT NULL REFERENCES "requirements_attachments_files" ("id") ON DELETE CASCADE
        );
        COMMENT ON TABLE "file_biz_relations" IS '文件和业务关联表';
        COMMENT ON COLUMN "file_biz_relations"."id" IS '主键ID';
        COMMENT ON COLUMN "file_biz_relations"."ai_analysis_summary" IS 'AI分析总结信息（来自LLM的详细分析结果）';
        COMMENT ON COLUMN "file_biz_relations"."product_type" IS '产品类型（INSIGHTPLUS/DOCGEN/LABPLAN等）';
        COMMENT ON COLUMN "file_biz_relations"."biz_id" IS '关联的业务ID（项目ID、实验ID等，不做外键约束）';
        COMMENT ON COLUMN "file_biz_relations"."created_at" IS '创建时间';
        COMMENT ON COLUMN "file_biz_relations"."updated_at" IS '更新时间';
        COMMENT ON COLUMN "file_biz_relations"."file_id" IS '关联的文件';
        CREATE INDEX IF NOT EXISTS "idx_file_biz_relations_product_type" ON "file_biz_relations" ("product_type");
        CREATE INDEX IF NOT EXISTS "idx_file_biz_relations_biz_id" ON "file_biz_relations" ("biz_id");
        CREATE INDEX IF NOT EXISTS "idx_file_biz_relations_file_id" ON "file_biz_relations" ("file_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_file_biz_relations_file_id";
        DROP INDEX IF EXISTS "idx_file_biz_relations_biz_id";
        DROP INDEX IF EXISTS "idx_file_biz_relations_product_type";
        DROP TABLE IF EXISTS "file_biz_relations";"""