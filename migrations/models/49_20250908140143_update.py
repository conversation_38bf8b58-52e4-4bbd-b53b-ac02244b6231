from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 创建文章生成配置表
        CREATE TABLE "article_generation_configs" (
            "id" UUID PRIMARY KEY,
            "user_id" UUID NOT NULL,
            "organization_id" UUID,
            "model_id" UUID NOT NULL,
            "research_id" UUID,
            "name" VARCHAR(500) NOT NULL,
            "meeting_minutes_audio" VARCHAR(500),
            "target_audience" VARCHAR(50),
            "analysis_method" VARCHAR(200),
            "industry_type" VARCHAR(50),
            "total_investment_amount" DECIMAL(15,2),
            "financial_analysis" TEXT,
            "risk_assessment" TEXT,
            "research_field" VARCHAR(200),
            "material_subject" VARCHAR(500),
            "team_members" TEXT,
            "team_introduction" TEXT,
            "reference_library" TEXT,
            "reference_library_urls" TEXT,
            "reference_materials" TEXT,
            "custom_template" TEXT,
            "additional_info" TEXT,
            "user_add_prompt" TEXT,
            "word_count_requirement" INTEGER,
            "language_style" VARCHAR(100),
            "application_category" VARCHAR(100),
            "requirements_attachments" TEXT,
            "search_engine" VARCHAR(20) DEFAULT 'google',
            "search_list_count" INTEGER DEFAULT 10,
            "search_iterations" INTEGER DEFAULT 3,
            "retrieval_content_count" INTEGER DEFAULT 20,
            "literature_summary_enabled" BOOLEAN DEFAULT true,
            "max_content_collection_count" INTEGER DEFAULT 20,
            "max_reference_count" INTEGER DEFAULT 20,
            "temperature" DECIMAL(3,2) DEFAULT 0.7,
            "top_p" DECIMAL(3,2) DEFAULT 0.9,
            "top_k" INTEGER DEFAULT 50,
            "outline_system_prompt" TEXT,
            "content_system_prompt" TEXT,
            "outline_user_prompt" TEXT,
            "content_user_prompt" TEXT,
            "generated_outline_file_path" VARCHAR(500),
            "generated_content_file_path" VARCHAR(500),
            "outline_generation_time" TIMESTAMPTZ,
            "content_generation_time" TIMESTAMPTZ,
            "outline_generation_status" VARCHAR(20) DEFAULT 'pending',
            "content_generation_status" VARCHAR(20) DEFAULT 'pending',
            "outline_tokens_consumed" INTEGER DEFAULT 0,
            "content_tokens_consumed" INTEGER DEFAULT 0,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            "is_deleted" BOOLEAN DEFAULT false,
            "deleted_at" TIMESTAMPTZ
        );
        
        -- 添加外键约束
        ALTER TABLE "article_generation_configs" 
        ADD CONSTRAINT "fk_article_generation_configs_user_id" 
        FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;
        
        ALTER TABLE "article_generation_configs" 
        ADD CONSTRAINT "fk_article_generation_configs_organization_id" 
        FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE SET NULL;
        
        ALTER TABLE "article_generation_configs" 
        ADD CONSTRAINT "fk_article_generation_configs_model_id" 
        FOREIGN KEY ("model_id") REFERENCES "model_configs" ("id") ON DELETE CASCADE;
        
        ALTER TABLE "article_generation_configs" 
        ADD CONSTRAINT "fk_article_generation_configs_research_id" 
        FOREIGN KEY ("research_id") REFERENCES "researches" ("id") ON DELETE SET NULL;
        
        -- 添加表注释
        COMMENT ON TABLE "article_generation_configs" IS '文章生成配置模型 - 用于配置各种类型文章的生成参数';
        
        -- 添加字段注释
        COMMENT ON COLUMN "article_generation_configs"."id" IS '主键ID';
        COMMENT ON COLUMN "article_generation_configs"."user_id" IS '创建用户ID';
        COMMENT ON COLUMN "article_generation_configs"."organization_id" IS '所属机构ID';
        COMMENT ON COLUMN "article_generation_configs"."model_id" IS '选择的模型ID';
        COMMENT ON COLUMN "article_generation_configs"."research_id" IS '关联研究ID';
        COMMENT ON COLUMN "article_generation_configs"."name" IS '主题（必填）';
        COMMENT ON COLUMN "article_generation_configs"."meeting_minutes_audio" IS '会议纪要-录音文件路径';
        COMMENT ON COLUMN "article_generation_configs"."target_audience" IS '智库报告-目标受众';
        COMMENT ON COLUMN "article_generation_configs"."analysis_method" IS '智库报告-分析方法';
        COMMENT ON COLUMN "article_generation_configs"."industry_type" IS '市场行业研习报告-行业类型';
        COMMENT ON COLUMN "article_generation_configs"."total_investment_amount" IS '可研报告-总投资额';
        COMMENT ON COLUMN "article_generation_configs"."financial_analysis" IS '可研报告-财务分析';
        COMMENT ON COLUMN "article_generation_configs"."risk_assessment" IS '可研报告-风险评估';
        COMMENT ON COLUMN "article_generation_configs"."research_field" IS '文献综述-研究领域';
        COMMENT ON COLUMN "article_generation_configs"."material_subject" IS '材料主体';
        COMMENT ON COLUMN "article_generation_configs"."team_members" IS '团队成员（JSON格式存储）';
        COMMENT ON COLUMN "article_generation_configs"."team_introduction" IS '团队介绍';
        COMMENT ON COLUMN "article_generation_configs"."reference_library" IS '参考文献库';
        COMMENT ON COLUMN "article_generation_configs"."reference_library_urls" IS '参考文献库URL列表（JSON格式存储）';
        COMMENT ON COLUMN "article_generation_configs"."reference_materials" IS '参考资料';
        COMMENT ON COLUMN "article_generation_configs"."custom_template" IS '自定义模版';
        COMMENT ON COLUMN "article_generation_configs"."additional_info" IS '额外信息补充';
        COMMENT ON COLUMN "article_generation_configs"."user_add_prompt" IS '用户添加的提示词';
        COMMENT ON COLUMN "article_generation_configs"."word_count_requirement" IS '字数要求';
        COMMENT ON COLUMN "article_generation_configs"."language_style" IS '语言风格';
        COMMENT ON COLUMN "article_generation_configs"."application_category" IS '申报口径';
        COMMENT ON COLUMN "article_generation_configs"."requirements_attachments" IS '申报要求附件（JSON格式存储）';
        COMMENT ON COLUMN "article_generation_configs"."search_engine" IS '搜索引擎选择';
        COMMENT ON COLUMN "article_generation_configs"."search_list_count" IS '搜索列表数';
        COMMENT ON COLUMN "article_generation_configs"."search_iterations" IS '搜索迭代次数';
        COMMENT ON COLUMN "article_generation_configs"."retrieval_content_count" IS '最大检索内容数';
        COMMENT ON COLUMN "article_generation_configs"."literature_summary_enabled" IS '文献总结开关';
        COMMENT ON COLUMN "article_generation_configs"."max_content_collection_count" IS '最大内容收集数量';
        COMMENT ON COLUMN "article_generation_configs"."max_reference_count" IS '最大参考文献数';
        COMMENT ON COLUMN "article_generation_configs"."temperature" IS '温度参数';
        COMMENT ON COLUMN "article_generation_configs"."top_p" IS 'Top-p参数';
        COMMENT ON COLUMN "article_generation_configs"."top_k" IS 'Top-k参数';
        COMMENT ON COLUMN "article_generation_configs"."outline_system_prompt" IS '大纲的系统提示词';
        COMMENT ON COLUMN "article_generation_configs"."content_system_prompt" IS '正文的系统提示词';
        COMMENT ON COLUMN "article_generation_configs"."outline_user_prompt" IS '大纲的用户提示词';
        COMMENT ON COLUMN "article_generation_configs"."content_user_prompt" IS '正文的用户提示词';
        COMMENT ON COLUMN "article_generation_configs"."generated_outline_file_path" IS '生成的大纲文件地址';
        COMMENT ON COLUMN "article_generation_configs"."generated_content_file_path" IS '生成的正文文件地址';
        COMMENT ON COLUMN "article_generation_configs"."outline_generation_time" IS '大纲生成时间';
        COMMENT ON COLUMN "article_generation_configs"."content_generation_time" IS '正文生成时间';
        COMMENT ON COLUMN "article_generation_configs"."outline_generation_status" IS '大纲生成状态';
        COMMENT ON COLUMN "article_generation_configs"."content_generation_status" IS '正文生成状态';
        COMMENT ON COLUMN "article_generation_configs"."outline_tokens_consumed" IS '大纲生成消耗的Token数量';
        COMMENT ON COLUMN "article_generation_configs"."content_tokens_consumed" IS '正文生成消耗的Token数量';
        COMMENT ON COLUMN "article_generation_configs"."created_at" IS '创建时间';
        COMMENT ON COLUMN "article_generation_configs"."updated_at" IS '更新时间';
        COMMENT ON COLUMN "article_generation_configs"."is_deleted" IS '是否删除';
        COMMENT ON COLUMN "article_generation_configs"."deleted_at" IS '删除时间';
        
        -- 创建文章文献表
        CREATE TABLE "article_literatures" (
            "id" UUID PRIMARY KEY,
            "research_id" UUID NOT NULL,
            "title" VARCHAR(500) NOT NULL,
            "authors" VARCHAR(500) NOT NULL,
            "journal" VARCHAR(200) NOT NULL,
            "year" INTEGER NOT NULL,
            "issue" VARCHAR(20),
            "volume" VARCHAR(20),
            "pages" VARCHAR(50),
            "doi" VARCHAR(200),
            "url" VARCHAR(200),
            "summary" TEXT NOT NULL,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            "is_deleted" BOOLEAN DEFAULT false,
            "deleted_at" TIMESTAMPTZ
        );
        
        -- 添加外键约束
        ALTER TABLE "article_literatures" 
        ADD CONSTRAINT "fk_article_literatures_research_id" 
        FOREIGN KEY ("research_id") REFERENCES "researches" ("id") ON DELETE CASCADE;
        
        -- 添加表注释
        COMMENT ON TABLE "article_literatures" IS '文章文献';
        
        -- 添加字段注释
        COMMENT ON COLUMN "article_literatures"."id" IS '文献ID';
        COMMENT ON COLUMN "article_literatures"."research_id" IS '关联的研究项目ID';
        COMMENT ON COLUMN "article_literatures"."title" IS '文献标题';
        COMMENT ON COLUMN "article_literatures"."authors" IS '作者，多个作者用逗号分隔';
        COMMENT ON COLUMN "article_literatures"."journal" IS '期刊名称';
        COMMENT ON COLUMN "article_literatures"."year" IS '发表年份';
        COMMENT ON COLUMN "article_literatures"."issue" IS '期号';
        COMMENT ON COLUMN "article_literatures"."volume" IS '卷号';
        COMMENT ON COLUMN "article_literatures"."pages" IS '页码范围，如：123-145';
        COMMENT ON COLUMN "article_literatures"."doi" IS 'DOI索引';
        COMMENT ON COLUMN "article_literatures"."url" IS '文献网页链接';
        COMMENT ON COLUMN "article_literatures"."summary" IS '文献总结';
        COMMENT ON COLUMN "article_literatures"."created_at" IS '创建时间';
        COMMENT ON COLUMN "article_literatures"."updated_at" IS '更新时间';
        COMMENT ON COLUMN "article_literatures"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
        COMMENT ON COLUMN "article_literatures"."deleted_at" IS '删除时间';
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 删除文章文献表
        DROP TABLE IF EXISTS "article_literatures";
        
        -- 删除文章生成配置表
        DROP TABLE IF EXISTS "article_generation_configs";
        """ 