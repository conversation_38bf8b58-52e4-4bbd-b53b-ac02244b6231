from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "article_generation_configs" ADD "leader_id" VARCHAR(100);
        ALTER TABLE "article_generation_configs" ADD "team_members_id" VARCHAR(500);
        ALTER TABLE "article_generation_configs" ADD "requirements_attachments_id" VARCHAR(500);
        ALTER TABLE "article_generation_configs" ADD "leader_text" TEXT;
        ALTER TABLE "article_generation_configs" ADD "ai_leader_introduction" TEXT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "article_generation_configs" DROP COLUMN "leader_id";
        ALTER TABLE "article_generation_configs" DROP COLUMN "team_members_id";
        ALTER TABLE "article_generation_configs" DROP COLUMN "requirements_attachments_id";
        ALTER TABLE "article_generation_configs" DROP COLUMN "leader_text";
        ALTER TABLE "article_generation_configs" DROP COLUMN "ai_leader_introduction";"""
