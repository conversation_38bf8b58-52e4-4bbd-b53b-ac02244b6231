from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "saas_platforms" ALTER COLUMN "userinfo_url" TYPE VARCHAR(512) USING "userinfo_url"::VARCHAR(512);
        ALTER TABLE "saas_platforms" ALTER COLUMN "token_url" TYPE VARCHAR(512) USING "token_url"::VARCHAR(512);
        ALTER TABLE "users" ADD "mobile" VARCHAR(50);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "users" DROP COLUMN "mobile";
        ALTER TABLE "saas_platforms" ALTER COLUMN "userinfo_url" TYPE VARCHAR(255) USING "userinfo_url"::VARCHAR(255);
        ALTER TABLE "saas_platforms" ALTER COLUMN "token_url" TYPE VARCHAR(255) USING "token_url"::VARCHAR(255);"""
