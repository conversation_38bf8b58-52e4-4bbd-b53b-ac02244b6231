from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "researches" DROP CONSTRAINT IF EXISTS "fk_research_api_keys_daca9999";
        ALTER TABLE "researches" DROP COLUMN "api_key_id";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "researches" ADD "api_key_id" UUID NOT NULL;
        ALTER TABLE "researches" ADD CONSTRAINT "fk_research_api_keys_daca9999" FOREIGN KEY ("api_key_id") REFERENCES "api_keys" ("id") ON DELETE CASCADE;"""
