from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organization_role_menus" ALTER COLUMN "organization_id" DROP NOT NULL;
        ALTER TABLE "project_leaders" ADD "user_id" UUID;
        ALTER TABLE "project_member_joins" ADD "user_id" UUID;
        ALTER TABLE "project_leaders" ADD CONSTRAINT "fk_project__users_8ab433c5" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;
        ALTER TABLE "project_member_joins" ADD CONSTRAINT "fk_project__users_6ef72ad6" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_member_joins" DROP CONSTRAINT IF EXISTS "fk_project__users_6ef72ad6";
        ALTER TABLE "project_leaders" DROP CONSTRAINT IF EXISTS "fk_project__users_8ab433c5";
        ALTER TABLE "organizations" DROP CONSTRAINT IF EXISTS "fk_organiza_users_8badba1c";
        ALTER TABLE "project_leaders" DROP COLUMN "user_id";
        ALTER TABLE "project_member_joins" DROP COLUMN "user_id";
        ALTER TABLE "organization_role_menus" ALTER COLUMN "organization_id" SET NOT NULL;"""