from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" ADD "research_id" UUID;
        ALTER TABLE "project_configs" ADD CONSTRAINT "fk_project__research_842ba88d" FOREIGN KEY ("research_id") REFERENCES "researches" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" DROP CONSTRAINT IF EXISTS "fk_project__research_842ba88d";
        ALTER TABLE "project_configs" DROP COLUMN "research_id";"""
