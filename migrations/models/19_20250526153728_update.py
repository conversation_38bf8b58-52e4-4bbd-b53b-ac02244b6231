from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "knowledge_canvas" ADD "author" VARCHAR(255);
        ALTER TABLE "knowledge_canvas" ADD "source" TEXT;
        ALTER TABLE "knowledge_canvas" ADD "ai_analysis" TEXT;
        ALTER TABLE "knowledge_canvas" ADD "reference" TEXT;
        ALTER TABLE "knowledge_canvas" ADD "detailed_description" TEXT;
        ALTER TABLE "knowledge_canvas" ADD "ai_expanded" TEXT;
        CREATE TABLE IF NOT EXISTS "literatures" (
    "id" UUID NOT NULL PRIMARY KEY,
    "title" VARCHAR(500) NOT NULL,
    "authors" VARCHAR(500) NOT NULL,
    "journal" VARCHAR(200) NOT NULL,
    "year" INT NOT NULL,
    "issue" VARCHAR(20),
    "volume" VARCHAR(20),
    "pages" VARCHAR(50),
    "doi" VARCHAR(200),
    "url" VARCHAR(200),
    "summary" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "research_id" UUID NOT NULL REFERENCES "researches" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "knowledge_canvas"."author" IS '作者';
COMMENT ON COLUMN "knowledge_canvas"."source" IS '来源';
COMMENT ON COLUMN "knowledge_canvas"."ai_analysis" IS 'AI分析';
COMMENT ON COLUMN "knowledge_canvas"."reference" IS '引用';
COMMENT ON COLUMN "knowledge_canvas"."detailed_description" IS '详细描述';
COMMENT ON COLUMN "knowledge_canvas"."ai_expanded" IS 'AI扩写';
COMMENT ON COLUMN "literatures"."id" IS '文献ID';
COMMENT ON COLUMN "literatures"."title" IS '文献标题';
COMMENT ON COLUMN "literatures"."authors" IS '作者，多个作者用逗号分隔';
COMMENT ON COLUMN "literatures"."journal" IS '期刊名称';
COMMENT ON COLUMN "literatures"."year" IS '发表年份';
COMMENT ON COLUMN "literatures"."issue" IS '期号';
COMMENT ON COLUMN "literatures"."volume" IS '卷号';
COMMENT ON COLUMN "literatures"."pages" IS '页码范围，如：123-145';
COMMENT ON COLUMN "literatures"."doi" IS 'DOI索引';
COMMENT ON COLUMN "literatures"."url" IS '文献网页链接';
COMMENT ON COLUMN "literatures"."summary" IS '文献总结';
COMMENT ON COLUMN "literatures"."created_at" IS '创建时间';
COMMENT ON COLUMN "literatures"."updated_at" IS '更新时间';
COMMENT ON COLUMN "literatures"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "literatures"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "literatures"."research_id" IS '关联的研究项目';
COMMENT ON TABLE "literatures" IS '文献模型';
"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "researches" ADD "literatures" JSONB;
        ALTER TABLE "knowledge_canvas" DROP COLUMN "author";
        ALTER TABLE "knowledge_canvas" DROP COLUMN "source";
        ALTER TABLE "knowledge_canvas" DROP COLUMN "ai_analysis";
        ALTER TABLE "knowledge_canvas" DROP COLUMN "reference";
        ALTER TABLE "knowledge_canvas" DROP COLUMN "detailed_description";
        ALTER TABLE "knowledge_canvas" DROP COLUMN "ai_expanded";
        DROP TABLE IF EXISTS "literatures";"""
