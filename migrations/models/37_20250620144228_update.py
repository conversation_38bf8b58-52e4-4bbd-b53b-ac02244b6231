from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "organization_model_uses" (
    "id" UUID NOT NULL PRIMARY KEY,
    "default_way" VARCHAR(19) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMPTZ,
    "model_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "organization_id" UUID NOT NULL REFERENCES "organizations" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "organization_model_uses"."default_way" IS '模型的默认用途';
COMMENT ON COLUMN "organization_model_uses"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organization_model_uses"."created_at" IS '创建时间';
COMMENT ON COLUMN "organization_model_uses"."updated_at" IS '更新时间';
COMMENT ON COLUMN "organization_model_uses"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organization_model_uses"."model_id" IS '模型';
COMMENT ON COLUMN "organization_model_uses"."organization_id" IS '机构';
COMMENT ON TABLE "organization_model_uses" IS '机构模型的用途表';
        ALTER TABLE "organization_models" DROP COLUMN "default_way";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "organization_models" ADD "default_way" VARCHAR(19) NOT NULL;
        DROP TABLE IF EXISTS "organization_model_uses";"""
