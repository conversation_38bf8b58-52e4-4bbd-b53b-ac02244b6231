from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" ADD "ai_trace_report" TEXT;
        ALTER TABLE "project_configs" ADD "hallucination_generated_time" TIMESTAMPTZ;
        ALTER TABLE "project_configs" ADD "ai_trace_generated_time" TIMESTAMPTZ;
        ALTER TABLE "project_configs" ADD "hallucination_report" TEXT;
        ALTER TABLE "workflows" ADD "content_remark" VARCHAR(100);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "workflows" DROP COLUMN "content_remark";
        ALTER TABLE "project_configs" DROP COLUMN "ai_trace_report";
        ALTER TABLE "project_configs" DROP COLUMN "hallucination_generated_time";
        ALTER TABLE "project_configs" DROP COLUMN "ai_trace_generated_time";
        ALTER TABLE "project_configs" DROP COLUMN "hallucination_report";"""
