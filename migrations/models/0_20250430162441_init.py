from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);
CREATE TABLE IF NOT EXISTS "api_keys" (
    "id" UUID NOT NULL PRIMARY KEY,
    "key" VARCHAR(100) NOT NULL UNIQUE,
    "name" VARCHAR(100) NOT NULL,
    "quota" INT NOT NULL DEFAULT 0,
    "used" INT NOT NULL DEFAULT 0,
    "is_active" BOOL NOT NULL DEFAULT True,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "api_keys"."key" IS '实际的API Key (形如 sk-XXXX)';
COMMENT ON COLUMN "api_keys"."name" IS 'Key的描述说明';
COMMENT ON COLUMN "api_keys"."quota" IS '调用次数上限，0表示无限制';
COMMENT ON COLUMN "api_keys"."used" IS '当前已使用的次数';
COMMENT ON COLUMN "api_keys"."is_active" IS 'Key是否有效';
COMMENT ON COLUMN "api_keys"."created_at" IS '创建时间';
COMMENT ON TABLE "api_keys" IS 'API密钥模型，用于用户访问权限控制';
CREATE TABLE IF NOT EXISTS "area" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "parent_id" INT,
    "name" VARCHAR(100) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "area"."name" IS '名称';
COMMENT ON COLUMN "area"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "area"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "area"."updated_at" IS '更新时间';
COMMENT ON COLUMN "area"."created_at" IS '创建时间';
COMMENT ON TABLE "area" IS '省市区表';
CREATE TABLE IF NOT EXISTS "dictionary" (
    "id" UUID NOT NULL PRIMARY KEY,
    "value" VARCHAR(50) NOT NULL,
    "remark" VARCHAR(300),
    "label" VARCHAR(50) NOT NULL,
    "category" VARCHAR(50) NOT NULL,
    "category_value" VARCHAR(50) NOT NULL,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "dictionary"."value" IS '字典值';
COMMENT ON COLUMN "dictionary"."remark" IS '附件信息';
COMMENT ON COLUMN "dictionary"."label" IS '展示标签';
COMMENT ON COLUMN "dictionary"."category" IS '分类标签';
COMMENT ON COLUMN "dictionary"."category_value" IS '分类值';
COMMENT ON COLUMN "dictionary"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "dictionary"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "dictionary"."updated_at" IS '更新时间';
COMMENT ON COLUMN "dictionary"."created_at" IS '创建时间';
COMMENT ON TABLE "dictionary" IS '字典表';
CREATE TABLE IF NOT EXISTS "project_leaders" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(255) NOT NULL,
    "credit_code" VARCHAR(25) NOT NULL,
    "institution_type" VARCHAR(20) NOT NULL,
    "address" VARCHAR(200),
    "website" VARCHAR(255),
    "ai_introduction" VARCHAR(500),
    "founded_date" DATE NOT NULL,
    "related_projects" VARCHAR(500) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" INT NOT NULL DEFAULT 0,
    "deleted_at" TIMESTAMPTZ,
    "city_id" INT REFERENCES "area" ("id") ON DELETE CASCADE,
    "district_id" INT REFERENCES "area" ("id") ON DELETE CASCADE,
    "province_id" INT REFERENCES "area" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "project_leaders"."name" IS '主体名称';
COMMENT ON COLUMN "project_leaders"."credit_code" IS '统一社会信用代码';
COMMENT ON COLUMN "project_leaders"."institution_type" IS '机构性质';
COMMENT ON COLUMN "project_leaders"."address" IS '详细地址';
COMMENT ON COLUMN "project_leaders"."website" IS '机构网站';
COMMENT ON COLUMN "project_leaders"."ai_introduction" IS '公司的AI详细介绍';
COMMENT ON COLUMN "project_leaders"."founded_date" IS '成立时间';
COMMENT ON COLUMN "project_leaders"."related_projects" IS '主要业务及业绩';
COMMENT ON COLUMN "project_leaders"."created_at" IS '创建时间';
COMMENT ON COLUMN "project_leaders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "project_leaders"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "project_leaders"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "project_leaders"."city_id" IS '关联省市区';
COMMENT ON COLUMN "project_leaders"."district_id" IS '关联省市区';
COMMENT ON COLUMN "project_leaders"."province_id" IS '关联省市区';
COMMENT ON TABLE "project_leaders" IS '项目主体模型';
CREATE TABLE IF NOT EXISTS "project_members" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "title" VARCHAR(100) NOT NULL,
    "organization" VARCHAR(255),
    "introduction" TEXT,
    "education" VARCHAR(255),
    "representative_works" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" INT NOT NULL DEFAULT 0,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "project_members"."name" IS '姓名';
COMMENT ON COLUMN "project_members"."title" IS '职称';
COMMENT ON COLUMN "project_members"."organization" IS '所属结构';
COMMENT ON COLUMN "project_members"."introduction" IS '简介';
COMMENT ON COLUMN "project_members"."education" IS '学历';
COMMENT ON COLUMN "project_members"."representative_works" IS '代表作';
COMMENT ON COLUMN "project_members"."created_at" IS '创建时间';
COMMENT ON COLUMN "project_members"."updated_at" IS '更新时间';
COMMENT ON COLUMN "project_members"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "project_members"."deleted_at" IS '删除时间';
COMMENT ON TABLE "project_members" IS '项目成员模型';
CREATE TABLE IF NOT EXISTS "project_member_joins" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "join_id" VARCHAR(100) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" INT NOT NULL DEFAULT 0,
    "deleted_at" TIMESTAMPTZ,
    "member_id" UUID NOT NULL REFERENCES "project_members" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "project_member_joins"."join_id" IS '成员关联ID';
COMMENT ON COLUMN "project_member_joins"."created_at" IS '创建时间';
COMMENT ON COLUMN "project_member_joins"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "project_member_joins"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "project_member_joins"."member_id" IS '关联的项目成员';
COMMENT ON TABLE "project_member_joins" IS '项目成员关联模型';
CREATE TABLE IF NOT EXISTS "reports" (
    "id" UUID NOT NULL PRIMARY KEY,
    "title" VARCHAR(255) NOT NULL,
    "outline" TEXT NOT NULL,
    "fund_type" VARCHAR(4) NOT NULL DEFAULT 'nsfc',
    "content_md" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "api_key_id" UUID NOT NULL REFERENCES "api_keys" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "reports"."title" IS '研究课题名称';
COMMENT ON COLUMN "reports"."outline" IS '研究课题的大纲与要求';
COMMENT ON COLUMN "reports"."fund_type" IS '项目类型';
COMMENT ON COLUMN "reports"."content_md" IS '最终生成的研究报告内容(Markdown格式)';
COMMENT ON COLUMN "reports"."created_at" IS '创建时间';
COMMENT ON COLUMN "reports"."status" IS '报告状态';
COMMENT ON COLUMN "reports"."api_key_id" IS '对应使用该Key的用户';
COMMENT ON TABLE "reports" IS '研究报告模型';
CREATE TABLE IF NOT EXISTS "researches" (
    "id" UUID NOT NULL PRIMARY KEY,
    "query" TEXT NOT NULL,
    "search_queries" JSONB NOT NULL,
    "contexts" JSONB NOT NULL,
    "report_content" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "iterations" INT NOT NULL DEFAULT 0,
    "status" VARCHAR(9) NOT NULL DEFAULT 'pending',
    "api_key_id" UUID NOT NULL REFERENCES "api_keys" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "researches"."query" IS '用户研究查询';
COMMENT ON COLUMN "researches"."search_queries" IS '搜索查询列表';
COMMENT ON COLUMN "researches"."contexts" IS '收集的上下文信息';
COMMENT ON COLUMN "researches"."report_content" IS '最终生成的研究报告内容';
COMMENT ON COLUMN "researches"."created_at" IS '创建时间';
COMMENT ON COLUMN "researches"."updated_at" IS '更新时间';
COMMENT ON COLUMN "researches"."iterations" IS '已完成的迭代次数';
COMMENT ON COLUMN "researches"."status" IS '研究状态';
COMMENT ON COLUMN "researches"."api_key_id" IS '对应使用该Key的用户';
COMMENT ON TABLE "researches" IS '深度研究模型';
CREATE TABLE IF NOT EXISTS "research_resources" (
    "id" UUID NOT NULL PRIMARY KEY,
    "url" VARCHAR(2048) NOT NULL,
    "title" VARCHAR(512),
    "content" TEXT,
    "extracted_context" TEXT,
    "search_query" VARCHAR(512) NOT NULL,
    "is_useful" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "research_id" UUID NOT NULL REFERENCES "researches" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "research_resources"."url" IS '资源URL';
COMMENT ON COLUMN "research_resources"."title" IS '资源标题';
COMMENT ON COLUMN "research_resources"."content" IS '资源原始内容';
COMMENT ON COLUMN "research_resources"."extracted_context" IS '提取的相关上下文';
COMMENT ON COLUMN "research_resources"."search_query" IS '用于发现此资源的搜索查询';
COMMENT ON COLUMN "research_resources"."is_useful" IS '资源是否有用';
COMMENT ON COLUMN "research_resources"."created_at" IS '创建时间';
COMMENT ON COLUMN "research_resources"."research_id" IS '关联的研究';
COMMENT ON TABLE "research_resources" IS '研究资源模型，存储搜索到的资源';
CREATE TABLE IF NOT EXISTS "users" (
    "id" UUID NOT NULL PRIMARY KEY,
    "username" VARCHAR(50),
    "hashed_password" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "company" VARCHAR(100),
    "achievement" TEXT,
    "position" VARCHAR(100),
    "realname" VARCHAR(50),
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False
);
COMMENT ON COLUMN "users"."username" IS '管理员用户名';
COMMENT ON COLUMN "users"."hashed_password" IS '加密存储的密码';
COMMENT ON COLUMN "users"."created_at" IS '创建时间';
COMMENT ON COLUMN "users"."company" IS '公司名称';
COMMENT ON COLUMN "users"."achievement" IS '成就';
COMMENT ON COLUMN "users"."position" IS '职位';
COMMENT ON COLUMN "users"."realname" IS '真实姓名';
COMMENT ON COLUMN "users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "users"."is_deleted" IS '是否删除';
COMMENT ON TABLE "users" IS '管理员用户模型，仅用于后台管理';
CREATE TABLE IF NOT EXISTS "model_configs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "model_name" VARCHAR(100) NOT NULL,
    "name" VARCHAR(255),
    "api_key" VARCHAR(255) NOT NULL,
    "api_url" TEXT NOT NULL,
    "max_context" INT NOT NULL DEFAULT 0,
    "max_output" INT NOT NULL DEFAULT 0,
    "description" TEXT,
    "is_active" BOOL NOT NULL DEFAULT True,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "model_configs"."model_name" IS '模型名称（英文标识）';
COMMENT ON COLUMN "model_configs"."name" IS '模型显示名称';
COMMENT ON COLUMN "model_configs"."api_key" IS '调用模型所需的 API 密钥';
COMMENT ON COLUMN "model_configs"."api_url" IS 'API 链接';
COMMENT ON COLUMN "model_configs"."max_context" IS '模型支持的最大上下文长度';
COMMENT ON COLUMN "model_configs"."max_output" IS '模型的最大输出上限';
COMMENT ON COLUMN "model_configs"."description" IS '模型的描述或用途';
COMMENT ON COLUMN "model_configs"."is_active" IS '是否启用模型';
COMMENT ON COLUMN "model_configs"."created_at" IS '记录创建时间';
COMMENT ON COLUMN "model_configs"."updated_at" IS '记录更新时间';
COMMENT ON COLUMN "model_configs"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "model_configs"."user_id" IS '关联的用户ID';
COMMENT ON TABLE "model_configs" IS '模型配置表，用于存储模型相关的配置信息';
CREATE TABLE IF NOT EXISTS "project_configs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(50),
    "application_category" VARCHAR(50),
    "team_members" VARCHAR(100),
    "team_introduction" TEXT,
    "word_count_requirement" INT,
    "literature_library" TEXT,
    "requirements_attachments" JSONB,
    "language_style" VARCHAR(100),
    "ai_generated_outline" TEXT,
    "outline_generated_time" TIMESTAMPTZ,
    "manual_modified_outline" TEXT,
    "manual_modified_outline_time" TIMESTAMPTZ,
    "ai_generated_report" TEXT,
    "report_generation_time" TIMESTAMPTZ,
    "manual_modified_report" TEXT,
    "manual_modified_report_time" TIMESTAMPTZ,
    "outline_tokens_consumed" INT DEFAULT 0,
    "report_tokens_consumed" INT DEFAULT 0,
    "status" VARCHAR(20) NOT NULL DEFAULT 'CONFIGURING',
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" INT NOT NULL DEFAULT 0,
    "deleted_at" TIMESTAMPTZ,
    "leader_id" UUID REFERENCES "project_leaders" ("id") ON DELETE CASCADE,
    "model_id" UUID REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "project_configs"."name" IS '项目名称';
COMMENT ON COLUMN "project_configs"."application_category" IS '申报口径';
COMMENT ON COLUMN "project_configs"."team_members" IS '团队成员关联ID';
COMMENT ON COLUMN "project_configs"."team_introduction" IS '团队介绍';
COMMENT ON COLUMN "project_configs"."word_count_requirement" IS '字数要求';
COMMENT ON COLUMN "project_configs"."literature_library" IS '文献库';
COMMENT ON COLUMN "project_configs"."requirements_attachments" IS '申报要求附件';
COMMENT ON COLUMN "project_configs"."language_style" IS '语言风格';
COMMENT ON COLUMN "project_configs"."ai_generated_outline" IS 'AI生成的大纲';
COMMENT ON COLUMN "project_configs"."outline_generated_time" IS '大纲生成时间';
COMMENT ON COLUMN "project_configs"."manual_modified_outline" IS '人工修改的大纲';
COMMENT ON COLUMN "project_configs"."manual_modified_outline_time" IS '人工修改大纲的时间';
COMMENT ON COLUMN "project_configs"."ai_generated_report" IS 'AI生成的报告';
COMMENT ON COLUMN "project_configs"."report_generation_time" IS '报告的生成时间';
COMMENT ON COLUMN "project_configs"."manual_modified_report" IS '人工修改的报告';
COMMENT ON COLUMN "project_configs"."manual_modified_report_time" IS '人工修改报告的时间';
COMMENT ON COLUMN "project_configs"."outline_tokens_consumed" IS '大纲生成消耗的tokens数量';
COMMENT ON COLUMN "project_configs"."report_tokens_consumed" IS '报告生成消耗的tokens数量';
COMMENT ON COLUMN "project_configs"."status" IS '项目状态 (\n        CONFIGURING,\n        OUTLINE_GENERATING,\n        OUTLINE_GENERATED,\n        OUTLINE_FAILED,\n        REPORT_GENERATING,\n        REPORT_GENERATED,\n        REPORT_FAILED,\n        )';
COMMENT ON COLUMN "project_configs"."created_at" IS '创建时间';
COMMENT ON COLUMN "project_configs"."updated_at" IS '更新时间';
COMMENT ON COLUMN "project_configs"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "project_configs"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "project_configs"."leader_id" IS '申报主体';
COMMENT ON COLUMN "project_configs"."model_id" IS '关联的模型配置';
COMMENT ON COLUMN "project_configs"."user_id" IS '创建用户';
COMMENT ON TABLE "project_configs" IS '项目配置模型';
CREATE TABLE IF NOT EXISTS "literature_librarys" (
    "id" UUID NOT NULL PRIMARY KEY,
    "query" TEXT NOT NULL,
    "search_queries" JSONB NOT NULL,
    "contexts" JSONB NOT NULL,
    "report_content" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "iterations" INT NOT NULL DEFAULT 0,
    "status" VARCHAR(9) NOT NULL DEFAULT 'pending',
    "project_config_id" UUID REFERENCES "project_configs" ("id") ON DELETE CASCADE,
    "user_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "literature_librarys"."query" IS '文献查询内容';
COMMENT ON COLUMN "literature_librarys"."search_queries" IS '搜索查询列表';
COMMENT ON COLUMN "literature_librarys"."contexts" IS '收集的上下文信息';
COMMENT ON COLUMN "literature_librarys"."report_content" IS '最终生成的文献报告内容';
COMMENT ON COLUMN "literature_librarys"."created_at" IS '创建时间';
COMMENT ON COLUMN "literature_librarys"."updated_at" IS '更新时间';
COMMENT ON COLUMN "literature_librarys"."iterations" IS '已完成的迭代次数';
COMMENT ON COLUMN "literature_librarys"."status" IS '文献库状态';
COMMENT ON COLUMN "literature_librarys"."project_config_id" IS '关联的项目配置';
COMMENT ON COLUMN "literature_librarys"."user_id" IS '关联用户';
COMMENT ON TABLE "literature_librarys" IS '文献库模型';
CREATE TABLE IF NOT EXISTS "literature_library_resources" (
    "id" UUID NOT NULL PRIMARY KEY,
    "url" VARCHAR(2048) NOT NULL,
    "title" VARCHAR(512),
    "content" TEXT,
    "extracted_context" TEXT,
    "search_query" VARCHAR(512) NOT NULL,
    "is_useful" BOOL NOT NULL DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "literature_library_id" UUID NOT NULL REFERENCES "literature_librarys" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "literature_library_resources"."url" IS '资源URL';
COMMENT ON COLUMN "literature_library_resources"."title" IS '资源标题';
COMMENT ON COLUMN "literature_library_resources"."content" IS '资源原始内容';
COMMENT ON COLUMN "literature_library_resources"."extracted_context" IS '提取的相关上下文';
COMMENT ON COLUMN "literature_library_resources"."search_query" IS '用于发现此资源的搜索查询';
COMMENT ON COLUMN "literature_library_resources"."is_useful" IS '资源是否有用';
COMMENT ON COLUMN "literature_library_resources"."created_at" IS '创建时间';
COMMENT ON COLUMN "literature_library_resources"."literature_library_id" IS '关联的文献库';
COMMENT ON TABLE "literature_library_resources" IS '文献库资源模型，存储搜索到的资源';
CREATE TABLE IF NOT EXISTS "project_model_configs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "model_config_id" UUID NOT NULL REFERENCES "model_configs" ("id") ON DELETE CASCADE,
    "project_config_id" UUID NOT NULL REFERENCES "project_configs" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_project_mod_project_17395b" UNIQUE ("project_config_id", "model_config_id")
);
COMMENT ON COLUMN "project_model_configs"."created_at" IS '创建时间';
COMMENT ON COLUMN "project_model_configs"."updated_at" IS '更新时间';
COMMENT ON COLUMN "project_model_configs"."model_config_id" IS '关联的模型配置';
COMMENT ON COLUMN "project_model_configs"."project_config_id" IS '关联的项目配置';
COMMENT ON TABLE "project_model_configs" IS '项目模型配置关联表，用于关联项目配置和模型配置';
CREATE TABLE IF NOT EXISTS "requirements_attachments_files" (
    "id" UUID NOT NULL PRIMARY KEY,
    "file_path" VARCHAR(255) NOT NULL,
    "file_name" VARCHAR(255) NOT NULL,
    "file_content" TEXT,
    "word_count" INT NOT NULL DEFAULT 0,
    "analysis_result" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "project_configs_id" UUID REFERENCES "project_configs" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "requirements_attachments_files"."file_path" IS '文件路径';
COMMENT ON COLUMN "requirements_attachments_files"."file_name" IS '文件名称';
COMMENT ON COLUMN "requirements_attachments_files"."file_content" IS '文件内容';
COMMENT ON COLUMN "requirements_attachments_files"."word_count" IS '文件字数';
COMMENT ON COLUMN "requirements_attachments_files"."analysis_result" IS 'LLM分析结果';
COMMENT ON COLUMN "requirements_attachments_files"."created_at" IS '创建时间';
COMMENT ON COLUMN "requirements_attachments_files"."project_configs_id" IS '关联的项目配置';
COMMENT ON TABLE "requirements_attachments_files" IS '申报要求附件文件模型';
CREATE TABLE IF NOT EXISTS "user_report_usages" (
    "id" UUID NOT NULL PRIMARY KEY,
    "used_count" INT NOT NULL DEFAULT 0,
    "max_allowed_count" INT,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "user_id_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "user_report_usages"."used_count" IS '已使用生成报告次数';
COMMENT ON COLUMN "user_report_usages"."max_allowed_count" IS '最大允许生成报告次数';
COMMENT ON COLUMN "user_report_usages"."created_at" IS '创建时间';
COMMENT ON COLUMN "user_report_usages"."updated_at" IS '更新时间';
COMMENT ON COLUMN "user_report_usages"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "user_report_usages"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "user_report_usages"."user_id_id" IS '关联用户ID';
COMMENT ON TABLE "user_report_usages" IS '用户报告生成使用次数模型';
CREATE TABLE IF NOT EXISTS "workflows" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" INT NOT NULL DEFAULT 0,
    "content" VARCHAR(100),
    "category" VARCHAR(7) NOT NULL DEFAULT 'OTHER',
    "deleted_at" TIMESTAMPTZ,
    "order" INT NOT NULL DEFAULT 0,
    "operator_id" UUID NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
    "project_config_id" UUID NOT NULL REFERENCES "project_configs" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "workflows"."name" IS '流程名称';
COMMENT ON COLUMN "workflows"."created_at" IS '创建时间';
COMMENT ON COLUMN "workflows"."updated_at" IS '更新时间';
COMMENT ON COLUMN "workflows"."is_deleted" IS '是否删除 (0: 正常, 1: 删除)';
COMMENT ON COLUMN "workflows"."content" IS '流程内容';
COMMENT ON COLUMN "workflows"."category" IS '流程分类：OUTLINE, REPORT, OTHER';
COMMENT ON COLUMN "workflows"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "workflows"."order" IS '流程的顺序';
COMMENT ON COLUMN "workflows"."operator_id" IS '操作人';
COMMENT ON COLUMN "workflows"."project_config_id" IS '关联项目配置ID';
COMMENT ON TABLE "workflows" IS '工作流程模型';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
