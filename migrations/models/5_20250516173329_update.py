from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "organizations" (
    "id" UUID NOT NULL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL,
    "code" VARCHAR(50) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "is_active" BOOL NOT NULL DEFAULT True,
    "contact_person" VARCHAR(50) NOT NULL,
    "contact_phone" VARCHAR(20) NOT NULL,
    "contact_email" VARCHAR(100) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ,
    "description" TEXT,
    "remarks" TEXT
);
COMMENT ON COLUMN "organizations"."name" IS '机构名称';
COMMENT ON COLUMN "organizations"."code" IS '机构代码(唯一)';
COMMENT ON COLUMN "organizations"."type" IS '机构类型';
COMMENT ON COLUMN "organizations"."is_active" IS '是否启用';
COMMENT ON COLUMN "organizations"."contact_person" IS '联系人';
COMMENT ON COLUMN "organizations"."contact_phone" IS '联系电话';
COMMENT ON COLUMN "organizations"."contact_email" IS '电子邮箱';
COMMENT ON COLUMN "organizations"."created_at" IS '创建时间';
COMMENT ON COLUMN "organizations"."updated_at" IS '最后修改时间';
COMMENT ON COLUMN "organizations"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "organizations"."deleted_at" IS '删除时间';
COMMENT ON COLUMN "organizations"."description" IS '机构描述';
COMMENT ON COLUMN "organizations"."remarks" IS '备注';
        ALTER TABLE "users" ADD "organization_id" UUID;
        ALTER TABLE "users" ADD CONSTRAINT "fk_users_organiza_bbdbc59c" FOREIGN KEY ("organization_id") REFERENCES "organizations" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "users" DROP CONSTRAINT IF EXISTS "fk_users_organiza_bbdbc59c";
        ALTER TABLE "users" DROP COLUMN "organization_id";
        DROP TABLE IF EXISTS "organizations";"""
