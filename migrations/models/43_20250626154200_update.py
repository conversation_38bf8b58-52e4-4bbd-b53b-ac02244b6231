from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" ADD "user_add_demo_id" UUID;
        ALTER TABLE "project_configs" ADD "user_add_prompt" TEXT;
        CREATE TABLE IF NOT EXISTS "upload_files" (
    "id" UUID NOT NULL PRIMARY KEY,
    "file_path" VARCHAR(255) NOT NULL,
    "file_name" VARCHAR(255) NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_deleted" BOOL NOT NULL DEFAULT False,
    "deleted_at" TIMESTAMPTZ
);
COMMENT ON COLUMN "project_configs"."user_add_demo_id" IS '关联文件上传表';
COMMENT ON COLUMN "project_configs"."user_add_prompt" IS '用户增加的提示词';
COMMENT ON COLUMN "upload_files"."file_path" IS '文件路径';
COMMENT ON COLUMN "upload_files"."file_name" IS '文件名称';
COMMENT ON COLUMN "upload_files"."created_at" IS '创建时间';
COMMENT ON COLUMN "upload_files"."updated_at" IS '更新时间';
COMMENT ON COLUMN "upload_files"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "upload_files"."deleted_at" IS '删除时间';
COMMENT ON TABLE "upload_files" IS '上传的文件';
        ALTER TABLE "project_configs" ADD CONSTRAINT "fk_project__upload_f_9f130bab" FOREIGN KEY ("user_add_demo_id") REFERENCES "upload_files" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "project_configs" DROP CONSTRAINT IF EXISTS "fk_project__upload_f_9f130bab";
        ALTER TABLE "project_configs" DROP COLUMN "user_add_demo_id";
        ALTER TABLE "project_configs" DROP COLUMN "user_add_prompt";
        DROP TABLE IF EXISTS "upload_files";"""
