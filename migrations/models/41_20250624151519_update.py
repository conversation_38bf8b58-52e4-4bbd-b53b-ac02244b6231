from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "docgen_project_url_summary" ALTER COLUMN "is_valid" DROP DEFAULT;
        COMMENT ON COLUMN "docgen_project_url_summary"."is_valid" IS '是否有效，如果为None说明还没核验';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "docgen_project_url_summary" ALTER COLUMN "is_valid" SET DEFAULT False;
        COMMENT ON COLUMN "docgen_project_url_summary"."is_valid" IS '是否有效';"""
