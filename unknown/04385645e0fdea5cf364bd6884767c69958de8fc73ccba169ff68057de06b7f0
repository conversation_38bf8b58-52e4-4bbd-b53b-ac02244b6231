from typing import Optional
from uuid import UUID
from pydantic import BaseModel


class ProjectModelConfigBase(BaseModel):
    """项目模型配置关联基础模型"""
    project_config_id: UUID
    model_config_id: UUID


class ProjectModelConfigCreate(ProjectModelConfigBase):
    """创建项目模型配置关联模型"""
    pass


class ProjectModelConfigResponse(ProjectModelConfigBase):
    """项目模型配置关联响应模型"""
    id: UUID
    
    class Config:
        from_attributes = True 